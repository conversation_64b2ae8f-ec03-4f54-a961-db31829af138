#pragma once
#include <string>
#include <memory>
#include <functional>
#include <unordered_map>
#include <vector>
#include <thread>
#include <atomic>
#include <nlohmann/json.hpp>
#include <expected>
#include <span>
#include <format>
#include <regex>
#include <queue>
#include <condition_variable>
#include <future>
#include <mutex>
#include "database-service/api/api_types.hpp"
#include "database-service/utils/thread_pool.hpp"

namespace dbservice {

class DatabaseService;

namespace core {
    class ConnectionManager;
}

namespace security {
    class SecurityManager;
}

namespace api {

/**
 * @struct SSLConfig
 * @brief SSL configuration for the API server
 */
struct SSLConfig {
    bool enabled = false;
    std::string certPath;
    std::string keyPath;
};

/**
 * @struct CorsConfig
 * @brief CORS configuration for the API server
 */
struct CorsConfig {
    bool enabled = false;
    std::vector<std::string> allowedOrigins;
    std::vector<std::string> allowedMethods;
    std::vector<std::string> allowedHeaders;
    bool allowCredentials = false;
    int maxAge = 86400; // 24 hours
};

/**
 * @class ApiServer
 * @brief HTTP API server for the database service
 */
class ApiServer {
public:
    /**
     * @brief Constructor
     * @param port Port to listen on
     * @param connectionManager Connection manager
     * @param securityManager Security manager
     * @param databaseService Database service
     */
    ApiServer(unsigned short port,
              std::shared_ptr<core::ConnectionManager> connectionManager,
              std::shared_ptr<security::SecurityManager> securityManager,
              std::shared_ptr<DatabaseService> databaseService);

    /**
     * @brief Destructor
     */
    ~ApiServer();

    /**
     * @brief Start the API server
     * @return Success or error message
     */
    std::expected<void, ApiError> start();

    /**
     * @brief Stop the API server
     * @return Success or error message
     */
    std::expected<void, ApiError> stop();

    /**
     * @brief Check if the server is running
     * @return True if the server is running
     */
    bool isRunning() const;

    /**
     * @brief Get the port the server is listening on
     * @return Port
     */
    unsigned short getPort() const;

    /**
     * @brief Configure CORS settings
     * @param config CORS configuration
     */
    void configureCors(const CorsConfig& config);

    /**
     * @brief Configure SSL settings
     * @param config SSL configuration
     */
        void configureSSL(const SSLConfig& config);

    /**
     * @brief Add a route to the API server
     * @param method HTTP method (GET, POST, etc.)
     * @param path Path pattern (e.g., /users/{id})
     * @param handler The handler function
     */
    void addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler);

    /**
     * @brief Get thread pool statistics
     * @return JSON object with thread pool metrics
     */
    nlohmann::json getThreadPoolStats() const;

private:
    /**
     * @brief Handle a request
     * @param request Parsed request
     * @return Response with headers and body or error message
     */


        std::expected<Response, std::string> handleRequest(ParsedRequest& request);

    /**
     * @brief Apply CORS headers to a response
     * @param method Request method
     * @param headers Request headers
     * @param responseHeaders Response headers to modify
     */
    void applyCorsHeaders(
        std::string_view method,
        const std::unordered_map<std::string, std::string>& headers,
        std::unordered_map<std::string, std::string>& responseHeaders);

    /**
     * @brief Run the server loop
     */
    void run();

    /**
     * @brief Handle client connection in thread pool
     * @param clientSocket Client socket descriptor
     * @param clientAddress Client address information
     */
    void handleConnection(int clientSocket, struct sockaddr_in clientAddress);

    /**
     * @brief Initialize route handlers
     */
    void initializeRoutes();

    /**
     * @brief Load CORS configuration from config manager
     */
    void loadCorsConfig();

    /**
     * @brief Parse an HTTP request
     * @param request Raw HTTP request
     * @return Parsed request or error message
     */


    std::expected<ParsedRequest, std::string> parseRequest(const std::string& request);

    /**
     * @brief Create a success response
     * @param data Response data
     * @return Response with default headers and body
     */
    Response createSuccessResponse(const nlohmann::json& data);

    /**
     * @brief Create a success response with headers
     * @param data Response data
     * @return Response with headers and body
     */
    Response createSuccessResponseWithHeaders(const nlohmann::json& data);

    /**
     * @brief Create an error response
     * @param statusCode HTTP status code
     * @param statusText HTTP status text
     * @param message Error message
     * @return Response with headers and body
     */
    Response createErrorResponse(
        int statusCode, const std::string& statusText, const std::string& message);

    /**
     * @brief Parse query parameters from a query string
     * @param queryString Query string to parse
     * @return Map of query parameters
     */
    std::unordered_map<std::string, std::string> parseQueryParams(std::string_view queryString);

    /**
     * @brief URL decode a string
     * @param input String to decode
     * @return Decoded string
     */
    std::string urlDecode(std::string_view input);

    unsigned short port_;
    std::shared_ptr<core::ConnectionManager> connectionManager_;
    std::shared_ptr<security::SecurityManager> securityManager_;
    std::shared_ptr<DatabaseService> databaseService_;
    std::atomic<bool> running_;
    std::thread serverThread_;
    int serverSocket_;
    CorsConfig corsConfig_;
    SSLConfig sslConfig_;

    // Route handlers
    struct Route {
        std::regex pattern;
        std::vector<std::string> param_names;
        RouteHandler handler;
    };

    std::unordered_map<std::string, std::vector<Route>> routes_;

    // Thread pool for handling connections
    std::unique_ptr<utils::ThreadPool> threadPool_;
};

} // namespace api
} // namespace dbservice
