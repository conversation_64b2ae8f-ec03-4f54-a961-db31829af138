Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:22:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   22 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[ 16%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 22%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
[ 27%] Building CXX object CMakeFiles/database-service.dir/src/core/transaction.cpp.o
[ 33%] Building CXX object CMakeFiles/database-service.dir/src/database_service.cpp.o
[ 38%] Building CXX object CMakeFiles/database-service.dir/src/main.cpp.o
[ 44%] Building CXX object CMakeFiles/database-service.dir/src/metrics/database_metrics.cpp.o
[ 50%] Building CXX object CMakeFiles/database-service.dir/src/metrics/metrics_collector.cpp.o
[ 55%] Building CXX object CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o
[ 61%] Building CXX object CMakeFiles/database-service.dir/src/security/credential_store.cpp.o
/home/<USER>/database-service-build/src/schema/schema_manager.cpp: In member function ‘std::expected<void, dbservice::schema::SchemaError> dbservice::schema::SchemaManager::createSchema(const std::string&)’:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:83:34: error: invalid use of incomplete type ‘class dbservice::core::Connection’
   83 |         int result = managed_conn->executeNonQuery(query, {});
      |                                  ^~
In file included from /home/<USER>/database-service-build/src/schema/schema_manager.cpp:11:
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:93:20: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
   93 |     } catch (const pqxx::sql_error& e) {
      |                    ^~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:93:24: error: expected ‘)’ before ‘::’ token
   93 |     } catch (const pqxx::sql_error& e) {
      |             ~          ^~
      |                        )
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:93:24: error: expected ‘{’ before ‘::’ token
   93 |     } catch (const pqxx::sql_error& e) {
      |                        ^~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:93:26: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
   93 |     } catch (const pqxx::sql_error& e) {
      |                          ^~~~~~~~~
      |                          strerror
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:93:37: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
   93 |     } catch (const pqxx::sql_error& e) {
      |                                     ^
      |                                     std::numbers::e
In file included from /usr/include/c++/14/bits/max_size_type.h:37,
                 from /usr/include/c++/14/bits/ranges_base.h:39,
                 from /usr/include/c++/14/string_view:56,
                 from /usr/include/c++/14/bits/basic_string.h:47,
                 from /usr/include/c++/14/string:54,
                 from /usr/include/c++/14/bits/locale_classes.h:40,
                 from /usr/include/c++/14/bits/ios_base.h:41,
                 from /usr/include/c++/14/ios:44,
                 from /usr/include/c++/14/istream:40,
                 from /usr/include/c++/14/fstream:40,
                 from /home/<USER>/database-service-build/src/schema/schema_manager.cpp:2:
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:97:7: error: expected primary-expression before ‘catch’
   97 |     } catch (const std::exception& e) {
      |       ^~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp: In member function ‘std::expected<void, dbservice::schema::SchemaError> dbservice::schema::SchemaManager::dropSchema(const std::string&)’:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:130:34: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  130 |         int result = managed_conn->executeNonQuery(query, {});
      |                                  ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:140:20: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
  140 |     } catch (const pqxx::sql_error& e) {
      |                    ^~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:140:24: error: expected ‘)’ before ‘::’ token
  140 |     } catch (const pqxx::sql_error& e) {
      |             ~          ^~
      |                        )
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:140:24: error: expected ‘{’ before ‘::’ token
  140 |     } catch (const pqxx::sql_error& e) {
      |                        ^~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:140:26: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
  140 |     } catch (const pqxx::sql_error& e) {
      |                          ^~~~~~~~~
      |                          strerror
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:140:37: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
  140 |     } catch (const pqxx::sql_error& e) {
      |                                     ^
      |                                     std::numbers::e
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:144:7: error: expected primary-expression before ‘catch’
  144 |     } catch (const std::exception& e) {
      |       ^~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp: In member function ‘std::expected<bool, dbservice::schema::SchemaError> dbservice::schema::SchemaManager::schemaExists(const std::string&)’:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:169:40: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  169 |         auto result_rows = managed_conn->executeQuery(query, {schemaName});
      |                                        ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:171:20: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
  171 |     } catch (const pqxx::sql_error& e) {
      |                    ^~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:171:24: error: expected ‘)’ before ‘::’ token
  171 |     } catch (const pqxx::sql_error& e) {
      |             ~          ^~
      |                        )
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:171:24: error: expected ‘{’ before ‘::’ token
  171 |     } catch (const pqxx::sql_error& e) {
      |                        ^~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:171:26: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
  171 |     } catch (const pqxx::sql_error& e) {
      |                          ^~~~~~~~~
      |                          strerror
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:171:37: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
  171 |     } catch (const pqxx::sql_error& e) {
      |                                     ^
      |                                     std::numbers::e
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:175:7: error: expected primary-expression before ‘catch’
  175 |     } catch (const std::exception& e) {
      |       ^~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp: In member function ‘std::expected<std::vector<std::__cxx11::basic_string<char> >, dbservice::schema::SchemaError> dbservice::schema::SchemaManager::getSchemas()’:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:198:40: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  198 |         auto result_rows = managed_conn->executeQuery(query, {});
      |                                        ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:206:20: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
  206 |     } catch (const pqxx::sql_error& e) {
      |                    ^~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:206:24: error: expected ‘)’ before ‘::’ token
  206 |     } catch (const pqxx::sql_error& e) {
      |             ~          ^~
      |                        )
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:206:24: error: expected ‘{’ before ‘::’ token
  206 |     } catch (const pqxx::sql_error& e) {
      |                        ^~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:206:26: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
  206 |     } catch (const pqxx::sql_error& e) {
      |                          ^~~~~~~~~
      |                          strerror
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:206:37: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
  206 |     } catch (const pqxx::sql_error& e) {
      |                                     ^
      |                                     std::numbers::e
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:210:7: error: expected primary-expression before ‘catch’
  210 |     } catch (const std::exception& e) {
      |       ^~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp: In member function ‘std::expected<void, dbservice::schema::SchemaError> dbservice::schema::SchemaManager::applyMigration(const std::string&, const std::string&)’:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:242:60: error: no match for ‘operator=’ (operand types are ‘std::shared_ptr<dbservice::core::Transaction>’ and ‘std::expected<std::shared_ptr<dbservice::core::Transaction>, std::__cxx11::basic_string<char> >’)
  242 |         transaction = connectionManager_->beginTransaction(); // This might need to return std::expected too.
      |                                                            ^
In file included from /usr/include/c++/14/bits/fs_path.h:46,
                 from /usr/include/c++/14/filesystem:52,
                 from /home/<USER>/database-service-build/include/database-service/utils/filesystem_wrapper.hpp:4,
                 from /home/<USER>/database-service-build/src/schema/schema_manager.cpp:4:
/usr/include/c++/14/bits/shared_ptr.h:417:9: note: candidate: ‘template<class _Yp> std::shared_ptr<_Tp>::_Assignable<const std::shared_ptr<_Yp>&> std::shared_ptr<_Tp>::operator=(const std::shared_ptr<_Yp>&) [with _Tp = dbservice::core::Transaction]’
  417 |         operator=(const shared_ptr<_Yp>& __r) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:417:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:242:60: note:   ‘std::expected<std::shared_ptr<dbservice::core::Transaction>, std::__cxx11::basic_string<char> >’ is not derived from ‘const std::shared_ptr<_Tp>’
  242 |         transaction = connectionManager_->beginTransaction(); // This might need to return std::expected too.
      |                                                            ^
/usr/include/c++/14/bits/shared_ptr.h:428:9: note: candidate: ‘template<class _Yp> std::shared_ptr<_Tp>::_Assignable<std::auto_ptr<_Up> > std::shared_ptr<_Tp>::operator=(std::auto_ptr<_Up>&&) [with _Tp = dbservice::core::Transaction]’
  428 |         operator=(auto_ptr<_Yp>&& __r)
      |         ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:428:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:242:60: note:   ‘std::expected<std::shared_ptr<dbservice::core::Transaction>, std::__cxx11::basic_string<char> >’ is not derived from ‘std::auto_ptr<_Up>’
  242 |         transaction = connectionManager_->beginTransaction(); // This might need to return std::expected too.
      |                                                            ^
/usr/include/c++/14/bits/shared_ptr.h:445:9: note: candidate: ‘template<class _Yp> std::shared_ptr<_Tp>::_Assignable<std::shared_ptr<_Yp> > std::shared_ptr<_Tp>::operator=(std::shared_ptr<_Yp>&&) [with _Tp = dbservice::core::Transaction]’
  445 |         operator=(shared_ptr<_Yp>&& __r) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:445:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:242:60: note:   ‘std::expected<std::shared_ptr<dbservice::core::Transaction>, std::__cxx11::basic_string<char> >’ is not derived from ‘std::shared_ptr<_Tp>’
  242 |         transaction = connectionManager_->beginTransaction(); // This might need to return std::expected too.
      |                                                            ^
/usr/include/c++/14/bits/shared_ptr.h:453:9: note: candidate: ‘template<class _Yp, class _Del> std::shared_ptr<_Tp>::_Assignable<std::unique_ptr<_Up, _Ep> > std::shared_ptr<_Tp>::operator=(std::unique_ptr<_Up, _Ep>&&) [with _Del = _Yp; _Tp = dbservice::core::Transaction]’
  453 |         operator=(unique_ptr<_Yp, _Del>&& __r)
      |         ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:453:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:242:60: note:   ‘std::expected<std::shared_ptr<dbservice::core::Transaction>, std::__cxx11::basic_string<char> >’ is not derived from ‘std::unique_ptr<_Tp, _Dp>’
  242 |         transaction = connectionManager_->beginTransaction(); // This might need to return std::expected too.
      |                                                            ^
/usr/include/c++/14/bits/shared_ptr.h:413:19: note: candidate: ‘std::shared_ptr<_Tp>& std::shared_ptr<_Tp>::operator=(const std::shared_ptr<_Tp>&) [with _Tp = dbservice::core::Transaction]’
  413 |       shared_ptr& operator=(const shared_ptr&) noexcept = default;
      |                   ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:413:29: note:   no known conversion for argument 1 from ‘std::expected<std::shared_ptr<dbservice::core::Transaction>, std::__cxx11::basic_string<char> >’ to ‘const std::shared_ptr<dbservice::core::Transaction>&’
  413 |       shared_ptr& operator=(const shared_ptr&) noexcept = default;
      |                             ^~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:437:7: note: candidate: ‘std::shared_ptr<_Tp>& std::shared_ptr<_Tp>::operator=(std::shared_ptr<_Tp>&&) [with _Tp = dbservice::core::Transaction]’
  437 |       operator=(shared_ptr&& __r) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:437:30: note:   no known conversion for argument 1 from ‘std::expected<std::shared_ptr<dbservice::core::Transaction>, std::__cxx11::basic_string<char> >’ to ‘std::shared_ptr<dbservice::core::Transaction>&&’
  437 |       operator=(shared_ptr&& __r) noexcept
      |                 ~~~~~~~~~~~~~^~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:257:40: error: invalid use of incomplete type ‘using std::__shared_ptr_access<dbservice::core::Transaction, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Transaction’ {aka ‘class dbservice::core::Transaction’}
  257 |         int script_result = transaction->executeNonQuery(script, {});
      |                                        ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:137:41: note: forward declaration of ‘using std::__shared_ptr_access<dbservice::core::Transaction, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Transaction’ {aka ‘class dbservice::core::Transaction’}
  137 |     std::expected<std::shared_ptr<class Transaction>, std::string> beginTransaction();
      |                                         ^~~~~~~~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:261:24: error: invalid use of incomplete type ‘using std::__shared_ptr_access<dbservice::core::Transaction, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Transaction’ {aka ‘class dbservice::core::Transaction’}
  261 |             transaction->rollback(); // Attempt rollback
      |                        ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:137:41: note: forward declaration of ‘using std::__shared_ptr_access<dbservice::core::Transaction, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Transaction’ {aka ‘class dbservice::core::Transaction’}
  137 |     std::expected<std::shared_ptr<class Transaction>, std::string> beginTransaction();
      |                                         ^~~~~~~~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:266:40: error: invalid use of incomplete type ‘using std::__shared_ptr_access<dbservice::core::Transaction, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Transaction’ {aka ‘class dbservice::core::Transaction’}
  266 |         int record_result = transaction->executeNonQuery(record_query, {schemaName, migrationName});
      |                                        ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:137:41: note: forward declaration of ‘using std::__shared_ptr_access<dbservice::core::Transaction, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Transaction’ {aka ‘class dbservice::core::Transaction’}
  137 |     std::expected<std::shared_ptr<class Transaction>, std::string> beginTransaction();
      |                                         ^~~~~~~~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:270:24: error: invalid use of incomplete type ‘using std::__shared_ptr_access<dbservice::core::Transaction, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Transaction’ {aka ‘class dbservice::core::Transaction’}
  270 |             transaction->rollback(); // Attempt rollback
      |                        ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:137:41: note: forward declaration of ‘using std::__shared_ptr_access<dbservice::core::Transaction, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Transaction’ {aka ‘class dbservice::core::Transaction’}
  137 |     std::expected<std::shared_ptr<class Transaction>, std::string> beginTransaction();
      |                                         ^~~~~~~~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:274:25: error: invalid use of incomplete type ‘using std::__shared_ptr_access<dbservice::core::Transaction, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Transaction’ {aka ‘class dbservice::core::Transaction’}
  274 |         if (!transaction->commit()) {
      |                         ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:137:41: note: forward declaration of ‘using std::__shared_ptr_access<dbservice::core::Transaction, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Transaction’ {aka ‘class dbservice::core::Transaction’}
  137 |     std::expected<std::shared_ptr<class Transaction>, std::string> beginTransaction();
      |                                         ^~~~~~~~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:283:20: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
  283 |     } catch (const pqxx::sql_error& e) {
      |                    ^~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:283:24: error: expected ‘)’ before ‘::’ token
  283 |     } catch (const pqxx::sql_error& e) {
      |             ~          ^~
      |                        )
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:283:24: error: expected ‘{’ before ‘::’ token
  283 |     } catch (const pqxx::sql_error& e) {
      |                        ^~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:283:26: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
  283 |     } catch (const pqxx::sql_error& e) {
      |                          ^~~~~~~~~
      |                          strerror
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:283:37: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
  283 |     } catch (const pqxx::sql_error& e) {
      |                                     ^
      |                                     std::numbers::e
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:288:7: error: expected primary-expression before ‘catch’
  288 |     } catch (const std::exception& e) {
      |       ^~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp: In member function ‘std::expected<std::vector<std::__cxx11::basic_string<char> >, dbservice::schema::SchemaError> dbservice::schema::SchemaManager::getAppliedMigrations(const std::string&)’:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:312:40: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  312 |         auto result_rows = managed_conn->executeQuery(query, {schemaName});
      |                                        ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:320:20: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
  320 |     } catch (const pqxx::sql_error& e) {
      |                    ^~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:320:24: error: expected ‘)’ before ‘::’ token
  320 |     } catch (const pqxx::sql_error& e) {
      |             ~          ^~
      |                        )
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:320:24: error: expected ‘{’ before ‘::’ token
  320 |     } catch (const pqxx::sql_error& e) {
      |                        ^~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:320:26: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
  320 |     } catch (const pqxx::sql_error& e) {
      |                          ^~~~~~~~~
      |                          strerror
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:320:37: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
  320 |     } catch (const pqxx::sql_error& e) {
      |                                     ^
      |                                     std::numbers::e
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:324:7: error: expected primary-expression before ‘catch’
  324 |     } catch (const std::exception& e) {
      |       ^~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp: In member function ‘std::expected<void, dbservice::schema::SchemaError> dbservice::schema::SchemaManager::createMigrationsTable()’:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:355:34: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  355 |         int result = managed_conn->executeNonQuery(query, {});
      |                                  ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:365:20: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
  365 |     } catch (const pqxx::sql_error& e) {
      |                    ^~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:365:24: error: expected ‘)’ before ‘::’ token
  365 |     } catch (const pqxx::sql_error& e) {
      |             ~          ^~
      |                        )
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:365:24: error: expected ‘{’ before ‘::’ token
  365 |     } catch (const pqxx::sql_error& e) {
      |                        ^~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:365:26: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
  365 |     } catch (const pqxx::sql_error& e) {
      |                          ^~~~~~~~~
      |                          strerror
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:365:37: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
  365 |     } catch (const pqxx::sql_error& e) {
      |                                     ^
      |                                     std::numbers::e
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:369:7: error: expected primary-expression before ‘catch’
  369 |     } catch (const std::exception& e) {
      |       ^~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp: In member function ‘bool dbservice::security::CredentialStore::initialize(const std::string&)’:
/home/<USER>/database-service-build/src/security/credential_store.cpp:41:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   41 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/credential_store.cpp:6:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:42:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   42 |     SHA256_Update(&sha256, encryptionKey.c_str(), encryptionKey.length());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:43:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   43 |     SHA256_Final(key, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/schema/schema_manager.cpp: In member function ‘std::expected<void, dbservice::schema::SchemaError> dbservice::schema::SchemaManager::createSchema(const std::string&)’:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:102:1: warning: control reaches end of non-void function [-Wreturn-type]
  102 | }
      | ^
/home/<USER>/database-service-build/src/schema/schema_manager.cpp: In member function ‘std::expected<void, dbservice::schema::SchemaError> dbservice::schema::SchemaManager::dropSchema(const std::string&)’:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:149:1: warning: control reaches end of non-void function [-Wreturn-type]
  149 | }
      | ^
/home/<USER>/database-service-build/src/schema/schema_manager.cpp: In member function ‘std::expected<std::vector<std::__cxx11::basic_string<char> >, dbservice::schema::SchemaError> dbservice::schema::SchemaManager::getSchemas()’:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:215:1: warning: control reaches end of non-void function [-Wreturn-type]
  215 | }
      | ^
/home/<USER>/database-service-build/src/schema/schema_manager.cpp: In member function ‘std::expected<void, dbservice::schema::SchemaError> dbservice::schema::SchemaManager::applyMigration(const std::string&, const std::string&)’:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:294:1: warning: control reaches end of non-void function [-Wreturn-type]
  294 | }
      | ^
/home/<USER>/database-service-build/src/schema/schema_manager.cpp: In member function ‘std::expected<std::vector<std::__cxx11::basic_string<char> >, dbservice::schema::SchemaError> dbservice::schema::SchemaManager::getAppliedMigrations(const std::string&)’:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:329:1: warning: control reaches end of non-void function [-Wreturn-type]
  329 | }
      | ^
/home/<USER>/database-service-build/src/schema/schema_manager.cpp: In member function ‘std::expected<void, dbservice::schema::SchemaError> dbservice::schema::SchemaManager::createMigrationsTable()’:
/home/<USER>/database-service-build/src/schema/schema_manager.cpp:374:1: warning: control reaches end of non-void function [-Wreturn-type]
  374 | }
      | ^
make[2]: *** [CMakeFiles/database-service.dir/build.make:205: CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
