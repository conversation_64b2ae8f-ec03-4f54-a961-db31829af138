Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  5%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:22:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   22 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[ 16%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 22%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
[ 27%] Building CXX object CMakeFiles/database-service.dir/src/core/transaction.cpp.o
[ 33%] Building CXX object CMakeFiles/database-service.dir/src/database_service.cpp.o
[ 38%] Building CXX object CMakeFiles/database-service.dir/src/main.cpp.o
[ 44%] Building CXX object CMakeFiles/database-service.dir/src/metrics/database_metrics.cpp.o
[ 50%] Building CXX object CMakeFiles/database-service.dir/src/metrics/metrics_collector.cpp.o
/home/<USER>/database-service-build/src/metrics/database_metrics.cpp: In member function ‘bool dbservice::metrics::DatabaseMetrics::initialize()’:
/home/<USER>/database-service-build/src/metrics/database_metrics.cpp:50:36: error: use of deleted function ‘dbservice::metrics::DatabaseMetrics::QueryMetrics& dbservice::metrics::DatabaseMetrics::QueryMetrics::operator=(const dbservice::metrics::DatabaseMetrics::QueryMetrics&)’
   50 |         queryMetrics_[queryType] = metrics;
      |                                    ^~~~~~~
In file included from /home/<USER>/database-service-build/src/metrics/database_metrics.cpp:1:
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:115:12: note: ‘dbservice::metrics::DatabaseMetrics::QueryMetrics& dbservice::metrics::DatabaseMetrics::QueryMetrics::operator=(const dbservice::metrics::DatabaseMetrics::QueryMetrics&)’ is implicitly deleted because the default definition would be ill-formed:
  115 |     struct QueryMetrics {
      |            ^~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp: At global scope:
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:115:12: error: use of deleted function ‘std::atomic<int>& std::atomic<int>::operator=(const std::atomic<int>&)’
In file included from /home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:7:
/usr/include/c++/14/atomic:835:15: note: declared here
  835 |       atomic& operator=(const atomic&) = delete;
      |               ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:115:12: note: use ‘-fdiagnostics-all-candidates’ to display considered candidates
  115 |     struct QueryMetrics {
      |            ^~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:115:12: error: use of deleted function ‘std::atomic<double>& std::atomic<double>::operator=(const std::atomic<double>&)’
/usr/include/c++/14/atomic:1648:15: note: declared here
 1648 |       atomic& operator=(const atomic&) = delete;
      |               ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:115:12: note: use ‘-fdiagnostics-all-candidates’ to display considered candidates
  115 |     struct QueryMetrics {
      |            ^~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:115:12: error: use of deleted function ‘std::atomic<double>& std::atomic<double>::operator=(const std::atomic<double>&)’
/usr/include/c++/14/atomic:1648:15: note: declared here
 1648 |       atomic& operator=(const atomic&) = delete;
      |               ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:115:12: note: use ‘-fdiagnostics-all-candidates’ to display considered candidates
  115 |     struct QueryMetrics {
      |            ^~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:115:12: error: use of deleted function ‘std::atomic<double>& std::atomic<double>::operator=(const std::atomic<double>&)’
/usr/include/c++/14/atomic:1648:15: note: declared here
 1648 |       atomic& operator=(const atomic&) = delete;
      |               ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:115:12: note: use ‘-fdiagnostics-all-candidates’ to display considered candidates
  115 |     struct QueryMetrics {
      |            ^~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:115:12: error: use of deleted function ‘std::atomic<int>& std::atomic<int>::operator=(const std::atomic<int>&)’
/usr/include/c++/14/atomic:835:15: note: declared here
  835 |       atomic& operator=(const atomic&) = delete;
      |               ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:115:12: note: use ‘-fdiagnostics-all-candidates’ to display considered candidates
  115 |     struct QueryMetrics {
      |            ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/metrics/database_metrics.cpp: In member function ‘bool dbservice::metrics::DatabaseMetrics::initialize()’:
/home/<USER>/database-service-build/src/metrics/database_metrics.cpp:50:36: note: use ‘-fdiagnostics-all-candidates’ to display considered candidates
   50 |         queryMetrics_[queryType] = metrics;
      |                                    ^~~~~~~
make[2]: *** [CMakeFiles/database-service.dir/build.make:177: CMakeFiles/database-service.dir/src/metrics/database_metrics.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
