-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Performing Test HAVE_STD_EXPECTED
-- Performing Test HAVE_STD_EXPECTED - Success
-- Performing Test HAVE_STD_FORMAT
-- Performing Test HAVE_STD_FORMAT - Success
-- Performing Test HAVE_STD_PRINT
-- Performing Test HAVE_STD_PRINT - Failed
CMake Warning at CMakeLists.txt:85 (message):
  std::print is not available.  Will fall back to iostream.


-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- 
-- C++23 Features:
--   std::expected: 1
--   std::format: 1
--   std::print: 
-- ===============================================
-- 
-- Configuring done (3.5s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
[1/16] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
FAILED: CMakeFiles/database-service.dir/src/api/route_controller.cpp.o 
/usr/bin/g++-14 -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -I/home/<USER>/database-service-build/include -I/usr/include/postgresql -O3 -DNDEBUG -O3 -std=c++23 -Wall -Wextra -Wpedantic -MD -MT CMakeFiles/database-service.dir/src/api/route_controller.cpp.o -MF CMakeFiles/database-service.dir/src/api/route_controller.cpp.o.d -o CMakeFiles/database-service.dir/src/api/route_controller.cpp.o -c /home/<USER>/database-service-build/src/api/route_controller.cpp
In file included from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:14,
                 from /home/<USER>/database-service-build/include/database-service/api/route_controller.hpp:3,
                 from /home/<USER>/database-service-build/src/api/route_controller.cpp:1:
/home/<USER>/database-service-build/include/database-service/api/api_types.hpp:19:50: error: ‘Response’ was not declared in this scope
   19 | using RouteHandler = std::function<std::expected<Response, std::string>(const ParsedRequest&)>;
      |                                                  ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_types.hpp:19:71: error: template argument 1 is invalid
   19 | using RouteHandler = std::function<std::expected<Response, std::string>(const ParsedRequest&)>;
      |                                                                       ^
/home/<USER>/database-service-build/include/database-service/api/api_types.hpp:19:94: error: template argument 1 is invalid
   19 | using RouteHandler = std::function<std::expected<Response, std::string>(const ParsedRequest&)>;
      |                                                                                              ^
/home/<USER>/database-service-build/include/database-service/api/api_types.hpp:19:27: error: ‘<expression error>’ in namespace ‘std’ does not name a type
   19 | using RouteHandler = std::function<std::expected<Response, std::string>(const ParsedRequest&)>;
      |                           ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:114:71: error: ‘dbservice::api::RouteHandler’ has not been declared
  114 |     void addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler);
      |                                                                       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘void dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:15:43: error: cannot convert ‘dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
   15 |     server.addRoute("GET", "/api/health", [this](const ParsedRequest& request) {
      |                                           ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                           |
      |                                           dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)::<lambda(const dbservice::api::ParsedRequest&)>
   16 |         return handleHealthCheck(request);
      |         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ 
   17 |     });
      |     ~                                      
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:114:100: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  114 |     void addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler);
      |                                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:29:21: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
   29 |     response_struct.statusCode = 200;
      |                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:22:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   22 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[2/16] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[3/16] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
FAILED: CMakeFiles/database-service.dir/src/api/api_server.cpp.o 
/usr/bin/g++-14 -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -I/home/<USER>/database-service-build/include -I/usr/include/postgresql -O3 -DNDEBUG -O3 -std=c++23 -Wall -Wextra -Wpedantic -MD -MT CMakeFiles/database-service.dir/src/api/api_server.cpp.o -MF CMakeFiles/database-service.dir/src/api/api_server.cpp.o.d -o CMakeFiles/database-service.dir/src/api/api_server.cpp.o -c /home/<USER>/database-service-build/src/api/api_server.cpp
In file included from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:14,
                 from /home/<USER>/database-service-build/src/api/api_server.cpp:1:
/home/<USER>/database-service-build/include/database-service/api/api_types.hpp:19:50: error: ‘Response’ was not declared in this scope
   19 | using RouteHandler = std::function<std::expected<Response, std::string>(const ParsedRequest&)>;
      |                                                  ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_types.hpp:19:71: error: template argument 1 is invalid
   19 | using RouteHandler = std::function<std::expected<Response, std::string>(const ParsedRequest&)>;
      |                                                                       ^
/home/<USER>/database-service-build/include/database-service/api/api_types.hpp:19:94: error: template argument 1 is invalid
   19 | using RouteHandler = std::function<std::expected<Response, std::string>(const ParsedRequest&)>;
      |                                                                                              ^
/home/<USER>/database-service-build/include/database-service/api/api_types.hpp:19:27: error: ‘<expression error>’ in namespace ‘std’ does not name a type
   19 | using RouteHandler = std::function<std::expected<Response, std::string>(const ParsedRequest&)>;
      |                           ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:114:71: error: ‘dbservice::api::RouteHandler’ has not been declared
  114 |     void addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler);
      |                                                                       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:78: error: ‘dbservice::api::RouteHandler’ has not been declared
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’:
/home/<USER>/database-service-build/src/api/api_server.cpp:192:30: error: no matching function for call to ‘std::vector<dbservice::api::ApiServer::Route>::push_back(<brace-enclosed initializer list>)’
  192 |     routes_[method].push_back({
      |     ~~~~~~~~~~~~~~~~~~~~~~~~~^~
  193 |         std::regex("^" + regex_path + "$"),
      |         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  194 |         param_names,
      |         ~~~~~~~~~~~~          
  195 |         std::move(handler)
      |         ~~~~~~~~~~~~~~~~~~    
  196 |     });
      |     ~~                        
In file included from /usr/include/c++/14/vector:66,
                 from /usr/include/c++/14/functional:64,
                 from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:4:
/usr/include/c++/14/bits/stl_vector.h:1283:7: note: candidate: ‘constexpr void std::vector< <template-parameter-1-1>, <template-parameter-1-2> >::push_back(const value_type&) [with _Tp = dbservice::api::ApiServer::Route; _Alloc = std::allocator<dbservice::api::ApiServer::Route>; value_type = dbservice::api::ApiServer::Route]’
 1283 |       push_back(const value_type& __x)
      |       ^~~~~~~~~
/usr/include/c++/14/bits/stl_vector.h:1283:35: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘const std::vector<dbservice::api::ApiServer::Route>::value_type&’ {aka ‘const dbservice::api::ApiServer::Route&’}
 1283 |       push_back(const value_type& __x)
      |                 ~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/stl_vector.h:1300:7: note: candidate: ‘constexpr void std::vector< <template-parameter-1-1>, <template-parameter-1-2> >::push_back(value_type&&) [with _Tp = dbservice::api::ApiServer::Route; _Alloc = std::allocator<dbservice::api::ApiServer::Route>; value_type = dbservice::api::ApiServer::Route]’
 1300 |       push_back(value_type&& __x)
      |       ^~~~~~~~~
/usr/include/c++/14/bits/stl_vector.h:1300:30: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘std::vector<dbservice::api::ApiServer::Route>::value_type&&’ {aka ‘dbservice::api::ApiServer::Route&&’}
 1300 |       push_back(value_type&& __x)
      |                 ~~~~~~~~~~~~~^~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:441:69: error: conversion from ‘dbservice::api::Response’ to non-scalar type ‘std::string’ {aka ‘std::__cxx11::basic_string<char>’} requested
  441 |                 std::string error_response_str = createErrorResponse(400, "Bad Request", parsed_request_expected.error());
      |                                                  ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:453:69: error: conversion from ‘dbservice::api::Response’ to non-scalar type ‘std::string’ {aka ‘std::__cxx11::basic_string<char>’} requested
  453 |                 std::string error_response_str = createErrorResponse(500, "Internal Server Error", response_expected.error());
      |                                                  ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:512:29: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  512 |             response_struct.statusCode = 204; // No Content
      |                             ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:519:102: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘max_age’; did you mean ‘maxAge’?
  519 |                 response_struct.headers["Access-Control-Max-Age"] = std::to_string(this->corsConfig_.max_age);
      |                                                                                                      ^~~~~~~
      |                                                                                                      maxAge
/home/<USER>/database-service-build/src/api/api_server.cpp:520:39: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allow_credentials’; did you mean ‘allowCredentials’?
  520 |                 if (this->corsConfig_.allow_credentials) {
      |                                       ^~~~~~~~~~~~~~~~~
      |                                       allowCredentials
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:510:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
  510 |         [this](const ParsedRequest& /*request*/) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
  511 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
  512 |             response_struct.statusCode = 204; // No Content
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  513 |             if (this->corsConfig_.enabled) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  514 |                 // These headers should ideally be configured based on corsConfig_ and the specific route/request origin.
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  515 |                 // For simplicity in this step, using common permissive values.
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  516 |                 response_struct.headers["Access-Control-Allow-Origin"] = "*";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  517 |                 response_struct.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, PATCH, OPTIONS";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  518 |                 response_struct.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  519 |                 response_struct.headers["Access-Control-Max-Age"] = std::to_string(this->corsConfig_.max_age);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  520 |                 if (this->corsConfig_.allow_credentials) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  521 |                     response_struct.headers["Access-Control-Allow-Credentials"] = "true";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  522 |                 }
      |                 ~
  523 |             }
      |             ~
  524 |             response_struct.body = ""; // Body is empty for 204
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  525 |             return response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~
  526 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:540:29: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  540 |             response_struct.statusCode = 200;
      |                             ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:546:92: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
  546 |                 response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                            ^~~~~~~~~~~~~~~
      |                                                                                            allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:546:142: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
  546 |                 response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                                                              ^~~~~~~~~~~~~~~
      |                                                                                                                                              allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:547:40: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allow_credentials’; did you mean ‘allowCredentials’?
  547 |                  if (this->corsConfig_.allow_credentials) {
      |                                        ^~~~~~~~~~~~~~~~~
      |                                        allowCredentials
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:531:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
  531 |         [this](const ParsedRequest& /*request*/) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
  532 |             nlohmann::json json_body;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
  533 |             json_body["status"] = "ok";
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~
  534 |             json_body["version"] = "1.0.0"; // Consider making version dynamic
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  535 |             json_body["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  536 |                 std::chrono::system_clock::now().time_since_epoch()
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  537 |             ).count();
      |             ~~~~~~~~~~
  538 | 
      |          
  539 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
  540 |             response_struct.statusCode = 200;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  541 |             response_struct.headers["Content-Type"] = "application/json";
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  542 |             response_struct.body = json_body.dump();
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  543 | 
      |          
  544 |             // Add CORS headers if enabled
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  545 |             if (this->corsConfig_.enabled) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  546 |                 response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  547 |                  if (this->corsConfig_.allow_credentials) {
      |                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  548 |                     response_struct.headers["Access-Control-Allow-Credentials"] = "true";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  549 |                 }
      |                 ~
  550 |             }
      |             ~
  551 |             return response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~
  552 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:562:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  562 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:578:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  578 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:599:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  599 |                     response_struct.statusCode = 403;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:608:41: error: ‘dbService_’ was not declared in this scope; did you mean ‘dbservice’?
  608 |                 auto metrics_expected = dbService_->getDatabaseMetrics();
      |                                         ^~~~~~~~~~
      |                                         dbservice
/home/<USER>/database-service-build/src/api/api_server.cpp:610:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  610 |                     response_struct.statusCode = 500;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:614:22: error: no matching function for call to ‘nlohmann::json_abi_v3_11_3::basic_json<>::basic_json(<brace-enclosed initializer list>)’
  614 |                     }).dump();
      |                      ^
In file included from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:9:
/usr/include/nlohmann/json.hpp:1141:5: note: candidate: ‘template<class JsonRef, typename std::enable_if<nlohmann::json_abi_v3_11_3::detail::conjunction<nlohmann::json_abi_v3_11_3::detail::is_json_ref<JsonRef>, std::is_same<typename JsonRef::value_type, nlohmann::json_abi_v3_11_3::basic_json<std::map, std::vector, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, bool, long int, long unsigned int, double, std::allocator, nlohmann::json_abi_v3_11_3::adl_serializer, std::vector<unsigned char, std::allocator<unsigned char> >, void> > >::value, int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const JsonRef&) [with typename std::enable_if<nlohmann::json_abi_v3_11_3::detail::conjunction<nlohmann::json_abi_v3_11_3::detail::is_json_ref<JsonRef>, std::is_same<typename JsonRef::value_type, nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value, int>::type <anonymous> = JsonRef; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1141 |     basic_json(const JsonRef& ref) : basic_json(ref.moved_or_copied()) {}
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1141:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:614:22: note:   couldn’t deduce template parameter ‘JsonRef’
  614 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1032:5: note: candidate: ‘template<class InputIT, typename std::enable_if<(std::is_same<IterImpl, nlohmann::json_abi_v3_11_3::detail::iter_impl<nlohmann::json_abi_v3_11_3::basic_json<> > >::value || std::is_same<IterImpl, nlohmann::json_abi_v3_11_3::detail::iter_impl<const nlohmann::json_abi_v3_11_3::basic_json<> > >::value), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(InputIT, InputIT) [with typename std::enable_if<(std::is_same<InputIT, nlohmann::json_abi_v3_11_3::detail::iter_impl<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value || std::is_same<InputIT, nlohmann::json_abi_v3_11_3::detail::iter_impl<const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value), int>::type <anonymous> = InputIT; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1032 |     basic_json(InputIT first, InputIT last)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1032:5: note:   candidate expects 2 arguments, 1 provided
/usr/include/nlohmann/json.hpp:849:5: note: candidate: ‘template<class BasicJsonType, typename std::enable_if<(nlohmann::json_abi_v3_11_3::detail::is_basic_json<BasicJsonType>::value && (! std::is_same<nlohmann::json_abi_v3_11_3::basic_json<>, BasicJsonType>::value)), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const BasicJsonType&) [with typename std::enable_if<(nlohmann::json_abi_v3_11_3::detail::is_basic_json<BasicJsonType>::value && (! std::is_same<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>, BasicJsonType>::value)), int>::type <anonymous> = BasicJsonType; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
  849 |     basic_json(const BasicJsonType& val)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:849:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:614:22: note:   couldn’t deduce template parameter ‘BasicJsonType’
  614 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:835:5: note: candidate: ‘template<class CompatibleType, class U, typename std::enable_if<((! nlohmann::json_abi_v3_11_3::detail::is_basic_json<T>::value) && nlohmann::json_abi_v3_11_3::detail::is_compatible_type<nlohmann::json_abi_v3_11_3::basic_json<>, U>::value), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(CompatibleType&&) [with U = CompatibleType; typename std::enable_if<((! nlohmann::json_abi_v3_11_3::detail::is_basic_json<U>::value) && nlohmann::json_abi_v3_11_3::detail::is_compatible_type<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>, U>::value), int>::type <anonymous> = U; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
  835 |     basic_json(CompatibleType && val) noexcept(noexcept( // NOLINT(bugprone-forwarding-reference-overload,bugprone-exception-escape)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:835:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:614:22: note:   couldn’t deduce template parameter ‘CompatibleType’
  614 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1214:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1214 |     basic_json(basic_json&& other) noexcept
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1214:29: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>&&’
 1214 |     basic_json(basic_json&& other) noexcept
      |                ~~~~~~~~~~~~~^~~~~
/usr/include/nlohmann/json.hpp:1145:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1145 |     basic_json(const basic_json& other)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1145:34: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘const nlohmann::json_abi_v3_11_3::basic_json<>&’
 1145 |     basic_json(const basic_json& other)
      |                ~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/nlohmann/json.hpp:1020:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(size_type, const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; size_type = long unsigned int]’
 1020 |     basic_json(size_type cnt, const basic_json& val):
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1020:5: note:   candidate expects 2 arguments, 1 provided
/usr/include/nlohmann/json.hpp:902:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(initializer_list_t, bool, value_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; initializer_list_t = std::initializer_list<nlohmann::json_abi_v3_11_3::detail::json_ref<nlohmann::json_abi_v3_11_3::basic_json<> > >; value_t = nlohmann::json_abi_v3_11_3::detail::value_t]’
  902 |     basic_json(initializer_list_t init,
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:902:35: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>::initializer_list_t’ {aka ‘std::initializer_list<nlohmann::json_abi_v3_11_3::detail::json_ref<nlohmann::json_abi_v3_11_3::basic_json<> > >’}
  902 |     basic_json(initializer_list_t init,
      |                ~~~~~~~~~~~~~~~~~~~^~~~
/usr/include/nlohmann/json.hpp:823:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(std::nullptr_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; std::nullptr_t = std::nullptr_t]’
  823 |     basic_json(std::nullptr_t = nullptr) noexcept // NOLINT(bugprone-exception-escape)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:823:16: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘std::nullptr_t’
  823 |     basic_json(std::nullptr_t = nullptr) noexcept // NOLINT(bugprone-exception-escape)
      |                ^~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/nlohmann/json.hpp:815:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(value_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; value_t = nlohmann::json_abi_v3_11_3::detail::value_t]’
  815 |     basic_json(const value_t v)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:815:30: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>::value_t’ {aka ‘nlohmann::json_abi_v3_11_3::detail::value_t’}
  815 |     basic_json(const value_t v)
      |                ~~~~~~~~~~~~~~^
/home/<USER>/database-service-build/src/api/api_server.cpp:619:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  619 |                 response_struct.statusCode = 200;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:625:96: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
  625 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                ^~~~~~~~~~~~~~~
      |                                                                                                allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:625:146: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
  625 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                                                                  ^~~~~~~~~~~~~~~
      |                                                                                                                                                  allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:626:43: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allow_credentials’; did you mean ‘allowCredentials’?
  626 |                     if (this->corsConfig_.allow_credentials) {
      |                                           ^~~~~~~~~~~~~~~~~
      |                                           allowCredentials
/home/<USER>/database-service-build/src/api/api_server.cpp:634:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  634 |                 response_struct.statusCode = 500;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:557:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
  557 |         [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
  558 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
  559 |             try {
      |             ~~~~~
  560 |                 auto auth_header_it = request.headers.find("Authorization");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  561 |                 if (auth_header_it == request.headers.end()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  562 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  563 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  564 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
  565 |                         {"message", "Missing Authorization header"}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  566 |                     }).dump();
      |                     ~~~~~~~~~~
  567 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  568 |                     return std::unexpected("Missing Authorization header"); // Error for logging, actual response in struct
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  569 |                 }
      |                 ~
  570 | 
      |          
  571 |                 std::string token = auth_header_it->second;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  572 |                 if (token.rfind("Bearer ", 0) == 0) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  573 |                     token = token.substr(7);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~
  574 |                 }
      |                 ~
  575 | 
      |          
  576 |                 auto validationResult = securityManager_->validateToken(token);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  577 |                 if (!validationResult) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~
  578 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  579 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  580 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
  581 |                         {"message", validationResult.error().message}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  582 |                     }).dump();
      |                     ~~~~~~~~~~
  583 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  584 |                     return std::unexpected(validationResult.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  585 |                 }
      |                 ~
  586 | 
      |          
  587 |                 // Assuming validateToken now returns std::expected<UserClaims, SecurityError>
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  588 |                 // And UserClaims struct/map contains an 'is_admin' field or similar.
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  589 |                 // This part needs to be adjusted based on the actual return type of validateToken.
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  590 |                 // For now, let's assume it returns a structure or map from which we can get admin status.
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  591 |                 // Placeholder: directly get user info to check admin status
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  592 |                 auto userInfo = securityManager_->getUserInfo(*validationResult); // Assuming validateToken returns username
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  593 |                 bool isAdmin = false;
      |                 ~~~~~~~~~~~~~~~~~~~~~
  594 |                 if (userInfo && userInfo->count("is_admin") && ((*userInfo)["is_admin"] == "true" || (*userInfo)["is_admin"] == "t")) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  595 |                     isAdmin = true;
      |                     ~~~~~~~~~~~~~~~
  596 |                 }
      |                 ~
  597 | 
      |          
  598 |                 if (!isAdmin) {
      |                 ~~~~~~~~~~~~~~~
  599 |                     response_struct.statusCode = 403;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  600 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  601 |                         {"error", "Forbidden"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~
  602 |                         {"message", "User does not have admin privileges"}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  603 |                     }).dump();
      |                     ~~~~~~~~~~
  604 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  605 |                     return std::unexpected("User is not admin");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  606 |                 }
      |                 ~
  607 | 
      |          
  608 |                 auto metrics_expected = dbService_->getDatabaseMetrics();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  609 |                 if (!metrics_expected) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~
  610 |                     response_struct.statusCode = 500;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  611 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  612 |                         {"error", "Internal Server Error"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  613 |                         {"message", metrics_expected.error()}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  614 |                     }).dump();
      |                     ~~~~~~~~~~
  615 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  616 |                     return std::unexpected(metrics_expected.error());
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  617 |                 }
      |                 ~
  618 | 
      |          
  619 |                 response_struct.statusCode = 200;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  620 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  621 |                 response_struct.body = (*metrics_expected).dump(); // Assuming metrics_expected.value() is nlohmann::json
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  622 | 
      |          
  623 |                 // Add CORS headers if enabled
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  624 |                 if (this->corsConfig_.enabled) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  625 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  626 |                     if (this->corsConfig_.allow_credentials) {
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  627 |                         response_struct.headers["Access-Control-Allow-Credentials"] = "true";
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  628 |                     }
      |                     ~
  629 |                 }
      |                 ~
  630 |                 return response_struct;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
  631 | 
      |          
  632 |             } catch (const std::exception& e) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  633 |                 utils::Logger::error(std::format("Exception in /api/database/metrics: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  634 |                 response_struct.statusCode = 500;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  635 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  636 |                     {"error", "Internal Server Error"},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  637 |                     {"message", e.what()}
      |                     ~~~~~~~~~~~~~~~~~~~~~
  638 |                 }).dump();
      |                 ~~~~~~~~~~
  639 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  640 |                 return std::unexpected(std::string("Exception: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  641 |             }
      |             ~
  642 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:651:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  651 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:667:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  667 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:683:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  683 |                     response_struct.statusCode = 403;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:693:41: error: ‘dbService_’ was not declared in this scope; did you mean ‘dbservice’?
  693 |                 auto metrics_expected = dbService_->getConnectionPoolMetrics();
      |                                         ^~~~~~~~~~
      |                                         dbservice
/home/<USER>/database-service-build/src/api/api_server.cpp:695:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  695 |                     response_struct.statusCode = 500;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:699:22: error: no matching function for call to ‘nlohmann::json_abi_v3_11_3::basic_json<>::basic_json(<brace-enclosed initializer list>)’
  699 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1141:5: note: candidate: ‘template<class JsonRef, typename std::enable_if<nlohmann::json_abi_v3_11_3::detail::conjunction<nlohmann::json_abi_v3_11_3::detail::is_json_ref<JsonRef>, std::is_same<typename JsonRef::value_type, nlohmann::json_abi_v3_11_3::basic_json<std::map, std::vector, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, bool, long int, long unsigned int, double, std::allocator, nlohmann::json_abi_v3_11_3::adl_serializer, std::vector<unsigned char, std::allocator<unsigned char> >, void> > >::value, int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const JsonRef&) [with typename std::enable_if<nlohmann::json_abi_v3_11_3::detail::conjunction<nlohmann::json_abi_v3_11_3::detail::is_json_ref<JsonRef>, std::is_same<typename JsonRef::value_type, nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value, int>::type <anonymous> = JsonRef; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1141 |     basic_json(const JsonRef& ref) : basic_json(ref.moved_or_copied()) {}
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1141:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:699:22: note:   couldn’t deduce template parameter ‘JsonRef’
  699 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1032:5: note: candidate: ‘template<class InputIT, typename std::enable_if<(std::is_same<IterImpl, nlohmann::json_abi_v3_11_3::detail::iter_impl<nlohmann::json_abi_v3_11_3::basic_json<> > >::value || std::is_same<IterImpl, nlohmann::json_abi_v3_11_3::detail::iter_impl<const nlohmann::json_abi_v3_11_3::basic_json<> > >::value), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(InputIT, InputIT) [with typename std::enable_if<(std::is_same<InputIT, nlohmann::json_abi_v3_11_3::detail::iter_impl<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value || std::is_same<InputIT, nlohmann::json_abi_v3_11_3::detail::iter_impl<const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value), int>::type <anonymous> = InputIT; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1032 |     basic_json(InputIT first, InputIT last)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1032:5: note:   candidate expects 2 arguments, 1 provided
/usr/include/nlohmann/json.hpp:849:5: note: candidate: ‘template<class BasicJsonType, typename std::enable_if<(nlohmann::json_abi_v3_11_3::detail::is_basic_json<BasicJsonType>::value && (! std::is_same<nlohmann::json_abi_v3_11_3::basic_json<>, BasicJsonType>::value)), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const BasicJsonType&) [with typename std::enable_if<(nlohmann::json_abi_v3_11_3::detail::is_basic_json<BasicJsonType>::value && (! std::is_same<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>, BasicJsonType>::value)), int>::type <anonymous> = BasicJsonType; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
  849 |     basic_json(const BasicJsonType& val)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:849:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:699:22: note:   couldn’t deduce template parameter ‘BasicJsonType’
  699 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:835:5: note: candidate: ‘template<class CompatibleType, class U, typename std::enable_if<((! nlohmann::json_abi_v3_11_3::detail::is_basic_json<T>::value) && nlohmann::json_abi_v3_11_3::detail::is_compatible_type<nlohmann::json_abi_v3_11_3::basic_json<>, U>::value), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(CompatibleType&&) [with U = CompatibleType; typename std::enable_if<((! nlohmann::json_abi_v3_11_3::detail::is_basic_json<U>::value) && nlohmann::json_abi_v3_11_3::detail::is_compatible_type<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>, U>::value), int>::type <anonymous> = U; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
  835 |     basic_json(CompatibleType && val) noexcept(noexcept( // NOLINT(bugprone-forwarding-reference-overload,bugprone-exception-escape)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:835:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:699:22: note:   couldn’t deduce template parameter ‘CompatibleType’
  699 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1214:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1214 |     basic_json(basic_json&& other) noexcept
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1214:29: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>&&’
 1214 |     basic_json(basic_json&& other) noexcept
      |                ~~~~~~~~~~~~~^~~~~
/usr/include/nlohmann/json.hpp:1145:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1145 |     basic_json(const basic_json& other)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1145:34: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘const nlohmann::json_abi_v3_11_3::basic_json<>&’
 1145 |     basic_json(const basic_json& other)
      |                ~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/nlohmann/json.hpp:1020:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(size_type, const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; size_type = long unsigned int]’
 1020 |     basic_json(size_type cnt, const basic_json& val):
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1020:5: note:   candidate expects 2 arguments, 1 provided
/usr/include/nlohmann/json.hpp:902:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(initializer_list_t, bool, value_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; initializer_list_t = std::initializer_list<nlohmann::json_abi_v3_11_3::detail::json_ref<nlohmann::json_abi_v3_11_3::basic_json<> > >; value_t = nlohmann::json_abi_v3_11_3::detail::value_t]’
  902 |     basic_json(initializer_list_t init,
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:902:35: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>::initializer_list_t’ {aka ‘std::initializer_list<nlohmann::json_abi_v3_11_3::detail::json_ref<nlohmann::json_abi_v3_11_3::basic_json<> > >’}
  902 |     basic_json(initializer_list_t init,
      |                ~~~~~~~~~~~~~~~~~~~^~~~
/usr/include/nlohmann/json.hpp:823:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(std::nullptr_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; std::nullptr_t = std::nullptr_t]’
  823 |     basic_json(std::nullptr_t = nullptr) noexcept // NOLINT(bugprone-exception-escape)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:823:16: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘std::nullptr_t’
  823 |     basic_json(std::nullptr_t = nullptr) noexcept // NOLINT(bugprone-exception-escape)
      |                ^~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/nlohmann/json.hpp:815:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(value_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; value_t = nlohmann::json_abi_v3_11_3::detail::value_t]’
  815 |     basic_json(const value_t v)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:815:30: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>::value_t’ {aka ‘nlohmann::json_abi_v3_11_3::detail::value_t’}
  815 |     basic_json(const value_t v)
      |                ~~~~~~~~~~~~~~^
/home/<USER>/database-service-build/src/api/api_server.cpp:704:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  704 |                 response_struct.statusCode = 200;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:709:96: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
  709 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                ^~~~~~~~~~~~~~~
      |                                                                                                allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:709:146: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
  709 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                                                                  ^~~~~~~~~~~~~~~
      |                                                                                                                                                  allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:710:43: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allow_credentials’; did you mean ‘allowCredentials’?
  710 |                     if (this->corsConfig_.allow_credentials) {
      |                                           ^~~~~~~~~~~~~~~~~
      |                                           allowCredentials
/home/<USER>/database-service-build/src/api/api_server.cpp:718:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  718 |                 response_struct.statusCode = 500;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:646:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
  646 |         [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
  647 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
  648 |             try {
      |             ~~~~~
  649 |                 auto auth_header_it = request.headers.find("Authorization");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  650 |                 if (auth_header_it == request.headers.end()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  651 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  652 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  653 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
  654 |                         {"message", "Missing Authorization header"}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  655 |                     }).dump();
      |                     ~~~~~~~~~~
  656 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  657 |                     return std::unexpected("Missing Authorization header");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  658 |                 }
      |                 ~
  659 | 
      |          
  660 |                 std::string token = auth_header_it->second;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  661 |                 if (token.rfind("Bearer ", 0) == 0) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  662 |                     token = token.substr(7);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~
  663 |                 }
      |                 ~
  664 | 
      |          
  665 |                 auto validationResult = securityManager_->validateToken(token);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  666 |                 if (!validationResult) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~
  667 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  668 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  669 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
  670 |                         {"message", validationResult.error().message}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  671 |                     }).dump();
      |                     ~~~~~~~~~~
  672 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  673 |                     return std::unexpected(validationResult.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  674 |                 }
      |                 ~
  675 | 
      |          
  676 |                 auto userInfo = securityManager_->getUserInfo(*validationResult);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  677 |                 bool isAdmin = false;
      |                 ~~~~~~~~~~~~~~~~~~~~~
  678 |                 if (userInfo && userInfo->count("is_admin") && ((*userInfo)["is_admin"] == "true" || (*userInfo)["is_admin"] == "t")) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  679 |                     isAdmin = true;
      |                     ~~~~~~~~~~~~~~~
  680 |                 }
      |                 ~
  681 | 
      |          
  682 |                 if (!isAdmin) {
      |                 ~~~~~~~~~~~~~~~
  683 |                     response_struct.statusCode = 403;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  684 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  685 |                         {"error", "Forbidden"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~
  686 |                         {"message", "User does not have admin privileges"}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  687 |                     }).dump();
      |                     ~~~~~~~~~~
  688 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  689 |                     return std::unexpected("User is not admin");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  690 |                 }
      |                 ~
  691 | 
      |          
  692 |                 // Assuming dbService_ has a method to get connection pool metrics
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  693 |                 auto metrics_expected = dbService_->getConnectionPoolMetrics();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  694 |                 if (!metrics_expected) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~
  695 |                     response_struct.statusCode = 500;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  696 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  697 |                         {"error", "Internal Server Error"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  698 |                         {"message", metrics_expected.error()}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  699 |                     }).dump();
      |                     ~~~~~~~~~~
  700 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  701 |                     return std::unexpected(metrics_expected.error());
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  702 |                 }
      |                 ~
  703 | 
      |          
  704 |                 response_struct.statusCode = 200;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  705 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  706 |                 response_struct.body = (*metrics_expected).dump();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  707 | 
      |          
  708 |                 if (this->corsConfig_.enabled) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  709 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  710 |                     if (this->corsConfig_.allow_credentials) {
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  711 |                         response_struct.headers["Access-Control-Allow-Credentials"] = "true";
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  712 |                     }
      |                     ~
  713 |                 }
      |                 ~
  714 |                 return response_struct;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
  715 | 
      |          
  716 |             } catch (const std::exception& e) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  717 |                 utils::Logger::error(std::format("Exception in /api/database/metrics/connection-pool: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  718 |                 response_struct.statusCode = 500;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  719 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  720 |                     {"error", "Internal Server Error"},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  721 |                     {"message", e.what()}
      |                     ~~~~~~~~~~~~~~~~~~~~~
  722 |                 }).dump();
      |                 ~~~~~~~~~~
  723 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  724 |                 return std::unexpected(std::string("Exception: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  725 |             }
      |             ~
  726 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:735:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  735 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:751:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  751 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:767:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  767 |                     response_struct.statusCode = 403;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:777:41: error: ‘dbService_’ was not declared in this scope; did you mean ‘dbservice’?
  777 |                 auto metrics_expected = dbService_->getQueryPerformanceMetrics();
      |                                         ^~~~~~~~~~
      |                                         dbservice
/home/<USER>/database-service-build/src/api/api_server.cpp:779:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  779 |                     response_struct.statusCode = 500;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:783:22: error: no matching function for call to ‘nlohmann::json_abi_v3_11_3::basic_json<>::basic_json(<brace-enclosed initializer list>)’
  783 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1141:5: note: candidate: ‘template<class JsonRef, typename std::enable_if<nlohmann::json_abi_v3_11_3::detail::conjunction<nlohmann::json_abi_v3_11_3::detail::is_json_ref<JsonRef>, std::is_same<typename JsonRef::value_type, nlohmann::json_abi_v3_11_3::basic_json<std::map, std::vector, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, bool, long int, long unsigned int, double, std::allocator, nlohmann::json_abi_v3_11_3::adl_serializer, std::vector<unsigned char, std::allocator<unsigned char> >, void> > >::value, int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const JsonRef&) [with typename std::enable_if<nlohmann::json_abi_v3_11_3::detail::conjunction<nlohmann::json_abi_v3_11_3::detail::is_json_ref<JsonRef>, std::is_same<typename JsonRef::value_type, nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value, int>::type <anonymous> = JsonRef; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1141 |     basic_json(const JsonRef& ref) : basic_json(ref.moved_or_copied()) {}
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1141:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:783:22: note:   couldn’t deduce template parameter ‘JsonRef’
  783 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1032:5: note: candidate: ‘template<class InputIT, typename std::enable_if<(std::is_same<IterImpl, nlohmann::json_abi_v3_11_3::detail::iter_impl<nlohmann::json_abi_v3_11_3::basic_json<> > >::value || std::is_same<IterImpl, nlohmann::json_abi_v3_11_3::detail::iter_impl<const nlohmann::json_abi_v3_11_3::basic_json<> > >::value), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(InputIT, InputIT) [with typename std::enable_if<(std::is_same<InputIT, nlohmann::json_abi_v3_11_3::detail::iter_impl<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value || std::is_same<InputIT, nlohmann::json_abi_v3_11_3::detail::iter_impl<const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value), int>::type <anonymous> = InputIT; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1032 |     basic_json(InputIT first, InputIT last)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1032:5: note:   candidate expects 2 arguments, 1 provided
/usr/include/nlohmann/json.hpp:849:5: note: candidate: ‘template<class BasicJsonType, typename std::enable_if<(nlohmann::json_abi_v3_11_3::detail::is_basic_json<BasicJsonType>::value && (! std::is_same<nlohmann::json_abi_v3_11_3::basic_json<>, BasicJsonType>::value)), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const BasicJsonType&) [with typename std::enable_if<(nlohmann::json_abi_v3_11_3::detail::is_basic_json<BasicJsonType>::value && (! std::is_same<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>, BasicJsonType>::value)), int>::type <anonymous> = BasicJsonType; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
  849 |     basic_json(const BasicJsonType& val)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:849:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:783:22: note:   couldn’t deduce template parameter ‘BasicJsonType’
  783 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:835:5: note: candidate: ‘template<class CompatibleType, class U, typename std::enable_if<((! nlohmann::json_abi_v3_11_3::detail::is_basic_json<T>::value) && nlohmann::json_abi_v3_11_3::detail::is_compatible_type<nlohmann::json_abi_v3_11_3::basic_json<>, U>::value), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(CompatibleType&&) [with U = CompatibleType; typename std::enable_if<((! nlohmann::json_abi_v3_11_3::detail::is_basic_json<U>::value) && nlohmann::json_abi_v3_11_3::detail::is_compatible_type<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>, U>::value), int>::type <anonymous> = U; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
  835 |     basic_json(CompatibleType && val) noexcept(noexcept( // NOLINT(bugprone-forwarding-reference-overload,bugprone-exception-escape)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:835:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:783:22: note:   couldn’t deduce template parameter ‘CompatibleType’
  783 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1214:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1214 |     basic_json(basic_json&& other) noexcept
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1214:29: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>&&’
 1214 |     basic_json(basic_json&& other) noexcept
      |                ~~~~~~~~~~~~~^~~~~
/usr/include/nlohmann/json.hpp:1145:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1145 |     basic_json(const basic_json& other)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1145:34: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘const nlohmann::json_abi_v3_11_3::basic_json<>&’
 1145 |     basic_json(const basic_json& other)
      |                ~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/nlohmann/json.hpp:1020:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(size_type, const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; size_type = long unsigned int]’
 1020 |     basic_json(size_type cnt, const basic_json& val):
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1020:5: note:   candidate expects 2 arguments, 1 provided
/usr/include/nlohmann/json.hpp:902:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(initializer_list_t, bool, value_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; initializer_list_t = std::initializer_list<nlohmann::json_abi_v3_11_3::detail::json_ref<nlohmann::json_abi_v3_11_3::basic_json<> > >; value_t = nlohmann::json_abi_v3_11_3::detail::value_t]’
  902 |     basic_json(initializer_list_t init,
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:902:35: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>::initializer_list_t’ {aka ‘std::initializer_list<nlohmann::json_abi_v3_11_3::detail::json_ref<nlohmann::json_abi_v3_11_3::basic_json<> > >’}
  902 |     basic_json(initializer_list_t init,
      |                ~~~~~~~~~~~~~~~~~~~^~~~
/usr/include/nlohmann/json.hpp:823:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(std::nullptr_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; std::nullptr_t = std::nullptr_t]’
  823 |     basic_json(std::nullptr_t = nullptr) noexcept // NOLINT(bugprone-exception-escape)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:823:16: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘std::nullptr_t’
  823 |     basic_json(std::nullptr_t = nullptr) noexcept // NOLINT(bugprone-exception-escape)
      |                ^~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/nlohmann/json.hpp:815:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(value_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; value_t = nlohmann::json_abi_v3_11_3::detail::value_t]’
  815 |     basic_json(const value_t v)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:815:30: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>::value_t’ {aka ‘nlohmann::json_abi_v3_11_3::detail::value_t’}
  815 |     basic_json(const value_t v)
      |                ~~~~~~~~~~~~~~^
/home/<USER>/database-service-build/src/api/api_server.cpp:788:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  788 |                 response_struct.statusCode = 200;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:793:96: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
  793 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                ^~~~~~~~~~~~~~~
      |                                                                                                allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:793:146: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
  793 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                                                                  ^~~~~~~~~~~~~~~
      |                                                                                                                                                  allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:794:43: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allow_credentials’; did you mean ‘allowCredentials’?
  794 |                     if (this->corsConfig_.allow_credentials) {
      |                                           ^~~~~~~~~~~~~~~~~
      |                                           allowCredentials
/home/<USER>/database-service-build/src/api/api_server.cpp:802:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  802 |                 response_struct.statusCode = 500;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:730:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
  730 |         [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
  731 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
  732 |             try {
      |             ~~~~~
  733 |                 auto auth_header_it = request.headers.find("Authorization");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  734 |                 if (auth_header_it == request.headers.end()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  735 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  736 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  737 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
  738 |                         {"message", "Missing Authorization header"}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  739 |                     }).dump();
      |                     ~~~~~~~~~~
  740 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  741 |                     return std::unexpected("Missing Authorization header");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  742 |                 }
      |                 ~
  743 | 
      |          
  744 |                 std::string token = auth_header_it->second;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  745 |                 if (token.rfind("Bearer ", 0) == 0) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  746 |                     token = token.substr(7);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~
  747 |                 }
      |                 ~
  748 | 
      |          
  749 |                 auto validationResult = securityManager_->validateToken(token);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  750 |                 if (!validationResult) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~
  751 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  752 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  753 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
  754 |                         {"message", validationResult.error().message}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  755 |                     }).dump();
      |                     ~~~~~~~~~~
  756 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  757 |                     return std::unexpected(validationResult.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  758 |                 }
      |                 ~
  759 | 
      |          
  760 |                 auto userInfo = securityManager_->getUserInfo(*validationResult);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  761 |                 bool isAdmin = false;
      |                 ~~~~~~~~~~~~~~~~~~~~~
  762 |                 if (userInfo && userInfo->count("is_admin") && ((*userInfo)["is_admin"] == "true" || (*userInfo)["is_admin"] == "t")) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  763 |                     isAdmin = true;
      |                     ~~~~~~~~~~~~~~~
  764 |                 }
      |                 ~
  765 | 
      |          
  766 |                 if (!isAdmin) {
      |                 ~~~~~~~~~~~~~~~
  767 |                     response_struct.statusCode = 403;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  768 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  769 |                         {"error", "Forbidden"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~
  770 |                         {"message", "User does not have admin privileges"}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  771 |                     }).dump();
      |                     ~~~~~~~~~~
  772 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  773 |                     return std::unexpected("User is not admin");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  774 |                 }
      |                 ~
  775 | 
      |          
  776 |                 // Assuming dbService_ has a method to get query performance metrics
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  777 |                 auto metrics_expected = dbService_->getQueryPerformanceMetrics();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  778 |                 if (!metrics_expected) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~
  779 |                     response_struct.statusCode = 500;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  780 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  781 |                         {"error", "Internal Server Error"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  782 |                         {"message", metrics_expected.error()}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  783 |                     }).dump();
      |                     ~~~~~~~~~~
  784 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  785 |                     return std::unexpected(metrics_expected.error());
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  786 |                 }
      |                 ~
  787 | 
      |          
  788 |                 response_struct.statusCode = 200;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  789 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  790 |                 response_struct.body = (*metrics_expected).dump();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  791 | 
      |          
  792 |                 if (this->corsConfig_.enabled) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  793 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  794 |                     if (this->corsConfig_.allow_credentials) {
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  795 |                         response_struct.headers["Access-Control-Allow-Credentials"] = "true";
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  796 |                     }
      |                     ~
  797 |                 }
      |                 ~
  798 |                 return response_struct;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
  799 | 
      |          
  800 |             } catch (const std::exception& e) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  801 |                 utils::Logger::error(std::format("Exception in /api/database/metrics/query-performance: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  802 |                 response_struct.statusCode = 500;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  803 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  804 |                     {"error", "Internal Server Error"},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  805 |                     {"message", e.what()}
      |                     ~~~~~~~~~~~~~~~~~~~~~
  806 |                 }).dump();
      |                 ~~~~~~~~~~
  807 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  808 |                 return std::unexpected(std::string("Exception: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  809 |             }
      |             ~
  810 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:821:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  821 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:833:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  833 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:848:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  848 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:865:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  865 |                 response_struct.statusCode = 200;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:870:96: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
  870 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                ^~~~~~~~~~~~~~~
      |                                                                                                allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:870:146: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
  870 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                                                                  ^~~~~~~~~~~~~~~
      |                                                                                                                                                  allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:871:44: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allow_credentials’; did you mean ‘allowCredentials’?
  871 |                      if (this->corsConfig_.allow_credentials) {
      |                                            ^~~~~~~~~~~~~~~~~
      |                                            allowCredentials
/home/<USER>/database-service-build/src/api/api_server.cpp:879:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  879 |                 response_struct.statusCode = 400;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:888:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  888 |                 response_struct.statusCode = 500;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:817:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
  817 |         [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
  818 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
  819 |             try {
      |             ~~~~~
  820 |                 if (request.body.empty()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~
  821 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  822 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  823 |                         {"error", "Bad Request"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~
  824 |                         {"message", "Request body is empty"}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  825 |                     }).dump();
      |                     ~~~~~~~~~~
  826 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  827 |                     return std::unexpected("Request body is empty");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  828 |                 }
      |                 ~
  829 | 
      |          
  830 |                 nlohmann::json request_json = nlohmann::json::parse(request.body);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  831 | 
      |          
  832 |                 if (!request_json.contains("username") || !request_json.contains("password")) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  833 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  834 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  835 |                         {"error", "Bad Request"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~
  836 |                         {"message", "Username and password are required"}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  837 |                     }).dump();
      |                     ~~~~~~~~~~
  838 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  839 |                     return std::unexpected("Missing username or password in request body");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  840 |                 }
      |                 ~
  841 | 
      |          
  842 |                 std::string username = request_json["username"].get<std::string>();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  843 |                 std::string password = request_json["password"].get<std::string>();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  844 | 
      |          
  845 |                 auto tokens_expected = securityManager_->authenticate(username, password);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  846 | 
      |          
  847 |                 if (!tokens_expected) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
  848 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  849 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  850 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
  851 |                         {"message", tokens_expected.error().message} // Use error message from SecurityError
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  852 |                     }).dump();
      |                     ~~~~~~~~~~
  853 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  854 |                     return std::unexpected(tokens_expected.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  855 |                 }
      |                 ~
  856 | 
      |          
  857 |                 const auto& tokens = *tokens_expected;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  858 |                 nlohmann::json response_body_json = {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  859 |                     {"access_token", tokens.accessToken},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  860 |                     {"refresh_token", tokens.refreshToken},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  861 |                     {"user", username}, // Consider getting username from tokens_expected if it contains verified user identity
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  862 |                     {"token_type", "Bearer"}
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~
  863 |                 };
      |                 ~~
  864 | 
      |          
  865 |                 response_struct.statusCode = 200;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  866 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  867 |                 response_struct.body = response_body_json.dump();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  868 | 
      |          
  869 |                 if (this->corsConfig_.enabled) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  870 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  871 |                      if (this->corsConfig_.allow_credentials) {
      |                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  872 |                         response_struct.headers["Access-Control-Allow-Credentials"] = "true";
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  873 |                     }
      |                     ~
  874 |                 }
      |                 ~
  875 |                 return response_struct;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
  876 | 
      |          
  877 |             } catch (const nlohmann::json::parse_error& e) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  878 |                 utils::Logger::error(std::format("JSON parse error in /api/auth/login: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  879 |                 response_struct.statusCode = 400;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  880 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  881 |                     {"error", "Bad Request"},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~
  882 |                     {"message", std::string("Invalid JSON format: ") + e.what()}
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  883 |                 }).dump();
      |                 ~~~~~~~~~~
  884 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  885 |                 return std::unexpected(std::string("Invalid JSON: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  886 |             } catch (const std::exception& e) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  887 |                 utils::Logger::error(std::format("Exception in /api/auth/login: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  888 |                 response_struct.statusCode = 500;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  889 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  890 |                     {"error", "Internal Server Error"},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  891 |                     {"message", e.what()}
      |                     ~~~~~~~~~~~~~~~~~~~~~
  892 |                 }).dump();
      |                 ~~~~~~~~~~
  893 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  894 |                 return std::unexpected(std::string("Exception: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  895 |             }
      |             ~
  896 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:905:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  905 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:917:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  917 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:930:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  930 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:946:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  946 |                 response_struct.statusCode = 200;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:951:96: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
  951 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                ^~~~~~~~~~~~~~~
      |                                                                                                allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:951:146: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
  951 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                                                                  ^~~~~~~~~~~~~~~
      |                                                                                                                                                  allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:952:44: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allow_credentials’; did you mean ‘allowCredentials’?
  952 |                      if (this->corsConfig_.allow_credentials) {
      |                                            ^~~~~~~~~~~~~~~~~
      |                                            allowCredentials
/home/<USER>/database-service-build/src/api/api_server.cpp:960:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  960 |                 response_struct.statusCode = 400;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:969:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  969 |                 response_struct.statusCode = 500;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:901:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
  901 |         [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
  902 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
  903 |             try {
      |             ~~~~~
  904 |                 if (request.body.empty()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~
  905 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  906 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  907 |                         {"error", "Bad Request"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~
  908 |                         {"message", "Request body is empty"}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  909 |                     }).dump();
      |                     ~~~~~~~~~~
  910 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  911 |                     return std::unexpected("Request body is empty");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  912 |                 }
      |                 ~
  913 | 
      |          
  914 |                 nlohmann::json request_json = nlohmann::json::parse(request.body);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  915 | 
      |          
  916 |                 if (!request_json.contains("refresh_token")) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  917 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  918 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  919 |                         {"error", "Bad Request"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~
  920 |                         {"message", "refresh_token is required"}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  921 |                     }).dump();
      |                     ~~~~~~~~~~
  922 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  923 |                     return std::unexpected("Missing refresh_token in request body");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  924 |                 }
      |                 ~
  925 | 
      |          
  926 |                 std::string refresh_token = request_json["refresh_token"].get<std::string>();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  927 |                 auto tokens_expected = securityManager_->refreshAccessToken(refresh_token);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  928 | 
      |          
  929 |                 if (!tokens_expected) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
  930 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  931 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  932 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
  933 |                         {"message", tokens_expected.error().message} // Use error message from SecurityError
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  934 |                     }).dump();
      |                     ~~~~~~~~~~
  935 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  936 |                     return std::unexpected(tokens_expected.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  937 |                 }
      |                 ~
  938 | 
      |          
  939 |                 const auto& tokens = *tokens_expected;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  940 |                 nlohmann::json response_body_json = {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  941 |                     {"access_token", tokens.accessToken},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  942 |                     {"refresh_token", tokens.refreshToken},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  943 |                     {"token_type", "Bearer"}
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~
  944 |                 };
      |                 ~~
  945 | 
      |          
  946 |                 response_struct.statusCode = 200;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  947 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  948 |                 response_struct.body = response_body_json.dump();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  949 | 
      |          
  950 |                 if (this->corsConfig_.enabled) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  951 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  952 |                      if (this->corsConfig_.allow_credentials) {
      |                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  953 |                         response_struct.headers["Access-Control-Allow-Credentials"] = "true";
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  954 |                     }
      |                     ~
  955 |                 }
      |                 ~
  956 |                 return response_struct;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
  957 | 
      |          
  958 |             } catch (const nlohmann::json::parse_error& e) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  959 |                 utils::Logger::error(std::format("JSON parse error in /api/auth/refresh: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  960 |                 response_struct.statusCode = 400;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  961 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  962 |                     {"error", "Bad Request"},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~
  963 |                     {"message", std::string("Invalid JSON format: ") + e.what()}
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  964 |                 }).dump();
      |                 ~~~~~~~~~~
  965 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  966 |                 return std::unexpected(std::string("Invalid JSON: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  967 |             } catch (const std::exception& e) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  968 |                 utils::Logger::error(std::format("Exception in /api/auth/refresh: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  969 |                 response_struct.statusCode = 500;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  970 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  971 |                     {"error", "Internal Server Error"},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  972 |                     {"message", e.what()}
      |                     ~~~~~~~~~~~~~~~~~~~~~
  973 |                 }).dump();
      |                 ~~~~~~~~~~
  974 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  975 |                 return std::unexpected(std::string("Exception: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  976 |             }
      |             ~
  977 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:987:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  987 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:998:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
  998 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1010:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1010 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1023:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1023 |                     response_struct.statusCode = 500;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1040:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1040 |                 response_struct.statusCode = 200;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1045:96: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
 1045 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                ^~~~~~~~~~~~~~~
      |                                                                                                allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:1045:146: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
 1045 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                                                                  ^~~~~~~~~~~~~~~
      |                                                                                                                                                  allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:1046:44: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allow_credentials’; did you mean ‘allowCredentials’?
 1046 |                      if (this->corsConfig_.allow_credentials) {
      |                                            ^~~~~~~~~~~~~~~~~
      |                                            allowCredentials
/home/<USER>/database-service-build/src/api/api_server.cpp:1054:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1054 |                 response_struct.statusCode = 500;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:982:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
  982 |         [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
  983 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
  984 |             try {
      |             ~~~~~
  985 |                 auto auth_header_it = request.headers.find("Authorization");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  986 |                 if (auth_header_it == request.headers.end()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  987 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  988 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  989 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
  990 |                         {"message", "Authentication required. Missing Authorization header."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  991 |                     }).dump();
      |                     ~~~~~~~~~~
  992 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  993 |                     return std::unexpected("Missing Authorization header");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  994 |                 }
      |                 ~
  995 | 
      |          
  996 |                 const std::string& auth_header = auth_header_it->second;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  997 |                 if (auth_header.rfind("Bearer ", 0) != 0) { // Check if starts with "Bearer "
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  998 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  999 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1000 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1001 |                         {"message", "Invalid authentication format. Expected Bearer token."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1002 |                     }).dump();
      |                     ~~~~~~~~~~
 1003 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1004 |                     return std::unexpected("Invalid token format, not Bearer");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1005 |                 }
      |                 ~
 1006 | 
      |          
 1007 |                 std::string token = auth_header.substr(7); // Extract token part
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1008 |                 auto validation_result = securityManager_->validateToken(token);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1009 |                 if (!validation_result) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~
 1010 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1011 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1012 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1013 |                         {"message", validation_result.error().message}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1014 |                     }).dump();
      |                     ~~~~~~~~~~
 1015 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1016 |                     return std::unexpected(validation_result.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1017 |                 }
      |                 ~
 1018 | 
      |          
 1019 |                 // Token is valid, username is in validation_result.value()
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1020 |                 auto user_info_expected = securityManager_->getUserInfo(*validation_result);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1021 |                 if (!user_info_expected) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1022 |                     // This case might indicate an internal issue if a validated token doesn't have user info
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1023 |                     response_struct.statusCode = 500;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1024 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1025 |                         {"error", "Internal Server Error"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1026 |                         {"message", user_info_expected.error().message}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1027 |                     }).dump();
      |                     ~~~~~~~~~~
 1028 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1029 |                     return std::unexpected("Failed to retrieve user info: " + user_info_expected.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1030 |                 }
      |                 ~
 1031 | 
      |          
 1032 |                 const auto& user_info_map = *user_info_expected;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1033 |                 nlohmann::json response_body_json = {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1034 |                     {"username", user_info_map.count("username") ? user_info_map.at("username") : ""},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1035 |                     {"email", user_info_map.count("email") ? user_info_map.at("email") : ""},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1036 |                     {"is_admin", user_info_map.count("is_admin") ? (user_info_map.at("is_admin") == "t" || user_info_map.at("is_admin") == "true") : false},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1037 |                     {"created_at", user_info_map.count("created_at") ? user_info_map.at("created_at") : ""}
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1038 |                 };
      |                 ~~
 1039 | 
      |          
 1040 |                 response_struct.statusCode = 200;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1041 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1042 |                 response_struct.body = response_body_json.dump();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1043 | 
      |          
 1044 |                 if (this->corsConfig_.enabled) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1045 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1046 |                      if (this->corsConfig_.allow_credentials) {
      |                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1047 |                         response_struct.headers["Access-Control-Allow-Credentials"] = "true";
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1048 |                     }
      |                     ~
 1049 |                 }
      |                 ~
 1050 |                 return response_struct;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
 1051 | 
      |          
 1052 |             } catch (const std::exception& e) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1053 |                 utils::Logger::error(std::format("Exception in /api/auth/user: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1054 |                 response_struct.statusCode = 500;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1055 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1056 |                     {"error", "Internal Server Error"},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1057 |                     {"message", e.what()}
      |                     ~~~~~~~~~~~~~~~~~~~~~
 1058 |                 }).dump();
      |                 ~~~~~~~~~~
 1059 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1060 |                 return std::unexpected(std::string("Exception: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1061 |             }
      |             ~
 1062 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:1072:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1072 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1083:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1083 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1095:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1095 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1109:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1109 |                     response_struct.statusCode = 500;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1118:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1118 |                 response_struct.statusCode = 200;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1125:96: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
 1125 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                ^~~~~~~~~~~~~~~
      |                                                                                                allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:1125:146: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
 1125 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                                                                  ^~~~~~~~~~~~~~~
      |                                                                                                                                                  allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:1126:44: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allow_credentials’; did you mean ‘allowCredentials’?
 1126 |                      if (this->corsConfig_.allow_credentials) {
      |                                            ^~~~~~~~~~~~~~~~~
      |                                            allowCredentials
/home/<USER>/database-service-build/src/api/api_server.cpp:1134:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1134 |                 response_struct.statusCode = 500;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:1067:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
 1067 |         [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
 1068 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
 1069 |             try {
      |             ~~~~~
 1070 |                 auto auth_header_it = request.headers.find("Authorization");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1071 |                 if (auth_header_it == request.headers.end()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1072 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1073 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1074 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1075 |                         {"message", "Authentication required. Missing Authorization header."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1076 |                     }).dump();
      |                     ~~~~~~~~~~
 1077 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1078 |                     return std::unexpected("Missing Authorization header");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1079 |                 }
      |                 ~
 1080 | 
      |          
 1081 |                 const std::string& auth_header = auth_header_it->second;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1082 |                 if (auth_header.rfind("Bearer ", 0) != 0) { // Check if starts with "Bearer "
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1083 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1084 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1085 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1086 |                         {"message", "Invalid authentication format. Expected Bearer token."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1087 |                     }).dump();
      |                     ~~~~~~~~~~
 1088 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1089 |                     return std::unexpected("Invalid token format, not Bearer");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1090 |                 }
      |                 ~
 1091 | 
      |          
 1092 |                 std::string token = auth_header.substr(7); // Extract token part
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1093 |                 auto validation_result = securityManager_->validateToken(token);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1094 |                 if (!validation_result) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~
 1095 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1096 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1097 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1098 |                         {"message", validation_result.error().message}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1099 |                     }).dump();
      |                     ~~~~~~~~~~
 1100 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1101 |                     return std::unexpected("Token validation failed: " + validation_result.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1102 |                 }
      |                 ~
 1103 | 
      |          
 1104 |                 // Token is valid, username is in validation_result.value()
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1105 |                 std::string username = *validation_result;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1106 |                 auto invalidation_result = securityManager_->invalidateTokens(username);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1107 | 
      |          
 1108 |                 if (!invalidation_result) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1109 |                     response_struct.statusCode = 500;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1110 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1111 |                         {"error", "Internal Server Error"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1112 |                         {"message", invalidation_result.error().message}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1113 |                     }).dump();
      |                     ~~~~~~~~~~
 1114 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1115 |                     return std::unexpected("Failed to invalidate tokens: " + invalidation_result.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1116 |                 }
      |                 ~
 1117 | 
      |          
 1118 |                 response_struct.statusCode = 200;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1119 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1120 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1121 |                     {"message", "Logout successful"}
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1122 |                 }).dump();
      |                 ~~~~~~~~~~
 1123 | 
      |          
 1124 |                 if (this->corsConfig_.enabled) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1125 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1126 |                      if (this->corsConfig_.allow_credentials) {
      |                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1127 |                         response_struct.headers["Access-Control-Allow-Credentials"] = "true";
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1128 |                     }
      |                     ~
 1129 |                 }
      |                 ~
 1130 |                 return response_struct;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
 1131 | 
      |          
 1132 |             } catch (const std::exception& e) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1133 |                 utils::Logger::error(std::format("Exception in /api/auth/logout: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1134 |                 response_struct.statusCode = 500;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1135 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1136 |                     {"error", "Internal Server Error"},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1137 |                     {"message", e.what()}
      |                     ~~~~~~~~~~~~~~~~~~~~~
 1138 |                 }).dump();
      |                 ~~~~~~~~~~
 1139 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1140 |                 return std::unexpected(std::string("Exception: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1141 |             }
      |             ~
 1142 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:1153:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1153 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1164:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1164 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1176:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1176 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1187:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1187 |                     response_struct.statusCode = 500; // Or 401 if user info is critical for auth context
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1199:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1199 |                     response_struct.statusCode = 403;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1210:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1210 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1220:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1220 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1237:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1237 |                     response_struct.statusCode = 500;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1246:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1246 |                 response_struct.statusCode = 200;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1253:96: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
 1253 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                ^~~~~~~~~~~~~~~
      |                                                                                                allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:1253:146: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
 1253 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                                                                  ^~~~~~~~~~~~~~~
      |                                                                                                                                                  allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:1254:43: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allow_credentials’; did you mean ‘allowCredentials’?
 1254 |                     if (this->corsConfig_.allow_credentials) {
      |                                           ^~~~~~~~~~~~~~~~~
      |                                           allowCredentials
/home/<USER>/database-service-build/src/api/api_server.cpp:1262:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1262 |                 response_struct.statusCode = 400;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1271:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1271 |                 response_struct.statusCode = 500;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:1147:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
 1147 |         [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
 1148 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
 1149 |             try {
      |             ~~~~~
 1150 |                 // Validate token and check admin permission
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1151 |                 auto auth_header_it = request.headers.find("Authorization");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1152 |                 if (auth_header_it == request.headers.end()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1153 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1154 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1155 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1156 |                         {"message", "Authentication required. Missing Authorization header."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1157 |                     }).dump();
      |                     ~~~~~~~~~~
 1158 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1159 |                     return std::unexpected("Missing Authorization header");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1160 |                 }
      |                 ~
 1161 | 
      |          
 1162 |                 const std::string& auth_header = auth_header_it->second;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1163 |                 if (auth_header.rfind("Bearer ", 0) != 0) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1164 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1165 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1166 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1167 |                         {"message", "Invalid authentication format. Expected Bearer token."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1168 |                     }).dump();
      |                     ~~~~~~~~~~
 1169 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1170 |                     return std::unexpected("Invalid token format, not Bearer");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1171 |                 }
      |                 ~
 1172 | 
      |          
 1173 |                 std::string token = auth_header.substr(7);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1174 |                 auto validation_result = securityManager_->validateToken(token);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1175 |                 if (!validation_result) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~
 1176 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1177 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1178 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1179 |                         {"message", validation_result.error().message}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1180 |                     }).dump();
      |                     ~~~~~~~~~~
 1181 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1182 |                     return std::unexpected("Token validation failed: " + validation_result.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1183 |                 }
      |                 ~
 1184 | 
      |          
 1185 |                 auto user_info_expected = securityManager_->getUserInfo(*validation_result);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1186 |                 if (!user_info_expected) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1187 |                     response_struct.statusCode = 500; // Or 401 if user info is critical for auth context
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1188 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1189 |                         {"error", "Internal Server Error"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1190 |                         {"message", "Failed to retrieve user information after token validation."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1191 |                     }).dump();
      |                     ~~~~~~~~~~
 1192 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1193 |                     return std::unexpected("Failed to retrieve user info: " + user_info_expected.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1194 |                 }
      |                 ~
 1195 |                 const auto& user_info_map = *user_info_expected;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1196 |                 bool is_admin = user_info_map.count("is_admin") && (user_info_map.at("is_admin") == "t" || user_info_map.at("is_admin") == "true");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1197 | 
      |          
 1198 |                 if (!is_admin) {
      |                 ~~~~~~~~~~~~~~~~
 1199 |                     response_struct.statusCode = 403;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1200 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1201 |                         {"error", "Forbidden"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~
 1202 |                         {"message", "User does not have admin privileges to store credentials."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1203 |                     }).dump();
      |                     ~~~~~~~~~~
 1204 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1205 |                     return std::unexpected("User is not admin");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1206 |                 }
      |                 ~
 1207 | 
      |          
 1208 |                 // Parse request body
      |                 ~~~~~~~~~~~~~~~~~~~~~
 1209 |                 if (request.body.empty()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1210 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1211 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1212 |                         {"error", "Bad Request"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~
 1213 |                         {"message", "Request body is empty"}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1214 |                         }).dump();
      |                         ~~~~~~~~~~
 1215 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1216 |                     return std::unexpected("Request body is empty");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1217 |                 }
      |                 ~
 1218 |                 nlohmann::json request_json = nlohmann::json::parse(request.body);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1219 |                 if (!request_json.contains("key") || !request_json.contains("value")) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1220 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1221 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1222 |                         {"error", "Bad Request"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~
 1223 |                         {"message", "'key' and 'value' are required in the request body."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1224 |                     }).dump();
      |                     ~~~~~~~~~~
 1225 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1226 |                     return std::unexpected("Missing 'key' or 'value' in request body");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1227 |                 }
      |                 ~
 1228 | 
      |          
 1229 |                 std::string key = request_json["key"].get<std::string>();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1230 |                 std::string value = request_json["value"].get<std::string>();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1231 | 
      |          
 1232 |                 // Store credential using CredentialStore singleton
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1233 |                 auto& credential_store = security::CredentialStore::getInstance();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1234 |                 // Assuming storeCredential might throw or return a more detailed error in future.
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1235 |                 // For now, it returns bool.
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1236 |                 if (!credential_store.storeCredential(key, value)) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1237 |                     response_struct.statusCode = 500;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1238 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1239 |                         {"error", "Internal Server Error"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1240 |                         {"message", "Failed to store credential."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1241 |                     }).dump();
      |                     ~~~~~~~~~~
 1242 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1243 |                     return std::unexpected("CredentialStore failed to store credential.");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1244 |                 }
      |                 ~
 1245 | 
      |          
 1246 |                 response_struct.statusCode = 200;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1247 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1248 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1249 |                     {"message", "Credential stored successfully"}
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1250 |                 }).dump();
      |                 ~~~~~~~~~~
 1251 | 
      |          
 1252 |                 if (this->corsConfig_.enabled) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1253 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1254 |                     if (this->corsConfig_.allow_credentials) {
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1255 |                         response_struct.headers["Access-Control-Allow-Credentials"] = "true";
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1256 |                     }
      |                     ~
 1257 |                 }
      |                 ~
 1258 |                 return response_struct;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
 1259 | 
      |          
 1260 |             } catch (const nlohmann::json::parse_error& e) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1261 |                 utils::Logger::error(std::format("JSON parse error in /api/credentials/store: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1262 |                 response_struct.statusCode = 400;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1263 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1264 |                     {"error", "Bad Request"},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~
 1265 |                     {"message", std::string("Invalid JSON format: ") + e.what()}
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1266 |                 }).dump();
      |                 ~~~~~~~~~~
 1267 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1268 |                 return std::unexpected(std::string("Invalid JSON: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1269 |             } catch (const std::exception& e) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1270 |                 utils::Logger::error(std::format("Exception in /api/credentials/store: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1271 |                 response_struct.statusCode = 500;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1272 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1273 |                     {"error", "Internal Server Error"},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1274 |                     {"message", e.what()}
      |                     ~~~~~~~~~~~~~~~~~~~~~
 1275 |                 }).dump();
      |                 ~~~~~~~~~~
 1276 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1277 |                 return std::unexpected(std::string("Exception: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1278 |             }
      |             ~
 1279 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:1289:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1289 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1300:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1300 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1312:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1312 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1323:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1323 |                     response_struct.statusCode = 500;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1335:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1335 |                     response_struct.statusCode = 403;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1346:44: error: ‘parseQueryString’ is not a member of ‘dbservice::utils’
 1346 |                 auto query_params = utils::parseQueryString(request.queryString);
      |                                            ^~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1346:69: error: ‘const struct dbservice::api::ParsedRequest’ has no member named ‘queryString’
 1346 |                 auto query_params = utils::parseQueryString(request.queryString);
      |                                                                     ^~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1349:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1349 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1364:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1364 |                     response_struct.statusCode = 404;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1373:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1373 |                 response_struct.statusCode = 200;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1381:96: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
 1381 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                ^~~~~~~~~~~~~~~
      |                                                                                                allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:1381:146: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
 1381 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                                                                  ^~~~~~~~~~~~~~~
      |                                                                                                                                                  allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:1382:43: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allow_credentials’; did you mean ‘allowCredentials’?
 1382 |                     if (this->corsConfig_.allow_credentials) {
      |                                           ^~~~~~~~~~~~~~~~~
      |                                           allowCredentials
/home/<USER>/database-service-build/src/api/api_server.cpp:1390:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1390 |                 response_struct.statusCode = 500;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:1283:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
 1283 |         [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
 1284 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
 1285 |             try {
      |             ~~~~~
 1286 |                 // Validate token and check admin permission
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1287 |                 auto auth_header_it = request.headers.find("Authorization");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1288 |                 if (auth_header_it == request.headers.end()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1289 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1290 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1291 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1292 |                         {"message", "Authentication required. Missing Authorization header."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1293 |                     }).dump();
      |                     ~~~~~~~~~~
 1294 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1295 |                     return std::unexpected("Missing Authorization header");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1296 |                 }
      |                 ~
 1297 | 
      |          
 1298 |                 const std::string& auth_header = auth_header_it->second;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1299 |                 if (auth_header.rfind("Bearer ", 0) != 0) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1300 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1301 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1302 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1303 |                         {"message", "Invalid authentication format. Expected Bearer token."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1304 |                     }).dump();
      |                     ~~~~~~~~~~
 1305 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1306 |                     return std::unexpected("Invalid token format, not Bearer");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1307 |                 }
      |                 ~
 1308 | 
      |          
 1309 |                 std::string token = auth_header.substr(7);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1310 |                 auto validation_result = securityManager_->validateToken(token);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1311 |                 if (!validation_result) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~
 1312 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1313 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1314 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1315 |                         {"message", validation_result.error().message}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1316 |                     }).dump();
      |                     ~~~~~~~~~~
 1317 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1318 |                     return std::unexpected("Token validation failed: " + validation_result.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1319 |                 }
      |                 ~
 1320 | 
      |          
 1321 |                 auto user_info_expected = securityManager_->getUserInfo(*validation_result);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1322 |                 if (!user_info_expected) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1323 |                     response_struct.statusCode = 500;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1324 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1325 |                         {"error", "Internal Server Error"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1326 |                         {"message", "Failed to retrieve user information after token validation."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1327 |                     }).dump();
      |                     ~~~~~~~~~~
 1328 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1329 |                     return std::unexpected("Failed to retrieve user info: " + user_info_expected.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1330 |                 }
      |                 ~
 1331 |                 const auto& user_info_map = *user_info_expected;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1332 |                 bool is_admin = user_info_map.count("is_admin") && (user_info_map.at("is_admin") == "t" || user_info_map.at("is_admin") == "true");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1333 | 
      |          
 1334 |                 if (!is_admin) {
      |                 ~~~~~~~~~~~~~~~~
 1335 |                     response_struct.statusCode = 403;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1336 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1337 |                         {"error", "Forbidden"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~
 1338 |                         {"message", "User does not have admin privileges to get credentials."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1339 |                     }).dump();
      |                     ~~~~~~~~~~
 1340 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1341 |                     return std::unexpected("User is not admin");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1342 |                 }
      |                 ~
 1343 | 
      |          
 1344 |                 // Parse query parameters from request.queryString
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1345 |                 std::string key_to_find;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~
 1346 |                 auto query_params = utils::parseQueryString(request.queryString);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1347 |                 auto key_it = query_params.find("key");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1348 |                 if (key_it == query_params.end() || key_it->second.empty()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1349 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1350 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1351 |                         {"error", "Bad Request"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~
 1352 |                         {"message", "'key' query parameter is required."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1353 |                     }).dump();
      |                     ~~~~~~~~~~
 1354 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1355 |                     return std::unexpected("Missing 'key' query parameter");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1356 |                 }
      |                 ~
 1357 |                 key_to_find = key_it->second;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1358 | 
      |          
 1359 |                 // Get credential
      |                 ~~~~~~~~~~~~~~~~~
 1360 |                 auto& credential_store = security::CredentialStore::getInstance();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1361 |                 std::string value = credential_store.getCredential(key_to_find);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1362 | 
      |          
 1363 |                 if (value.empty()) { // Assuming empty string means not found
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1364 |                     response_struct.statusCode = 404;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1365 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1366 |                         {"error", "Not Found"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~
 1367 |                         {"message", "Credential not found for the given key."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1368 |                     }).dump();
      |                     ~~~~~~~~~~
 1369 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1370 |                     return std::unexpected("Credential not found");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1371 |                 }
      |                 ~
 1372 | 
      |          
 1373 |                 response_struct.statusCode = 200;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1374 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1375 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1376 |                     {"key", key_to_find},
      |                     ~~~~~~~~~~~~~~~~~~~~~
 1377 |                     {"value", value}
      |                     ~~~~~~~~~~~~~~~~
 1378 |                 }).dump();
      |                 ~~~~~~~~~~
 1379 | 
      |          
 1380 |                 if (this->corsConfig_.enabled) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1381 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1382 |                     if (this->corsConfig_.allow_credentials) {
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1383 |                         response_struct.headers["Access-Control-Allow-Credentials"] = "true";
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1384 |                     }
      |                     ~
 1385 |                 }
      |                 ~
 1386 |                 return response_struct;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
 1387 | 
      |          
 1388 |             } catch (const std::exception& e) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1389 |                 utils::Logger::error(std::format("Exception in /api/credentials/get: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1390 |                 response_struct.statusCode = 500;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1391 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1392 |                     {"error", "Internal Server Error"},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1393 |                     {"message", e.what()}
      |                     ~~~~~~~~~~~~~~~~~~~~~
 1394 |                 }).dump();
      |                 ~~~~~~~~~~
 1395 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1396 |                 return std::unexpected(std::string("Exception: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1397 |             }
      |             ~
 1398 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:1408:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1408 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1419:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1419 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1431:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1431 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1442:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1442 |                     response_struct.statusCode = 500;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1454:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1454 |                     response_struct.statusCode = 403;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1465:44: error: ‘parseQueryString’ is not a member of ‘dbservice::utils’
 1465 |                 auto query_params = utils::parseQueryString(request.queryString);
      |                                            ^~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1465:69: error: ‘const struct dbservice::api::ParsedRequest’ has no member named ‘queryString’
 1465 |                 auto query_params = utils::parseQueryString(request.queryString);
      |                                                                     ^~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1468:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1468 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1483:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1483 |                     response_struct.statusCode = 404; // Or 500 if failure is unexpected
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1492:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1492 |                 response_struct.statusCode = 200;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1499:96: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
 1499 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                ^~~~~~~~~~~~~~~
      |                                                                                                allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:1499:146: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
 1499 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                                                                  ^~~~~~~~~~~~~~~
      |                                                                                                                                                  allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:1500:43: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allow_credentials’; did you mean ‘allowCredentials’?
 1500 |                     if (this->corsConfig_.allow_credentials) {
      |                                           ^~~~~~~~~~~~~~~~~
      |                                           allowCredentials
/home/<USER>/database-service-build/src/api/api_server.cpp:1508:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1508 |                 response_struct.statusCode = 500;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:1402:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
 1402 |         [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
 1403 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
 1404 |             try {
      |             ~~~~~
 1405 |                 // Validate token and check admin permission
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1406 |                 auto auth_header_it = request.headers.find("Authorization");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1407 |                 if (auth_header_it == request.headers.end()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1408 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1409 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1410 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1411 |                         {"message", "Authentication required. Missing Authorization header."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1412 |                     }).dump();
      |                     ~~~~~~~~~~
 1413 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1414 |                     return std::unexpected("Missing Authorization header");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1415 |                 }
      |                 ~
 1416 | 
      |          
 1417 |                 const std::string& auth_header = auth_header_it->second;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1418 |                 if (auth_header.rfind("Bearer ", 0) != 0) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1419 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1420 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1421 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1422 |                         {"message", "Invalid authentication format. Expected Bearer token."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1423 |                     }).dump();
      |                     ~~~~~~~~~~
 1424 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1425 |                     return std::unexpected("Invalid token format, not Bearer");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1426 |                 }
      |                 ~
 1427 | 
      |          
 1428 |                 std::string token = auth_header.substr(7);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1429 |                 auto validation_result = securityManager_->validateToken(token);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1430 |                 if (!validation_result) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~
 1431 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1432 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1433 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1434 |                         {"message", validation_result.error().message}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1435 |                     }).dump();
      |                     ~~~~~~~~~~
 1436 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1437 |                     return std::unexpected("Token validation failed: " + validation_result.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1438 |                 }
      |                 ~
 1439 | 
      |          
 1440 |                 auto user_info_expected = securityManager_->getUserInfo(*validation_result);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1441 |                  if (!user_info_expected) {
      |                  ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1442 |                     response_struct.statusCode = 500;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1443 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1444 |                         {"error", "Internal Server Error"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1445 |                         {"message", "Failed to retrieve user information after token validation."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1446 |                     }).dump();
      |                     ~~~~~~~~~~
 1447 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1448 |                     return std::unexpected("Failed to retrieve user info: " + user_info_expected.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1449 |                 }
      |                 ~
 1450 |                 const auto& user_info_map = *user_info_expected;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1451 |                 bool is_admin = user_info_map.count("is_admin") && (user_info_map.at("is_admin") == "t" || user_info_map.at("is_admin") == "true");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1452 | 
      |          
 1453 |                 if (!is_admin) {
      |                 ~~~~~~~~~~~~~~~~
 1454 |                     response_struct.statusCode = 403;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1455 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1456 |                         {"error", "Forbidden"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~
 1457 |                         {"message", "User does not have admin privileges to remove credentials."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1458 |                     }).dump();
      |                     ~~~~~~~~~~
 1459 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1460 |                     return std::unexpected("User is not admin");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1461 |                 }
      |                 ~
 1462 | 
      |          
 1463 |                 // Parse query parameters from request.queryString
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1464 |                 std::string key_to_remove;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1465 |                 auto query_params = utils::parseQueryString(request.queryString);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1466 |                 auto key_it = query_params.find("key");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1467 |                 if (key_it == query_params.end() || key_it->second.empty()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1468 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1469 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1470 |                         {"error", "Bad Request"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~
 1471 |                         {"message", "'key' query parameter is required."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1472 |                     }).dump();
      |                     ~~~~~~~~~~
 1473 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1474 |                     return std::unexpected("Missing 'key' query parameter");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1475 |                 }
      |                 ~
 1476 |                 key_to_remove = key_it->second;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1477 | 
      |          
 1478 |                 // Remove credential
      |                 ~~~~~~~~~~~~~~~~~~~~
 1479 |                 auto& credential_store = security::CredentialStore::getInstance();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1480 |                 bool success = credential_store.removeCredential(key_to_remove);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1481 | 
      |          
 1482 |                 if (!success) {
      |                 ~~~~~~~~~~~~~~~
 1483 |                     response_struct.statusCode = 404; // Or 500 if failure is unexpected
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1484 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1485 |                         {"error", "Not Found"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~
 1486 |                         {"message", "Credential not found or failed to remove."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1487 |                     }).dump();
      |                     ~~~~~~~~~~
 1488 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1489 |                     return std::unexpected("Credential not found or failed to remove");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1490 |                 }
      |                 ~
 1491 | 
      |          
 1492 |                 response_struct.statusCode = 200;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1493 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1494 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1495 |                     {"message", "Credential removed successfully"}
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1496 |                 }).dump();
      |                 ~~~~~~~~~~
 1497 | 
      |          
 1498 |                 if (this->corsConfig_.enabled) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1499 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1500 |                     if (this->corsConfig_.allow_credentials) {
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1501 |                         response_struct.headers["Access-Control-Allow-Credentials"] = "true";
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1502 |                     }
      |                     ~
 1503 |                 }
      |                 ~
 1504 |                 return response_struct;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
 1505 | 
      |          
 1506 |             } catch (const std::exception& e) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1507 |                 utils::Logger::error(std::format("Exception in /api/credentials/remove: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1508 |                 response_struct.statusCode = 500;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1509 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1510 |                     {"error", "Internal Server Error"},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1511 |                     {"message", e.what()}
      |                     ~~~~~~~~~~~~~~~~~~~~~
 1512 |                 }).dump();
      |                 ~~~~~~~~~~
 1513 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1514 |                 return std::unexpected(std::string("Exception: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1515 |             }
      |             ~
 1516 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:1527:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1527 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1538:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1538 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1550:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1550 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1562:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1562 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1576:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1576 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1587:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1587 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1600:41: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1600 |                         response_struct.statusCode = 400;
      |                                         ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1611:44: error: ‘warn’ is not a member of ‘dbservice::utils::Logger’
 1611 |                             utils::Logger::warn("/api/query: a parameter was not a string, attempting dump.");
      |                                            ^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1623:123: error: ‘class std::__cxx11::basic_string<char>’ has no member named ‘message’
 1623 |                     utils::Logger::error(std::format("Database query error in /api/query: {}", db_result_expected.error().message));
      |                                                                                                                           ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1624:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1624 |                     response_struct.statusCode = 500; // Or map DatabaseError type to HTTP status
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1627:64: error: ‘class std::__cxx11::basic_string<char>’ has no member named ‘message’
 1627 |                         {"message", db_result_expected.error().message}
      |                                                                ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1628:22: error: no matching function for call to ‘nlohmann::json_abi_v3_11_3::basic_json<>::basic_json(<brace-enclosed initializer list>)’
 1628 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1141:5: note: candidate: ‘template<class JsonRef, typename std::enable_if<nlohmann::json_abi_v3_11_3::detail::conjunction<nlohmann::json_abi_v3_11_3::detail::is_json_ref<JsonRef>, std::is_same<typename JsonRef::value_type, nlohmann::json_abi_v3_11_3::basic_json<std::map, std::vector, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, bool, long int, long unsigned int, double, std::allocator, nlohmann::json_abi_v3_11_3::adl_serializer, std::vector<unsigned char, std::allocator<unsigned char> >, void> > >::value, int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const JsonRef&) [with typename std::enable_if<nlohmann::json_abi_v3_11_3::detail::conjunction<nlohmann::json_abi_v3_11_3::detail::is_json_ref<JsonRef>, std::is_same<typename JsonRef::value_type, nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value, int>::type <anonymous> = JsonRef; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1141 |     basic_json(const JsonRef& ref) : basic_json(ref.moved_or_copied()) {}
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1141:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:1628:22: note:   couldn’t deduce template parameter ‘JsonRef’
 1628 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1032:5: note: candidate: ‘template<class InputIT, typename std::enable_if<(std::is_same<IterImpl, nlohmann::json_abi_v3_11_3::detail::iter_impl<nlohmann::json_abi_v3_11_3::basic_json<> > >::value || std::is_same<IterImpl, nlohmann::json_abi_v3_11_3::detail::iter_impl<const nlohmann::json_abi_v3_11_3::basic_json<> > >::value), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(InputIT, InputIT) [with typename std::enable_if<(std::is_same<InputIT, nlohmann::json_abi_v3_11_3::detail::iter_impl<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value || std::is_same<InputIT, nlohmann::json_abi_v3_11_3::detail::iter_impl<const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value), int>::type <anonymous> = InputIT; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1032 |     basic_json(InputIT first, InputIT last)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1032:5: note:   candidate expects 2 arguments, 1 provided
/usr/include/nlohmann/json.hpp:849:5: note: candidate: ‘template<class BasicJsonType, typename std::enable_if<(nlohmann::json_abi_v3_11_3::detail::is_basic_json<BasicJsonType>::value && (! std::is_same<nlohmann::json_abi_v3_11_3::basic_json<>, BasicJsonType>::value)), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const BasicJsonType&) [with typename std::enable_if<(nlohmann::json_abi_v3_11_3::detail::is_basic_json<BasicJsonType>::value && (! std::is_same<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>, BasicJsonType>::value)), int>::type <anonymous> = BasicJsonType; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
  849 |     basic_json(const BasicJsonType& val)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:849:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:1628:22: note:   couldn’t deduce template parameter ‘BasicJsonType’
 1628 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:835:5: note: candidate: ‘template<class CompatibleType, class U, typename std::enable_if<((! nlohmann::json_abi_v3_11_3::detail::is_basic_json<T>::value) && nlohmann::json_abi_v3_11_3::detail::is_compatible_type<nlohmann::json_abi_v3_11_3::basic_json<>, U>::value), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(CompatibleType&&) [with U = CompatibleType; typename std::enable_if<((! nlohmann::json_abi_v3_11_3::detail::is_basic_json<U>::value) && nlohmann::json_abi_v3_11_3::detail::is_compatible_type<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>, U>::value), int>::type <anonymous> = U; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
  835 |     basic_json(CompatibleType && val) noexcept(noexcept( // NOLINT(bugprone-forwarding-reference-overload,bugprone-exception-escape)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:835:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:1628:22: note:   couldn’t deduce template parameter ‘CompatibleType’
 1628 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1214:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1214 |     basic_json(basic_json&& other) noexcept
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1214:29: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>&&’
 1214 |     basic_json(basic_json&& other) noexcept
      |                ~~~~~~~~~~~~~^~~~~
/usr/include/nlohmann/json.hpp:1145:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1145 |     basic_json(const basic_json& other)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1145:34: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘const nlohmann::json_abi_v3_11_3::basic_json<>&’
 1145 |     basic_json(const basic_json& other)
      |                ~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/nlohmann/json.hpp:1020:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(size_type, const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; size_type = long unsigned int]’
 1020 |     basic_json(size_type cnt, const basic_json& val):
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1020:5: note:   candidate expects 2 arguments, 1 provided
/usr/include/nlohmann/json.hpp:902:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(initializer_list_t, bool, value_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; initializer_list_t = std::initializer_list<nlohmann::json_abi_v3_11_3::detail::json_ref<nlohmann::json_abi_v3_11_3::basic_json<> > >; value_t = nlohmann::json_abi_v3_11_3::detail::value_t]’
  902 |     basic_json(initializer_list_t init,
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:902:35: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>::initializer_list_t’ {aka ‘std::initializer_list<nlohmann::json_abi_v3_11_3::detail::json_ref<nlohmann::json_abi_v3_11_3::basic_json<> > >’}
  902 |     basic_json(initializer_list_t init,
      |                ~~~~~~~~~~~~~~~~~~~^~~~
/usr/include/nlohmann/json.hpp:823:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(std::nullptr_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; std::nullptr_t = std::nullptr_t]’
  823 |     basic_json(std::nullptr_t = nullptr) noexcept // NOLINT(bugprone-exception-escape)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:823:16: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘std::nullptr_t’
  823 |     basic_json(std::nullptr_t = nullptr) noexcept // NOLINT(bugprone-exception-escape)
      |                ^~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/nlohmann/json.hpp:815:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(value_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; value_t = nlohmann::json_abi_v3_11_3::detail::value_t]’
  815 |     basic_json(const value_t v)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:815:30: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>::value_t’ {aka ‘nlohmann::json_abi_v3_11_3::detail::value_t’}
  815 |     basic_json(const value_t v)
      |                ~~~~~~~~~~~~~~^
/home/<USER>/database-service-build/src/api/api_server.cpp:1630:109: error: ‘class std::__cxx11::basic_string<char>’ has no member named ‘message’
 1630 |                     return std::unexpected("Database query execution failed: " + db_result_expected.error().message);
      |                                                                                                             ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1643:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1643 |                 response_struct.statusCode = 200;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1648:96: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
 1648 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                ^~~~~~~~~~~~~~~
      |                                                                                                allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:1648:146: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
 1648 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                                                                  ^~~~~~~~~~~~~~~
      |                                                                                                                                                  allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:1649:43: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allow_credentials’; did you mean ‘allowCredentials’?
 1649 |                     if (this->corsConfig_.allow_credentials) {
      |                                           ^~~~~~~~~~~~~~~~~
      |                                           allowCredentials
/home/<USER>/database-service-build/src/api/api_server.cpp:1657:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1657 |                 response_struct.statusCode = 500;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:1521:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
 1521 |         [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
 1522 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
 1523 |             try {
      |             ~~~~~
 1524 |                 // 1. Token Validation
      |                 ~~~~~~~~~~~~~~~~~~~~~~
 1525 |                 auto auth_header_it = request.headers.find("Authorization");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1526 |                 if (auth_header_it == request.headers.end()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1527 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1528 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1529 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1530 |                         {"message", "Authentication required. Missing Authorization header."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1531 |                     }).dump();
      |                     ~~~~~~~~~~
 1532 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1533 |                     return std::unexpected("Missing Authorization header");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1534 |                 }
      |                 ~
 1535 | 
      |          
 1536 |                 const std::string& auth_header = auth_header_it->second;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1537 |                 if (auth_header.rfind("Bearer ", 0) != 0) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1538 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1539 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1540 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1541 |                         {"message", "Invalid authentication format. Expected Bearer token."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1542 |                     }).dump();
      |                     ~~~~~~~~~~
 1543 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1544 |                     return std::unexpected("Invalid token format, not Bearer");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1545 |                 }
      |                 ~
 1546 | 
      |          
 1547 |                 std::string token = auth_header.substr(7);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1548 |                 auto validation_result = securityManager_->validateToken(token);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1549 |                 if (!validation_result) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~
 1550 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1551 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1552 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1553 |                         {"message", validation_result.error().message}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1554 |                     }).dump();
      |                     ~~~~~~~~~~
 1555 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1556 |                     return std::unexpected("Token validation failed: " + validation_result.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1557 |                 }
      |                 ~
 1558 |                 // Token is valid, proceed. Username from *validation_result can be used for logging/auditing if needed.
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1559 | 
      |          
 1560 |                 // 2. Parse request body
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~
 1561 |                 if (request.body.empty()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1562 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1563 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1564 |                         {"error", "Bad Request"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~
 1565 |                         {"message", "Request body is empty."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1566 |                     }).dump();
      |                     ~~~~~~~~~~
 1567 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1568 |                     return std::unexpected("Request body is empty");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1569 |                 }
      |                 ~
 1570 | 
      |          
 1571 |                 nlohmann::json request_json;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1572 |                 try {
      |                 ~~~~~
 1573 |                     request_json = nlohmann::json::parse(request.body);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1574 |                 } catch (const nlohmann::json::parse_error& e) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1575 |                     utils::Logger::error(std::format("JSON parse error in /api/query: {}", e.what()));
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1576 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1577 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1578 |                         {"error", "Bad Request"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~
 1579 |                         {"message", std::string("Invalid JSON format: ") + e.what()}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1580 |                     }).dump();
      |                     ~~~~~~~~~~
 1581 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1582 |                     return std::unexpected(std::string("Invalid JSON: ") + e.what());
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1583 |                 }
      |                 ~
 1584 | 
      |          
 1585 |                 // 3. Extract query and parameters
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1586 |                 if (!request_json.contains("query") || !request_json["query"].is_string()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1587 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1588 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1589 |                         {"error", "Bad Request"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~
 1590 |                         {"message", "'query' field (string) is required in the request body."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1591 |                     }).dump();
      |                     ~~~~~~~~~~
 1592 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1593 |                     return std::unexpected("Missing or invalid 'query' field");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1594 |                 }
      |                 ~
 1595 |                 std::string query_str = request_json["query"].get<std::string>();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1596 | 
      |          
 1597 |                 std::vector<std::string> params_vec;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1598 |                 if (request_json.contains("params")) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1599 |                     if (!request_json["params"].is_array()) {
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1600 |                         response_struct.statusCode = 400;
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1601 |                         response_struct.body = nlohmann::json({
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1602 |                             {"error", "Bad Request"},
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~
 1603 |                             {"message", "'params' field must be an array if provided."}
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1604 |                         }).dump();
      |                         ~~~~~~~~~~
 1605 |                         response_struct.headers["Content-Type"] = "application/json";
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1606 |                         return std::unexpected("'params' field is not an array");
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1607 |                     }
      |                     ~
 1608 |                     for (const auto& param_item : request_json["params"]) {
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1609 |                         if (!param_item.is_string()) {
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1610 |                              // Attempt to dump non-string params, or enforce string type strictly
      |                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1611 |                             utils::Logger::warn("/api/query: a parameter was not a string, attempting dump.");
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1612 |                             params_vec.push_back(param_item.dump());
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1613 |                         } else {
      |                         ~~~~~~~~
 1614 |                             params_vec.push_back(param_item.get<std::string>());
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1615 |                         }
      |                         ~
 1616 |                     }
      |                     ~
 1617 |                 }
      |                 ~
 1618 | 
      |          
 1619 |                 // 4. Execute query using ConnectionManager
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1620 |                 auto db_result_expected = connectionManager_->executeQuery(query_str, params_vec);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1621 | 
      |          
 1622 |                 if (!db_result_expected) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1623 |                     utils::Logger::error(std::format("Database query error in /api/query: {}", db_result_expected.error().message));
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1624 |                     response_struct.statusCode = 500; // Or map DatabaseError type to HTTP status
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1625 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1626 |                         {"error", "Database Query Failed"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1627 |                         {"message", db_result_expected.error().message}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1628 |                     }).dump();
      |                     ~~~~~~~~~~
 1629 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1630 |                     return std::unexpected("Database query execution failed: " + db_result_expected.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1631 |                 }
      |                 ~
 1632 | 
      |          
 1633 |                 // 5. Format successful response
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1634 |                 const auto& result_data = *db_result_expected;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1635 |                 nlohmann::json response_body_json = {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1636 |                     {"rows_affected", result_data.size()}, // Changed from "rows" to "rows_affected" for clarity with SELECT vs other DML
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1637 |                     {"data", nlohmann::json::array()}
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1638 |                 };
      |                 ~~
 1639 |                 for (const auto& row_map : result_data) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1640 |                     response_body_json["data"].push_back(row_map);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1641 |                 }
      |                 ~
 1642 | 
      |          
 1643 |                 response_struct.statusCode = 200;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1644 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1645 |                 response_struct.body = response_body_json.dump();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1646 | 
      |          
 1647 |                 if (this->corsConfig_.enabled) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1648 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1649 |                     if (this->corsConfig_.allow_credentials) {
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1650 |                         response_struct.headers["Access-Control-Allow-Credentials"] = "true";
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1651 |                     }
      |                     ~
 1652 |                 }
      |                 ~
 1653 |                 return response_struct;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
 1654 | 
      |          
 1655 |             } catch (const std::exception& e) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1656 |                 utils::Logger::error(std::format("Generic exception in /api/query: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1657 |                 response_struct.statusCode = 500;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1658 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1659 |                     {"error", "Internal Server Error"},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1660 |                     {"message", e.what()}
      |                     ~~~~~~~~~~~~~~~~~~~~~
 1661 |                 }).dump();
      |                 ~~~~~~~~~~
 1662 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1663 |                 return std::unexpected(std::string("Exception: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1664 |             }
      |             ~
 1665 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:1676:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1676 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1687:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1687 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1699:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1699 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1711:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1711 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1725:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1725 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1736:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1736 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1749:41: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1749 |                         response_struct.statusCode = 400;
      |                                         ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1759:44: error: ‘warn’ is not a member of ‘dbservice::utils::Logger’
 1759 |                             utils::Logger::warn("/api/execute: a parameter was not a string, attempting dump.");
      |                                            ^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1771:129: error: ‘class std::__cxx11::basic_string<char>’ has no member named ‘message’
 1771 |                     utils::Logger::error(std::format("Database non-query error in /api/execute: {}", db_result_expected.error().message));
      |                                                                                                                                 ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1772:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1772 |                     response_struct.statusCode = 500; // Or map DatabaseError type to HTTP status
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1775:64: error: ‘class std::__cxx11::basic_string<char>’ has no member named ‘message’
 1775 |                         {"message", db_result_expected.error().message}
      |                                                                ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1776:22: error: no matching function for call to ‘nlohmann::json_abi_v3_11_3::basic_json<>::basic_json(<brace-enclosed initializer list>)’
 1776 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1141:5: note: candidate: ‘template<class JsonRef, typename std::enable_if<nlohmann::json_abi_v3_11_3::detail::conjunction<nlohmann::json_abi_v3_11_3::detail::is_json_ref<JsonRef>, std::is_same<typename JsonRef::value_type, nlohmann::json_abi_v3_11_3::basic_json<std::map, std::vector, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, bool, long int, long unsigned int, double, std::allocator, nlohmann::json_abi_v3_11_3::adl_serializer, std::vector<unsigned char, std::allocator<unsigned char> >, void> > >::value, int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const JsonRef&) [with typename std::enable_if<nlohmann::json_abi_v3_11_3::detail::conjunction<nlohmann::json_abi_v3_11_3::detail::is_json_ref<JsonRef>, std::is_same<typename JsonRef::value_type, nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value, int>::type <anonymous> = JsonRef; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1141 |     basic_json(const JsonRef& ref) : basic_json(ref.moved_or_copied()) {}
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1141:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:1776:22: note:   couldn’t deduce template parameter ‘JsonRef’
 1776 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1032:5: note: candidate: ‘template<class InputIT, typename std::enable_if<(std::is_same<IterImpl, nlohmann::json_abi_v3_11_3::detail::iter_impl<nlohmann::json_abi_v3_11_3::basic_json<> > >::value || std::is_same<IterImpl, nlohmann::json_abi_v3_11_3::detail::iter_impl<const nlohmann::json_abi_v3_11_3::basic_json<> > >::value), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(InputIT, InputIT) [with typename std::enable_if<(std::is_same<InputIT, nlohmann::json_abi_v3_11_3::detail::iter_impl<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value || std::is_same<InputIT, nlohmann::json_abi_v3_11_3::detail::iter_impl<const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value), int>::type <anonymous> = InputIT; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1032 |     basic_json(InputIT first, InputIT last)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1032:5: note:   candidate expects 2 arguments, 1 provided
/usr/include/nlohmann/json.hpp:849:5: note: candidate: ‘template<class BasicJsonType, typename std::enable_if<(nlohmann::json_abi_v3_11_3::detail::is_basic_json<BasicJsonType>::value && (! std::is_same<nlohmann::json_abi_v3_11_3::basic_json<>, BasicJsonType>::value)), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const BasicJsonType&) [with typename std::enable_if<(nlohmann::json_abi_v3_11_3::detail::is_basic_json<BasicJsonType>::value && (! std::is_same<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>, BasicJsonType>::value)), int>::type <anonymous> = BasicJsonType; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
  849 |     basic_json(const BasicJsonType& val)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:849:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:1776:22: note:   couldn’t deduce template parameter ‘BasicJsonType’
 1776 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:835:5: note: candidate: ‘template<class CompatibleType, class U, typename std::enable_if<((! nlohmann::json_abi_v3_11_3::detail::is_basic_json<T>::value) && nlohmann::json_abi_v3_11_3::detail::is_compatible_type<nlohmann::json_abi_v3_11_3::basic_json<>, U>::value), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(CompatibleType&&) [with U = CompatibleType; typename std::enable_if<((! nlohmann::json_abi_v3_11_3::detail::is_basic_json<U>::value) && nlohmann::json_abi_v3_11_3::detail::is_compatible_type<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>, U>::value), int>::type <anonymous> = U; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
  835 |     basic_json(CompatibleType && val) noexcept(noexcept( // NOLINT(bugprone-forwarding-reference-overload,bugprone-exception-escape)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:835:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:1776:22: note:   couldn’t deduce template parameter ‘CompatibleType’
 1776 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1214:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1214 |     basic_json(basic_json&& other) noexcept
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1214:29: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>&&’
 1214 |     basic_json(basic_json&& other) noexcept
      |                ~~~~~~~~~~~~~^~~~~
/usr/include/nlohmann/json.hpp:1145:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1145 |     basic_json(const basic_json& other)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1145:34: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘const nlohmann::json_abi_v3_11_3::basic_json<>&’
 1145 |     basic_json(const basic_json& other)
      |                ~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/nlohmann/json.hpp:1020:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(size_type, const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; size_type = long unsigned int]’
 1020 |     basic_json(size_type cnt, const basic_json& val):
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1020:5: note:   candidate expects 2 arguments, 1 provided
/usr/include/nlohmann/json.hpp:902:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(initializer_list_t, bool, value_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; initializer_list_t = std::initializer_list<nlohmann::json_abi_v3_11_3::detail::json_ref<nlohmann::json_abi_v3_11_3::basic_json<> > >; value_t = nlohmann::json_abi_v3_11_3::detail::value_t]’
  902 |     basic_json(initializer_list_t init,
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:902:35: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>::initializer_list_t’ {aka ‘std::initializer_list<nlohmann::json_abi_v3_11_3::detail::json_ref<nlohmann::json_abi_v3_11_3::basic_json<> > >’}
  902 |     basic_json(initializer_list_t init,
      |                ~~~~~~~~~~~~~~~~~~~^~~~
/usr/include/nlohmann/json.hpp:823:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(std::nullptr_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; std::nullptr_t = std::nullptr_t]’
  823 |     basic_json(std::nullptr_t = nullptr) noexcept // NOLINT(bugprone-exception-escape)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:823:16: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘std::nullptr_t’
  823 |     basic_json(std::nullptr_t = nullptr) noexcept // NOLINT(bugprone-exception-escape)
      |                ^~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/nlohmann/json.hpp:815:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(value_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; value_t = nlohmann::json_abi_v3_11_3::detail::value_t]’
  815 |     basic_json(const value_t v)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:815:30: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>::value_t’ {aka ‘nlohmann::json_abi_v3_11_3::detail::value_t’}
  815 |     basic_json(const value_t v)
      |                ~~~~~~~~~~~~~~^
/home/<USER>/database-service-build/src/api/api_server.cpp:1778:113: error: ‘class std::__cxx11::basic_string<char>’ has no member named ‘message’
 1778 |                     return std::unexpected("Database non-query execution failed: " + db_result_expected.error().message);
      |                                                                                                                 ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1783:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1783 |                 response_struct.statusCode = 200;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1790:96: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
 1790 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                ^~~~~~~~~~~~~~~
      |                                                                                                allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:1790:146: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
 1790 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                                                                  ^~~~~~~~~~~~~~~
      |                                                                                                                                                  allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:1791:43: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allow_credentials’; did you mean ‘allowCredentials’?
 1791 |                     if (this->corsConfig_.allow_credentials) {
      |                                           ^~~~~~~~~~~~~~~~~
      |                                           allowCredentials
/home/<USER>/database-service-build/src/api/api_server.cpp:1799:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1799 |                 response_struct.statusCode = 500;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:1670:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
 1670 |         [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
 1671 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
 1672 |             try {
      |             ~~~~~
 1673 |                 // 1. Token Validation
      |                 ~~~~~~~~~~~~~~~~~~~~~~
 1674 |                 auto auth_header_it = request.headers.find("Authorization");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1675 |                 if (auth_header_it == request.headers.end()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1676 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1677 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1678 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1679 |                         {"message", "Authentication required. Missing Authorization header."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1680 |                     }).dump();
      |                     ~~~~~~~~~~
 1681 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1682 |                     return std::unexpected("Missing Authorization header");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1683 |                 }
      |                 ~
 1684 | 
      |          
 1685 |                 const std::string& auth_header = auth_header_it->second;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1686 |                 if (auth_header.rfind("Bearer ", 0) != 0) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1687 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1688 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1689 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1690 |                         {"message", "Invalid authentication format. Expected Bearer token."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1691 |                     }).dump();
      |                     ~~~~~~~~~~
 1692 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1693 |                     return std::unexpected("Invalid token format, not Bearer");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1694 |                 }
      |                 ~
 1695 | 
      |          
 1696 |                 std::string token = auth_header.substr(7);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1697 |                 auto validation_result = securityManager_->validateToken(token);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1698 |                 if (!validation_result) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~
 1699 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1700 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1701 |                         {"error", "Unauthorized"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1702 |                         {"message", validation_result.error().message}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1703 |                     }).dump();
      |                     ~~~~~~~~~~
 1704 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1705 |                     return std::unexpected("Token validation failed: " + validation_result.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1706 |                 }
      |                 ~
 1707 |                 // Token is valid. Username from *validation_result can be used for logging/auditing.
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1708 | 
      |          
 1709 |                 // 2. Parse request body
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~
 1710 |                 if (request.body.empty()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1711 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1712 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1713 |                         {"error", "Bad Request"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~
 1714 |                         {"message", "Request body is empty."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1715 |                     }).dump();
      |                     ~~~~~~~~~~
 1716 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1717 |                     return std::unexpected("Request body is empty");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1718 |                 }
      |                 ~
 1719 | 
      |          
 1720 |                 nlohmann::json request_json;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1721 |                 try {
      |                 ~~~~~
 1722 |                     request_json = nlohmann::json::parse(request.body);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1723 |                 } catch (const nlohmann::json::parse_error& e) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1724 |                     utils::Logger::error(std::format("JSON parse error in /api/execute: {}", e.what()));
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1725 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1726 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1727 |                         {"error", "Bad Request"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~
 1728 |                         {"message", std::string("Invalid JSON format: ") + e.what()}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1729 |                     }).dump();
      |                     ~~~~~~~~~~
 1730 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1731 |                     return std::unexpected(std::string("Invalid JSON: ") + e.what());
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1732 |                 }
      |                 ~
 1733 | 
      |          
 1734 |                 // 3. Extract statement and parameters
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1735 |                 if (!request_json.contains("statement") || !request_json["statement"].is_string()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1736 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1737 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1738 |                         {"error", "Bad Request"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~
 1739 |                         {"message", "'statement' field (string) is required in the request body."}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1740 |                     }).dump();
      |                     ~~~~~~~~~~
 1741 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1742 |                     return std::unexpected("Missing or invalid 'statement' field");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1743 |                 }
      |                 ~
 1744 |                 std::string statement_str = request_json["statement"].get<std::string>();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1745 | 
      |          
 1746 |                 std::vector<std::string> params_vec;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1747 |                 if (request_json.contains("params")) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1748 |                     if (!request_json["params"].is_array()) {
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1749 |                         response_struct.statusCode = 400;
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1750 |                         response_struct.body = nlohmann::json({
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1751 |                             {"error", "Bad Request"},
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~
 1752 |                             {"message", "'params' field must be an array if provided."}
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1753 |                         }).dump();
      |                         ~~~~~~~~~~
 1754 |                         response_struct.headers["Content-Type"] = "application/json";
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1755 |                         return std::unexpected("'params' field is not an array");
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1756 |                     }
      |                     ~
 1757 |                     for (const auto& param_item : request_json["params"]) {
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1758 |                          if (!param_item.is_string()) {
      |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1759 |                             utils::Logger::warn("/api/execute: a parameter was not a string, attempting dump.");
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1760 |                             params_vec.push_back(param_item.dump());
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1761 |                         } else {
      |                         ~~~~~~~~
 1762 |                             params_vec.push_back(param_item.get<std::string>());
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1763 |                         }
      |                         ~
 1764 |                     }
      |                     ~
 1765 |                 }
      |                 ~
 1766 | 
      |          
 1767 |                 // 4. Execute non-query using ConnectionManager
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1768 |                 auto db_result_expected = connectionManager_->executeNonQuery(statement_str, params_vec);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1769 | 
      |          
 1770 |                 if (!db_result_expected) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1771 |                     utils::Logger::error(std::format("Database non-query error in /api/execute: {}", db_result_expected.error().message));
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1772 |                     response_struct.statusCode = 500; // Or map DatabaseError type to HTTP status
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1773 |                     response_struct.body = nlohmann::json({
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1774 |                         {"error", "Database Execution Failed"},
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1775 |                         {"message", db_result_expected.error().message}
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1776 |                     }).dump();
      |                     ~~~~~~~~~~
 1777 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1778 |                     return std::unexpected("Database non-query execution failed: " + db_result_expected.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1779 |                 }
      |                 ~
 1780 | 
      |          
 1781 |                 // 5. Format successful response
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1782 |                 int affected_rows = *db_result_expected;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1783 |                 response_struct.statusCode = 200;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1784 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1785 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1786 |                     {"affected_rows", affected_rows}
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1787 |                 }).dump();
      |                 ~~~~~~~~~~
 1788 | 
      |          
 1789 |                 if (this->corsConfig_.enabled) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1790 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1791 |                     if (this->corsConfig_.allow_credentials) {
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1792 |                         response_struct.headers["Access-Control-Allow-Credentials"] = "true";
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1793 |                     }
      |                     ~
 1794 |                 }
      |                 ~
 1795 |                 return response_struct;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
 1796 | 
      |          
 1797 |             } catch (const std::exception& e) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1798 |                 utils::Logger::error(std::format("Generic exception in /api/execute: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1799 |                 response_struct.statusCode = 500;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1800 |                 response_struct.body = nlohmann::json({
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1801 |                     {"error", "Internal Server Error"},
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1802 |                     {"message", e.what()}
      |                     ~~~~~~~~~~~~~~~~~~~~~
 1803 |                 }).dump();
      |                 ~~~~~~~~~~
 1804 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1805 |                 return std::unexpected(std::string("Exception: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1806 |             }
      |             ~
 1807 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:1817:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1817 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1825:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1825 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1834:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1834 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1842:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1842 |                     response_struct.statusCode = 500;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1851:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1851 |                     response_struct.statusCode = 403;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1862:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1862 |                 response_struct.statusCode = 501; // Not Implemented
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1867:96: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
 1867 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                ^~~~~~~~~~~~~~~
      |                                                                                                allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:1867:146: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allowed_origins’; did you mean ‘allowedOrigins’?
 1867 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                                                                                                                                                  ^~~~~~~~~~~~~~~
      |                                                                                                                                                  allowedOrigins
/home/<USER>/database-service-build/src/api/api_server.cpp:1868:43: error: ‘struct dbservice::api::CorsConfig’ has no member named ‘allow_credentials’; did you mean ‘allowCredentials’?
 1868 |                     if (this->corsConfig_.allow_credentials) {
      |                                           ^~~~~~~~~~~~~~~~~
      |                                           allowCredentials
/home/<USER>/database-service-build/src/api/api_server.cpp:1876:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1876 |                 response_struct.statusCode = 500;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:1812:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
 1812 |         [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
 1813 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
 1814 |             try {
      |             ~~~~~
 1815 |                 auto auth_header_it = request.headers.find("Authorization");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1816 |                 if (auth_header_it == request.headers.end()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1817 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1818 |                     response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", "Authentication required. Missing Authorization header."}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1819 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1820 |                     return std::unexpected("Missing Authorization header");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1821 |                 }
      |                 ~
 1822 | 
      |          
 1823 |                 const std::string& auth_header = auth_header_it->second;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1824 |                 if (auth_header.rfind("Bearer ", 0) != 0) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1825 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1826 |                     response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", "Invalid authentication format. Expected Bearer token."}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1827 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1828 |                     return std::unexpected("Invalid token format, not Bearer");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1829 |                 }
      |                 ~
 1830 | 
      |          
 1831 |                 std::string token = auth_header.substr(7);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1832 |                 auto validation_result = securityManager_->validateToken(token);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1833 |                 if (!validation_result) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~
 1834 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1835 |                     response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", validation_result.error().message}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1836 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1837 |                     return std::unexpected("Token validation failed: " + validation_result.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1838 |                 }
      |                 ~
 1839 | 
      |          
 1840 |                 auto user_info_expected = securityManager_->getUserInfo(*validation_result);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1841 |                 if (!user_info_expected) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~
 1842 |                     response_struct.statusCode = 500;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1843 |                     response_struct.body = nlohmann::json({{"error", "Internal Server Error"}, {"message", "Failed to retrieve user information after token validation."}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1844 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1845 |                     return std::unexpected("Failed to retrieve user info: " + user_info_expected.error().message);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1846 |                 }
      |                 ~
 1847 |                 const auto& user_info_map = *user_info_expected;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1848 |                 bool is_admin = user_info_map.count("is_admin") && (user_info_map.at("is_admin") == "t" || user_info_map.at("is_admin") == "true");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1849 | 
      |          
 1850 |                 if (!is_admin) {
      |                 ~~~~~~~~~~~~~~~~
 1851 |                     response_struct.statusCode = 403;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1852 |                     response_struct.body = nlohmann::json({{"error", "Forbidden"}, {"message", "User does not have admin privileges to list databases."}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1853 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1854 |                     return std::unexpected("User is not admin");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1855 |                 }
      |                 ~
 1856 | 
      |          
 1857 |                 // TODO: Call connectionManager_->listDatabases()
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1858 |                 // auto dbs_expected = connectionManager_->listDatabases();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1859 |                 // if (!dbs_expected) { /* handle error */ }
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1860 |                 // response_struct.body = nlohmann::json({{"databases", *dbs_expected}}).dump();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1861 | 
      |          
 1862 |                 response_struct.statusCode = 501; // Not Implemented
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1863 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1864 |                 response_struct.body = nlohmann::json({{"message", "List databases endpoint not fully implemented yet."}}).dump();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1865 | 
      |          
 1866 |                 if (this->corsConfig_.enabled) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1867 |                     response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowed_origins.empty() ? "*" : this->corsConfig_.allowed_origins[0];
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1868 |                     if (this->corsConfig_.allow_credentials) {
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1869 |                         response_struct.headers["Access-Control-Allow-Credentials"] = "true";
      |                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1870 |                     }
      |                     ~
 1871 |                 }
      |                 ~
 1872 |                 return response_struct;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
 1873 | 
      |          
 1874 |             } catch (const std::exception& e) {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1875 |                 utils::Logger::error(std::format("Exception in GET /api/databases: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1876 |                 response_struct.statusCode = 500;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1877 |                 response_struct.body = nlohmann::json({{"error", "Internal Server Error"}, {"message", e.what()}}).dump();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1878 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1879 |                 return std::unexpected(std::string("Exception: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1880 |             }
      |             ~
 1881 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:1891:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1891 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1897:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1897 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1904:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1904 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1910:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1910 |                     response_struct.statusCode = 500;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1917:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1917 |                     response_struct.statusCode = 403;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1923:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1923 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1932:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1932 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1939:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1939 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1951:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1951 |                 response_struct.statusCode = 501; // Not Implemented
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1960:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1960 |                 response_struct.statusCode = 500;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:1886:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
 1886 |         [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
 1887 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
 1888 |             try {
      |             ~~~~~
 1889 |                 auto auth_header_it = request.headers.find("Authorization");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1890 |                 if (auth_header_it == request.headers.end()) { /* ... 401 ... */
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1891 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1892 |                     response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", "Missing Authorization header"}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1893 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1894 |                     return std::unexpected("Missing Authorization header"); }
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1895 |                 const std::string& auth_header = auth_header_it->second;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1896 |                 if (auth_header.rfind("Bearer ", 0) != 0) { /* ... 401 ... */
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1897 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1898 |                     response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", "Invalid token format"}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1899 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1900 |                     return std::unexpected("Invalid token format"); }
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1901 |                 std::string token = auth_header.substr(7);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1902 |                 auto validation_result = securityManager_->validateToken(token);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1903 |                 if (!validation_result) { /* ... 401 ... */
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1904 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1905 |                     response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", validation_result.error().message}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1906 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1907 |                     return std::unexpected(validation_result.error().message); }
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1908 |                 auto user_info_expected = securityManager_->getUserInfo(*validation_result);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1909 |                 if (!user_info_expected) { /* ... 500 ... */
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1910 |                     response_struct.statusCode = 500;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1911 |                     response_struct.body = nlohmann::json({{"error", "Server Error"}, {"message", user_info_expected.error().message}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1912 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1913 |                     return std::unexpected(user_info_expected.error().message); }
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1914 |                 const auto& user_info_map = *user_info_expected;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1915 |                 bool is_admin = user_info_map.count("is_admin") && (user_info_map.at("is_admin") == "t" || user_info_map.at("is_admin") == "true");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1916 |                 if (!is_admin) { /* ... 403 ... */
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1917 |                     response_struct.statusCode = 403;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1918 |                     response_struct.body = nlohmann::json({{"error", "Forbidden"}, {"message", "Admin privileges required"}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1919 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1920 |                     return std::unexpected("Admin privileges required"); }
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1921 | 
      |          
 1922 |                 if (request.body.empty()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1923 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1924 |                     response_struct.body = nlohmann::json({{"error", "Bad Request"}, {"message", "Request body is empty."}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1925 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1926 |                     return std::unexpected("Request body is empty");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1927 |                 }
      |                 ~
 1928 |                 nlohmann::json request_json;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1929 |                 try {
      |                 ~~~~~
 1930 |                     request_json = nlohmann::json::parse(request.body);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1931 |                 } catch (const nlohmann::json::parse_error& e) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1932 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1933 |                     response_struct.body = nlohmann::json({{"error", "Bad Request"}, {"message", std::string("Invalid JSON format: ") + e.what()}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1934 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1935 |                     return std::unexpected(std::string("Invalid JSON: ") + e.what());
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1936 |                 }
      |                 ~
 1937 | 
      |          
 1938 |                 if (!request_json.contains("database_name") || !request_json["database_name"].is_string()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1939 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1940 |                     response_struct.body = nlohmann::json({{"error", "Bad Request"}, {"message", "'database_name' (string) is required in request body."}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1941 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1942 |                     return std::unexpected("Missing or invalid 'database_name' field");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1943 |                 }
      |                 ~
 1944 |                 std::string db_name = request_json["database_name"].get<std::string>();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1945 | 
      |          
 1946 |                 // TODO: Call connectionManager_->createDatabase(db_name)
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1947 |                 // auto create_expected = connectionManager_->createDatabase(db_name);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1948 |                 // if (!create_expected) { /* handle error */ }
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1949 |                 // response_struct.body = nlohmann::json({{"message", "Database '" + db_name + "' created successfully."}}).dump();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1950 | 
      |          
 1951 |                 response_struct.statusCode = 501; // Not Implemented
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1952 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1953 |                 response_struct.body = nlohmann::json({{"message", "Create database endpoint not fully implemented yet."}}).dump();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1954 | 
      |          
 1955 |                 if (this->corsConfig_.enabled) { /* ... CORS ... */ }
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1956 |                 return response_struct;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
 1957 | 
      |          
 1958 |             } catch (const std::exception& e) { /* ... 500 ... */
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1959 |                  utils::Logger::error(std::format("Exception in POST /api/databases: {}", e.what()));
      |                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1960 |                 response_struct.statusCode = 500;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1961 |                 response_struct.body = nlohmann::json({{"error", "Internal Server Error"}, {"message", e.what()}}).dump();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1962 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1963 |                 return std::unexpected(std::string("Exception: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1964 |             }
      |             ~
 1965 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:1975:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1975 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1981:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1981 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1988:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1988 |                     response_struct.statusCode = 401;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1994:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 1994 |                     response_struct.statusCode = 500;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:2001:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 2001 |                     response_struct.statusCode = 403;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:2007:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 2007 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:2016:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 2016 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:2023:37: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 2023 |                     response_struct.statusCode = 400;
      |                                     ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:2035:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 2035 |                 response_struct.statusCode = 501; // Not Implemented
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:2044:33: error: ‘struct dbservice::api::Response’ has no member named ‘statusCode’
 2044 |                 response_struct.statusCode = 500;
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:1970:9: error: cannot convert ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘int’
 1970 |         [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         |
      |         dbservice::api::ApiServer::initializeRoutes()::<lambda(const dbservice::api::ParsedRequest&)>
 1971 |             Response response_struct;
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~
 1972 |             try {
      |             ~~~~~
 1973 |                 auto auth_header_it = request.headers.find("Authorization");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1974 |                 if (auth_header_it == request.headers.end()) { /* ... 401 ... */
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1975 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1976 |                     response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", "Missing Authorization header"}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1977 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1978 |                     return std::unexpected("Missing Authorization header"); }
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1979 |                 const std::string& auth_header = auth_header_it->second;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1980 |                 if (auth_header.rfind("Bearer ", 0) != 0) { /* ... 401 ... */
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1981 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1982 |                     response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", "Invalid token format"}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1983 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1984 |                     return std::unexpected("Invalid token format"); }
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1985 |                 std::string token = auth_header.substr(7);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1986 |                 auto validation_result = securityManager_->validateToken(token);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1987 |                 if (!validation_result) { /* ... 401 ... */
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1988 |                     response_struct.statusCode = 401;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1989 |                     response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", validation_result.error().message}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1990 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1991 |                     return std::unexpected(validation_result.error().message); }
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1992 |                 auto user_info_expected = securityManager_->getUserInfo(*validation_result);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1993 |                 if (!user_info_expected) { /* ... 500 ... */
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1994 |                     response_struct.statusCode = 500;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1995 |                     response_struct.body = nlohmann::json({{"error", "Server Error"}, {"message", user_info_expected.error().message}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1996 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1997 |                     return std::unexpected(user_info_expected.error().message); }
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1998 |                 const auto& user_info_map = *user_info_expected;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1999 |                 bool is_admin = user_info_map.count("is_admin") && (user_info_map.at("is_admin") == "t" || user_info_map.at("is_admin") == "true");
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2000 |                 if (!is_admin) { /* ... 403 ... */
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2001 |                     response_struct.statusCode = 403;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2002 |                     response_struct.body = nlohmann::json({{"error", "Forbidden"}, {"message", "Admin privileges required"}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2003 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2004 |                     return std::unexpected("Admin privileges required"); }
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2005 | 
      |          
 2006 |                 if (request.body.empty()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2007 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2008 |                     response_struct.body = nlohmann::json({{"error", "Bad Request"}, {"message", "Request body is empty."}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2009 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2010 |                     return std::unexpected("Request body is empty");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2011 |                 }
      |                 ~
 2012 |                 nlohmann::json request_json;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2013 |                 try {
      |                 ~~~~~
 2014 |                     request_json = nlohmann::json::parse(request.body);
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2015 |                 } catch (const nlohmann::json::parse_error& e) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2016 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2017 |                     response_struct.body = nlohmann::json({{"error", "Bad Request"}, {"message", std::string("Invalid JSON format: ") + e.what()}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2018 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2019 |                     return std::unexpected(std::string("Invalid JSON: ") + e.what());
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2020 |                 }
      |                 ~
 2021 | 
      |          
 2022 |                 if (!request_json.contains("database_name") || !request_json["database_name"].is_string()) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2023 |                     response_struct.statusCode = 400;
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2024 |                     response_struct.body = nlohmann::json({{"error", "Bad Request"}, {"message", "'database_name' (string) is required in request body for deletion."}}).dump();
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2025 |                     response_struct.headers["Content-Type"] = "application/json";
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2026 |                     return std::unexpected("Missing or invalid 'database_name' field for deletion");
      |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2027 |                 }
      |                 ~
 2028 |                 std::string db_name = request_json["database_name"].get<std::string>();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2029 | 
      |          
 2030 |                 // TODO: Call connectionManager_->dropDatabase(db_name)
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2031 |                 // auto drop_expected = connectionManager_->dropDatabase(db_name);
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2032 |                 // if (!drop_expected) { /* handle error */ }
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2033 |                 // response_struct.body = nlohmann::json({{"message", "Database '" + db_name + "' dropped successfully."}}).dump();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2034 | 
      |          
 2035 |                 response_struct.statusCode = 501; // Not Implemented
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2036 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2037 |                 response_struct.body = nlohmann::json({{"message", "Drop database endpoint not fully implemented yet."}}).dump();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2038 | 
      |          
 2039 |                 if (this->corsConfig_.enabled) { /* ... CORS ... */ }
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2040 |                 return response_struct;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~
 2041 | 
      |          
 2042 |             } catch (const std::exception& e) { /* ... 500 ... */
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2043 |                 utils::Logger::error(std::format("Exception in DELETE /api/databases: {}", e.what()));
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2044 |                 response_struct.statusCode = 500;
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2045 |                 response_struct.body = nlohmann::json({{"error", "Internal Server Error"}, {"message", e.what()}}).dump();
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2046 |                 response_struct.headers["Content-Type"] = "application/json";
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2047 |                 return std::unexpected(std::string("Exception: ") + e.what());
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 2048 |             }
      |             ~
 2049 |         }
      |         ~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:107: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, int)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
      |                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: At global scope:
/home/<USER>/database-service-build/src/api/api_server.cpp:2053:26: error: ‘ParsedRequest’ is not a member of ‘dbservice::api::ApiServer’
 2053 | std::expected<ApiServer::ParsedRequest, std::string> ApiServer::parseRequest(const std::string& request) {
      |                          ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:2053:52: error: template argument 1 is invalid
 2053 | std::expected<ApiServer::ParsedRequest, std::string> ApiServer::parseRequest(const std::string& request) {
      |                                                    ^
/home/<USER>/database-service-build/src/api/api_server.cpp:2053:54: error: no declaration matches ‘int dbservice::api::ApiServer::parseRequest(const std::string&)’
 2053 | std::expected<ApiServer::ParsedRequest, std::string> ApiServer::parseRequest(const std::string& request) {
      |                                                      ^~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:159:47: note: candidate is: ‘std::expected<dbservice::api::ParsedRequest, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::parseRequest(const std::string&)’
  159 |     std::expected<ParsedRequest, std::string> parseRequest(const std::string& request);
      |                                               ^~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:55:7: note: ‘class dbservice::api::ApiServer’ defined here
   55 | class ApiServer {
      |       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:2094:31: error: ‘request’ was not declared in this scope
 2094 |     std::istringstream stream(request);
      |                               ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::parseQueryParams(std::string_view)’:
/home/<USER>/database-service-build/src/api/api_server.cpp:2140:39: error: no matching function for call to ‘std::__cxx11::basic_istringstream<char>::basic_istringstream(std::string_view&)’
 2140 |     std::istringstream iss(queryString);
      |                                       ^
In file included from /usr/include/c++/14/bits/quoted_string.h:38,
                 from /usr/include/c++/14/iomanip:50,
                 from /usr/include/c++/14/bits/fs_path.h:38,
                 from /usr/include/c++/14/filesystem:52,
                 from /usr/include/nlohmann/detail/meta/std_fs.hpp:22,
                 from /usr/include/nlohmann/detail/conversions/from_json.hpp:27,
                 from /usr/include/nlohmann/adl_serializer.hpp:14,
                 from /usr/include/nlohmann/json.hpp:34:
/usr/include/c++/14/sstream:663:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  663 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:663:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:2140:39: note:   ‘std::string_view’ {aka ‘std::basic_string_view<char>’} is not derived from ‘const std::__cxx11::basic_string<char, std::char_traits<char>, _Alloc>’
 2140 |     std::istringstream iss(queryString);
      |                                       ^
/usr/include/c++/14/sstream:655:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, std::ios_base::openmode, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  655 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:655:9: note:   candidate expects 3 arguments, 1 provided
/usr/include/c++/14/sstream:649:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  649 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:649:9: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/sstream:643:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(__string_type&&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; __string_type = std::__cxx11::basic_string<char>; std::ios_base::openmode = std::ios_base::openmode]’
  643 |       basic_istringstream(__string_type&& __str,
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:643:43: note:   no known conversion for argument 1 from ‘std::string_view’ {aka ‘std::basic_string_view<char>’} to ‘std::__cxx11::basic_istringstream<char>::__string_type&&’ {aka ‘std::__cxx11::basic_string<char>&&’}
  643 |       basic_istringstream(__string_type&& __str,
      |                           ~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:638:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::ios_base::openmode, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; std::ios_base::openmode = std::ios_base::openmode; allocator_type = std::allocator<char>]’
  638 |       basic_istringstream(ios_base::openmode __mode, const allocator_type& __a)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:638:7: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/sstream:632:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>&&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  632 |       basic_istringstream(basic_istringstream&& __rhs)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:632:49: note:   no known conversion for argument 1 from ‘std::string_view’ {aka ‘std::basic_string_view<char>’} to ‘std::__cxx11::basic_istringstream<char>&&’
  632 |       basic_istringstream(basic_istringstream&& __rhs)
      |                           ~~~~~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:615:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const __string_type&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; __string_type = std::__cxx11::basic_string<char>; std::ios_base::openmode = std::ios_base::openmode]’
  615 |       basic_istringstream(const __string_type& __str,
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:615:48: note:   no known conversion for argument 1 from ‘std::string_view’ {aka ‘std::basic_string_view<char>’} to ‘const std::__cxx11::basic_istringstream<char>::__string_type&’ {aka ‘const std::__cxx11::basic_string<char>&’}
  615 |       basic_istringstream(const __string_type& __str,
      |                           ~~~~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:597:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; std::ios_base::openmode = std::ios_base::openmode]’
  597 |       basic_istringstream(ios_base::openmode __mode)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:597:46: note:   no known conversion for argument 1 from ‘std::string_view’ {aka ‘std::basic_string_view<char>’} to ‘std::ios_base::openmode’
  597 |       basic_istringstream(ios_base::openmode __mode)
      |                           ~~~~~~~~~~~~~~~~~~~^~~~~~
/usr/include/c++/14/sstream:580:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream() [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  580 |       basic_istringstream()
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:580:7: note:   candidate expects 0 arguments, 1 provided
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘std::string dbservice::api::ApiServer::urlDecode(std::string_view)’:
/home/<USER>/database-service-build/src/api/api_server.cpp:2167:58: error: no matching function for call to ‘std::__cxx11::basic_istringstream<char>::basic_istringstream(std::basic_string_view<char>)’
 2167 |             std::istringstream iss(input.substr(i + 1, 2));
      |                                                          ^
/usr/include/c++/14/sstream:663:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  663 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:663:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:2167:58: note:   ‘std::basic_string_view<char>’ is not derived from ‘const std::__cxx11::basic_string<char, std::char_traits<char>, _Alloc>’
 2167 |             std::istringstream iss(input.substr(i + 1, 2));
      |                                                          ^
/usr/include/c++/14/sstream:655:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, std::ios_base::openmode, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  655 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:655:9: note:   candidate expects 3 arguments, 1 provided
/usr/include/c++/14/sstream:649:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  649 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:649:9: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/sstream:643:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(__string_type&&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; __string_type = std::__cxx11::basic_string<char>; std::ios_base::openmode = std::ios_base::openmode]’
  643 |       basic_istringstream(__string_type&& __str,
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:643:43: note:   no known conversion for argument 1 from ‘std::basic_string_view<char>’ to ‘std::__cxx11::basic_istringstream<char>::__string_type&&’ {aka ‘std::__cxx11::basic_string<char>&&’}
  643 |       basic_istringstream(__string_type&& __str,
      |                           ~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:638:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::ios_base::openmode, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; std::ios_base::openmode = std::ios_base::openmode; allocator_type = std::allocator<char>]’
  638 |       basic_istringstream(ios_base::openmode __mode, const allocator_type& __a)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:638:7: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/sstream:632:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>&&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  632 |       basic_istringstream(basic_istringstream&& __rhs)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:632:49: note:   no known conversion for argument 1 from ‘std::basic_string_view<char>’ to ‘std::__cxx11::basic_istringstream<char>&&’
  632 |       basic_istringstream(basic_istringstream&& __rhs)
      |                           ~~~~~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:615:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const __string_type&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; __string_type = std::__cxx11::basic_string<char>; std::ios_base::openmode = std::ios_base::openmode]’
  615 |       basic_istringstream(const __string_type& __str,
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:615:48: note:   no known conversion for argument 1 from ‘std::basic_string_view<char>’ to ‘const std::__cxx11::basic_istringstream<char>::__string_type&’ {aka ‘const std::__cxx11::basic_string<char>&’}
  615 |       basic_istringstream(const __string_type& __str,
      |                           ~~~~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:597:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; std::ios_base::openmode = std::ios_base::openmode]’
  597 |       basic_istringstream(ios_base::openmode __mode)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:597:46: note:   no known conversion for argument 1 from ‘std::basic_string_view<char>’ to ‘std::ios_base::openmode’
  597 |       basic_istringstream(ios_base::openmode __mode)
      |                           ~~~~~~~~~~~~~~~~~~~^~~~~~
/usr/include/c++/14/sstream:580:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream() [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  580 |       basic_istringstream()
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:580:7: note:   candidate expects 0 arguments, 1 provided
ninja: build stopped: subcommand failed.
