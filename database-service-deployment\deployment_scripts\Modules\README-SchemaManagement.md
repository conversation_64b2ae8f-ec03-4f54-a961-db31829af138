# Database Schema Management Module

## Overview

The `Manage-DatabaseSchemas.psm1` module provides comprehensive database schema management functionality for the Database Service deployment. This module allows you to create, manage, and migrate database schemas through an easy-to-use PowerShell interface.

## Features

### ✅ **Core Schema Operations**
- **List all schemas** in the database
- **Create new schemas** with proper ownership
- **Remove schemas** with safety checks
- **Check schema versions** and migration history

### ✅ **Migration Management**
- **Apply schema migrations** from SQL files
- **Track applied migrations** with timestamps
- **List available migration files**
- **View migration history**

### ✅ **Safety Features**
- **Database connection validation** before operations
- **Confirmation prompts** for destructive operations
- **Transaction safety** for migrations
- **Comprehensive error handling**

## Available Functions

### 1. `Get-DatabaseSchemas`
Lists all schemas in the database with descriptions and table counts.

**Usage:**
```powershell
Get-DatabaseSchemas                    # Use default database
Get-DatabaseSchemas -DatabaseName "mydb"  # Specify database
```

**Output:**
- Schema names and types (user/system/default)
- Table counts per schema
- Schema descriptions

### 2. `New-DatabaseSchema`
Creates a new database schema with proper ownership and permissions.

**Usage:**
```powershell
New-DatabaseSchema                     # Interactive mode
New-DatabaseSchema -SchemaName "myschema" -Owner "myuser"
```

**Features:**
- Interactive schema name input
- Automatic owner assignment
- Duplicate schema handling
- Permission setup

### 3. `Remove-DatabaseSchema`
Safely removes a database schema with confirmation prompts.

**Usage:**
```powershell
Remove-DatabaseSchema                  # Interactive mode with schema list
Remove-DatabaseSchema -SchemaName "myschema" -Force  # Skip confirmation
```

**Safety Features:**
- Lists available schemas before removal
- Prevents removal of system schemas
- Requires explicit confirmation ("DELETE")
- CASCADE removal of all contents

### 4. `Get-SchemaVersion`
Displays current schema version and migration history.

**Usage:**
```powershell
Get-SchemaVersion                      # Default schema (public)
Get-SchemaVersion -SchemaName "myschema"
```

**Output:**
- Current schema version
- Version description and timestamp
- Complete version history

### 5. `Apply-SchemaMigration` / `Invoke-SchemaMigration`
Applies SQL migration files to the database schema.

**Usage:**
```powershell
Apply-SchemaMigration                  # Interactive mode
Apply-SchemaMigration -MigrationFile "migration.sql" -Version "1.1.0"
```

**Features:**
- Lists available migration files
- Tracks applied migrations
- Version and description metadata
- Rollback on errors

### 6. `Get-AvailableMigrations`
Lists all available migration files on the server and locally.

**Usage:**
```powershell
Get-AvailableMigrations
```

**Output:**
- Server-side migration files with sizes and dates
- File content previews
- Local deployment files

### 7. `Get-AppliedMigrations`
Shows history of applied migrations with timestamps.

**Usage:**
```powershell
Get-AppliedMigrations                  # All schemas
Get-AppliedMigrations -SchemaName "public"  # Specific schema
```

**Output:**
- Migration names and application timestamps
- Schema-specific filtering
- Total migration count

### 8. `Initialize-SchemaTracking`
Sets up schema version and migration tracking tables.

**Usage:**
```powershell
Initialize-SchemaTracking
```

**Creates:**
- `schema_version` table for version tracking
- `schema_migrations` table for migration history
- Initial version entry

### 9. `Show-SchemaManagementMenu`
Interactive menu for all schema management operations.

**Usage:**
```powershell
Show-SchemaManagementMenu
```

**Menu Options:**
1. Get Database Schemas
2. Create New Schema
3. Remove Schema
4. Get Schema Version
5. Apply Schema Migration
6. Get Available Migrations
7. Get Applied Migrations
8. Initialize Schema Tracking

## Access Methods

### **Method 1: Main Deployment Script**
```powershell
# Run the deployment script
.\deploy-database-service-modular.ps1

# Select option 19: Manage Database Schemas
```

### **Method 2: Direct Module Import**
```powershell
# Import the module directly
Import-Module ".\Modules\Manage-DatabaseSchemas.psm1" -Force

# Use any function directly
Get-DatabaseSchemas
```

### **Method 3: PowerShell Session**
```powershell
# Load configuration first
$global:Config = Get-Content "config\database-service-development.json" | ConvertFrom-Json

# Import module
Import-Module ".\Modules\Manage-DatabaseSchemas.psm1" -Force

# Use functions
Show-SchemaManagementMenu
```

## Configuration Requirements

The module requires a properly configured `$global:Config` object with database settings:

```json
{
  "database": {
    "host": "localhost",
    "port": 5432,
    "name": "database_service",
    "user": "database_service_user",
    "password": "your_password"
  },
  "service": {
    "install_dir": "/opt/database-service"
  },
  "ssh": {
    "host": "your-server.com",
    "username": "your-user",
    "port": 22,
    "key_path": "path/to/ssh/key"
  }
}
```

## Schema Files Location

Migration files are expected in:
- **Server:** `/opt/database-service/schemas/`
- **Local:** `deployment_files/schemas/`

## Error Handling

The module includes comprehensive error handling:
- **Database connection validation** before operations
- **File existence checks** for migrations
- **Transaction rollback** on migration failures
- **Safe logging** to prevent empty string errors
- **Graceful fallbacks** for missing functions

## Security Features

- **SSH key-based authentication** for remote operations
- **User permission validation** for schema operations
- **Confirmation prompts** for destructive operations
- **System schema protection** (prevents removal of pg_catalog, etc.)

## Integration

The module integrates seamlessly with:
- **Main deployment script** (Option 19)
- **Database initialization** (Option 11)
- **Service installation** (Option 10)
- **Custom commands** (Option 16)

## Troubleshooting

### Common Issues:

1. **"Database connection failed"**
   - Check database configuration in config file
   - Verify PostgreSQL is running
   - Test SSH connection first

2. **"Schema management module not found"**
   - Ensure module is in Modules/ directory
   - Check module is listed in deployment script
   - Try importing module manually

3. **"Migration file not found"**
   - Check files exist in `/opt/database-service/schemas/`
   - Verify file permissions
   - Use Get-AvailableMigrations to list files

4. **"Permission denied"**
   - Check database user permissions
   - Verify SSH key access
   - Ensure service user has proper rights

## Best Practices

1. **Always backup** before applying migrations
2. **Test migrations** in development first
3. **Use version numbers** for migration tracking
4. **Document migration purposes** in descriptions
5. **Initialize tracking** before first migration
6. **Review available migrations** before applying

This module provides enterprise-grade schema management capabilities for the Database Service deployment!
