CMake Error: The current CMakeCache.txt directory /home/<USER>/database-service-build/build/CMakeCache.txt is different than the directory d:/Augment/project-tracker/database-service/build where CMakeCache.txt was created. This may result in binaries being created in the wrong place. If you are not sure, reedit the CMakeCache.txt
CMake Error: The source "/home/<USER>/database-service-build/CMakeLists.txt" does not match the source "D:/Augment/project-tracker/database-service/CMakeLists.txt" used to generate cache.  Re-run cmake with a different source directory.
make: *** No targets specified and no makefile found.  Stop.
