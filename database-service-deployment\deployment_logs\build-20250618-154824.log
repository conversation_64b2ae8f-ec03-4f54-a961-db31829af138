Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.5s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:22:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   22 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[ 16%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 22%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
[ 27%] Building CXX object CMakeFiles/database-service.dir/src/core/transaction.cpp.o
[ 33%] Building CXX object CMakeFiles/database-service.dir/src/database_service.cpp.o
[ 38%] Building CXX object CMakeFiles/database-service.dir/src/main.cpp.o
[ 44%] Building CXX object CMakeFiles/database-service.dir/src/metrics/database_metrics.cpp.o
[ 50%] Building CXX object CMakeFiles/database-service.dir/src/metrics/metrics_collector.cpp.o
[ 55%] Building CXX object CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o
[ 61%] Building CXX object CMakeFiles/database-service.dir/src/security/credential_store.cpp.o
/home/<USER>/database-service-build/src/security/credential_store.cpp: In member function ‘bool dbservice::security::CredentialStore::initialize(const std::string&)’:
/home/<USER>/database-service-build/src/security/credential_store.cpp:41:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   41 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/credential_store.cpp:6:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:42:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   42 |     SHA256_Update(&sha256, encryptionKey.c_str(), encryptionKey.length());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:43:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   43 |     SHA256_Final(key, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
[ 66%] Building CXX object CMakeFiles/database-service.dir/src/security/jwt.cpp.o
[ 72%] Building CXX object CMakeFiles/database-service.dir/src/security/security_manager.cpp.o
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<dbservice::security::TokenPair, dbservice::security::SecurityError> dbservice::security::SecurityManager::authenticate(const std::string&, const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:92:36: error: ‘AuthenticationFailed’ is not a member of ‘dbservice::security::SecurityErrorType’
   92 |                 SecurityErrorType::AuthenticationFailed,
      |                                    ^~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:99:46: error: no matching function for call to ‘dbservice::core::ManagedConnection::ManagedConnection(std::shared_ptr<dbservice::core::ConnectionManager>&)’
   99 |     ManagedConnection conn(connectionManager_);
      |                                              ^
In file included from /home/<USER>/database-service-build/src/security/security_manager.cpp:3:
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:14: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |              ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:51: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ConnectionManager&’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |                                ~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:5: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ManagedConnection&&)’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |     ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:43: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ManagedConnection&&’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |                       ~~~~~~~~~~~~~~~~~~~~^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:104:65: error: ‘DatabaseConnectionError’ is not a member of ‘dbservice::security::SecurityErrorType’
  104 |         return std::unexpected(SecurityError{SecurityErrorType::DatabaseConnectionError, errMsg});
      |                                                                 ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:110:29: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  110 |         auto dbResult = conn->executeQuery(queryUser, {username});
      |                             ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:116:69: error: ‘AuthenticationFailed’ is not a member of ‘dbservice::security::SecurityErrorType’
  116 |             return std::unexpected(SecurityError{SecurityErrorType::AuthenticationFailed, errMsg});
      |                                                                     ^~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:127:36: error: ‘AuthenticationFailed’ is not a member of ‘dbservice::security::SecurityErrorType’
  127 |                 SecurityErrorType::AuthenticationFailed,
      |                                    ^~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:136:38: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  136 |             int updateOpResult = conn->executeNonQuery(updateQuery, {username});
      |                                      ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:140:24: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
  140 |         } catch (const pqxx::sql_error& e_update) {
      |                        ^~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:140:28: error: expected ‘)’ before ‘::’ token
  140 |         } catch (const pqxx::sql_error& e_update) {
      |                 ~          ^~
      |                            )
/home/<USER>/database-service-build/src/security/security_manager.cpp:140:28: error: expected ‘{’ before ‘::’ token
  140 |         } catch (const pqxx::sql_error& e_update) {
      |                            ^~
/home/<USER>/database-service-build/src/security/security_manager.cpp:140:30: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
  140 |         } catch (const pqxx::sql_error& e_update) {
      |                              ^~~~~~~~~
      |                              strerror
/home/<USER>/database-service-build/src/security/security_manager.cpp:140:41: error: ‘e_update’ was not declared in this scope
  140 |         } catch (const pqxx::sql_error& e_update) {
      |                                         ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:142:11: error: expected primary-expression before ‘catch’
  142 |         } catch (const std::exception& e_update) {
      |           ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:163:20: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
  163 |     } catch (const pqxx::sql_error& e) {
      |                    ^~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:163:24: error: expected ‘)’ before ‘::’ token
  163 |     } catch (const pqxx::sql_error& e) {
      |             ~          ^~
      |                        )
/home/<USER>/database-service-build/src/security/security_manager.cpp:163:24: error: expected ‘{’ before ‘::’ token
  163 |     } catch (const pqxx::sql_error& e) {
      |                        ^~
/home/<USER>/database-service-build/src/security/security_manager.cpp:163:26: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
  163 |     } catch (const pqxx::sql_error& e) {
      |                          ^~~~~~~~~
      |                          strerror
/home/<USER>/database-service-build/src/security/security_manager.cpp:163:37: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
  163 |     } catch (const pqxx::sql_error& e) {
      |                                     ^
      |                                     std::numbers::e
In file included from /usr/include/c++/14/bits/max_size_type.h:37,
                 from /usr/include/c++/14/bits/ranges_base.h:39,
                 from /usr/include/c++/14/string_view:56,
                 from /usr/include/c++/14/bits/basic_string.h:47,
                 from /usr/include/c++/14/string:54,
                 from /home/<USER>/database-service-build/include/database-service/security/security_manager.hpp:2,
                 from /home/<USER>/database-service-build/src/security/security_manager.cpp:1:
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:168:7: error: expected primary-expression before ‘catch’
  168 |     } catch (const std::exception& e) {
      |       ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<std::__cxx11::basic_string<char>, dbservice::security::SecurityError> dbservice::security::SecurityManager::validateToken(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:221:46: error: no matching function for call to ‘dbservice::core::ManagedConnection::ManagedConnection(std::shared_ptr<dbservice::core::ConnectionManager>&)’
  221 |     ManagedConnection conn(connectionManager_);
      |                                              ^
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:14: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |              ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:51: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ConnectionManager&’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |                                ~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:5: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ManagedConnection&&)’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |     ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:43: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ManagedConnection&&’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |                       ~~~~~~~~~~~~~~~~~~~~^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:225:65: error: ‘DatabaseConnectionError’ is not a member of ‘dbservice::security::SecurityErrorType’
  225 |         return std::unexpected(SecurityError{SecurityErrorType::DatabaseConnectionError, errMsg});
      |                                                                 ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:230:29: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  230 |         auto dbResult = conn->executeQuery(query, {username});
      |                             ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:240:20: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
  240 |     } catch (const pqxx::sql_error& e) {
      |                    ^~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:240:24: error: expected ‘)’ before ‘::’ token
  240 |     } catch (const pqxx::sql_error& e) {
      |             ~          ^~
      |                        )
/home/<USER>/database-service-build/src/security/security_manager.cpp:240:24: error: expected ‘{’ before ‘::’ token
  240 |     } catch (const pqxx::sql_error& e) {
      |                        ^~
/home/<USER>/database-service-build/src/security/security_manager.cpp:240:26: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
  240 |     } catch (const pqxx::sql_error& e) {
      |                          ^~~~~~~~~
      |                          strerror
/home/<USER>/database-service-build/src/security/security_manager.cpp:240:37: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
  240 |     } catch (const pqxx::sql_error& e) {
      |                                     ^
      |                                     std::numbers::e
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:244:7: error: expected primary-expression before ‘catch’
  244 |     } catch (const std::exception& e) {
      |       ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: At global scope:
/home/<USER>/database-service-build/src/security/security_manager.cpp:251:46: error: no declaration matches ‘std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> > dbservice::security::SecurityManager::getUserInfo(const std::string&)’
  251 | std::unordered_map<std::string, std::string> SecurityManager::getUserInfo(const std::string& token) {
      |                                              ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/security/security_manager.hpp:128:80: note: candidate is: ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError> dbservice::security::SecurityManager::getUserInfo(const std::string&)’
  128 |     std::expected<std::unordered_map<std::string, std::string>, SecurityError> getUserInfo(const std::string& token);
      |                                                                                ^~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/security/security_manager.hpp:75:7: note: ‘class dbservice::security::SecurityManager’ defined here
   75 | class SecurityManager {
      |       ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:296:6: error: no declaration matches ‘bool dbservice::security::SecurityManager::hasPermission(const std::string&, const std::string&)’
  296 | bool SecurityManager::hasPermission(const std::string& username, const std::string& permission) {
      |      ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/security/security_manager.hpp:136:40: note: candidate is: ‘std::expected<bool, dbservice::security::SecurityError> dbservice::security::SecurityManager::hasPermission(const std::string&, const std::string&)’
  136 |     std::expected<bool, SecurityError> hasPermission(const std::string& username, const std::string& permission);
      |                                        ^~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/security/security_manager.hpp:75:7: note: ‘class dbservice::security::SecurityManager’ defined here
   75 | class SecurityManager {
      |       ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:321:6: error: no declaration matches ‘bool dbservice::security::SecurityManager::grantPermission(const std::string&, const std::string&)’
  321 | bool SecurityManager::grantPermission(const std::string& username, const std::string& permission) {
      |      ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/security/security_manager.hpp:144:40: note: candidate is: ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::grantPermission(const std::string&, const std::string&)’
  144 |     std::expected<void, SecurityError> grantPermission(const std::string& username, const std::string& permission);
      |                                        ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/security/security_manager.hpp:75:7: note: ‘class dbservice::security::SecurityManager’ defined here
   75 | class SecurityManager {
      |       ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:359:6: error: no declaration matches ‘bool dbservice::security::SecurityManager::revokePermission(const std::string&, const std::string&)’
  359 | bool SecurityManager::revokePermission(const std::string& username, const std::string& permission) {
      |      ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/security/security_manager.hpp:152:40: note: candidate is: ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::revokePermission(const std::string&, const std::string&)’
  152 |     std::expected<void, SecurityError> revokePermission(const std::string& username, const std::string& permission);
      |                                        ^~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/security/security_manager.hpp:75:7: note: ‘class dbservice::security::SecurityManager’ defined here
   75 | class SecurityManager {
      |       ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:388:6: error: no declaration matches ‘bool dbservice::security::SecurityManager::createUser(const std::string&, const std::string&, bool)’
  388 | bool SecurityManager::createUser(const std::string& username, const std::string& password, bool isAdmin) {
      |      ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/security/security_manager.hpp:161:40: note: candidate is: ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::createUser(const std::string&, const std::string&, bool)’
  161 |     std::expected<void, SecurityError> createUser(const std::string& username, const std::string& password, bool isAdmin = false);
      |                                        ^~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/security/security_manager.hpp:75:7: note: ‘class dbservice::security::SecurityManager’ defined here
   75 | class SecurityManager {
      |       ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:423:6: error: no declaration matches ‘bool dbservice::security::SecurityManager::deleteUser(const std::string&)’
  423 | bool SecurityManager::deleteUser(const std::string& username) {
      |      ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/security/security_manager.hpp:168:40: note: candidate is: ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::deleteUser(const std::string&)’
  168 |     std::expected<void, SecurityError> deleteUser(const std::string& username);
      |                                        ^~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/security/security_manager.hpp:75:7: note: ‘class dbservice::security::SecurityManager’ defined here
   75 | class SecurityManager {
      |       ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::createUsersTable()’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:487:46: error: no matching function for call to ‘dbservice::core::ManagedConnection::ManagedConnection(std::shared_ptr<dbservice::core::ConnectionManager>&)’
  487 |     ManagedConnection conn(connectionManager_);
      |                                              ^
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:14: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |              ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:51: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ConnectionManager&’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |                                ~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:5: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ManagedConnection&&)’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |     ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:43: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ManagedConnection&&’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |                       ~~~~~~~~~~~~~~~~~~~~^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:491:65: error: ‘DatabaseConnectionError’ is not a member of ‘dbservice::security::SecurityErrorType’
  491 |         return std::unexpected(SecurityError{SecurityErrorType::DatabaseConnectionError, errMsg});
      |                                                                 ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:508:26: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  508 |         int result = conn->executeNonQuery(query, {});
      |                          ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:512:69: error: ‘DatabaseQueryError’ is not a member of ‘dbservice::security::SecurityErrorType’; did you mean ‘DatabaseError’?
  512 |             return std::unexpected(SecurityError{SecurityErrorType::DatabaseQueryError, errMsg});
      |                                                                     ^~~~~~~~~~~~~~~~~~
      |                                                                     DatabaseError
/home/<USER>/database-service-build/src/security/security_manager.cpp:516:20: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
  516 |     } catch (const pqxx::sql_error& e) {
      |                    ^~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:516:24: error: expected ‘)’ before ‘::’ token
  516 |     } catch (const pqxx::sql_error& e) {
      |             ~          ^~
      |                        )
/home/<USER>/database-service-build/src/security/security_manager.cpp:516:24: error: expected ‘{’ before ‘::’ token
  516 |     } catch (const pqxx::sql_error& e) {
      |                        ^~
/home/<USER>/database-service-build/src/security/security_manager.cpp:516:26: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
  516 |     } catch (const pqxx::sql_error& e) {
      |                          ^~~~~~~~~
      |                          strerror
/home/<USER>/database-service-build/src/security/security_manager.cpp:516:37: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
  516 |     } catch (const pqxx::sql_error& e) {
      |                                     ^
      |                                     std::numbers::e
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:520:7: error: expected primary-expression before ‘catch’
  520 |     } catch (const std::exception& e) {
      |       ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::createPermissionsTable()’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:528:46: error: no matching function for call to ‘dbservice::core::ManagedConnection::ManagedConnection(std::shared_ptr<dbservice::core::ConnectionManager>&)’
  528 |     ManagedConnection conn(connectionManager_);
      |                                              ^
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:14: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |              ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:51: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ConnectionManager&’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |                                ~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:5: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ManagedConnection&&)’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |     ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:43: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ManagedConnection&&’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |                       ~~~~~~~~~~~~~~~~~~~~^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:532:65: error: ‘DatabaseConnectionError’ is not a member of ‘dbservice::security::SecurityErrorType’
  532 |         return std::unexpected(SecurityError{SecurityErrorType::DatabaseConnectionError, errMsg});
      |                                                                 ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:547:26: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  547 |         int result = conn->executeNonQuery(query, {});
      |                          ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:551:69: error: ‘DatabaseQueryError’ is not a member of ‘dbservice::security::SecurityErrorType’; did you mean ‘DatabaseError’?
  551 |             return std::unexpected(SecurityError{SecurityErrorType::DatabaseQueryError, errMsg});
      |                                                                     ^~~~~~~~~~~~~~~~~~
      |                                                                     DatabaseError
/home/<USER>/database-service-build/src/security/security_manager.cpp:555:20: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
  555 |     } catch (const pqxx::sql_error& e) {
      |                    ^~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:555:24: error: expected ‘)’ before ‘::’ token
  555 |     } catch (const pqxx::sql_error& e) {
      |             ~          ^~
      |                        )
/home/<USER>/database-service-build/src/security/security_manager.cpp:555:24: error: expected ‘{’ before ‘::’ token
  555 |     } catch (const pqxx::sql_error& e) {
      |                        ^~
/home/<USER>/database-service-build/src/security/security_manager.cpp:555:26: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
  555 |     } catch (const pqxx::sql_error& e) {
      |                          ^~~~~~~~~
      |                          strerror
/home/<USER>/database-service-build/src/security/security_manager.cpp:555:37: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
  555 |     } catch (const pqxx::sql_error& e) {
      |                                     ^
      |                                     std::numbers::e
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:559:7: error: expected primary-expression before ‘catch’
  559 |     } catch (const std::exception& e) {
      |       ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::createRefreshTokensTable()’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:567:46: error: no matching function for call to ‘dbservice::core::ManagedConnection::ManagedConnection(std::shared_ptr<dbservice::core::ConnectionManager>&)’
  567 |     ManagedConnection conn(connectionManager_);
      |                                              ^
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:14: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |              ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:51: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ConnectionManager&’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |                                ~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:5: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ManagedConnection&&)’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |     ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:43: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ManagedConnection&&’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |                       ~~~~~~~~~~~~~~~~~~~~^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:571:65: error: ‘DatabaseConnectionError’ is not a member of ‘dbservice::security::SecurityErrorType’
  571 |         return std::unexpected(SecurityError{SecurityErrorType::DatabaseConnectionError, errMsg});
      |                                                                 ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:587:26: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  587 |         int result = conn->executeNonQuery(query, {});
      |                          ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:591:69: error: ‘DatabaseQueryError’ is not a member of ‘dbservice::security::SecurityErrorType’; did you mean ‘DatabaseError’?
  591 |             return std::unexpected(SecurityError{SecurityErrorType::DatabaseQueryError, errMsg});
      |                                                                     ^~~~~~~~~~~~~~~~~~
      |                                                                     DatabaseError
/home/<USER>/database-service-build/src/security/security_manager.cpp:595:20: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
  595 |     } catch (const pqxx::sql_error& e) {
      |                    ^~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:595:24: error: expected ‘)’ before ‘::’ token
  595 |     } catch (const pqxx::sql_error& e) {
      |             ~          ^~
      |                        )
/home/<USER>/database-service-build/src/security/security_manager.cpp:595:24: error: expected ‘{’ before ‘::’ token
  595 |     } catch (const pqxx::sql_error& e) {
      |                        ^~
/home/<USER>/database-service-build/src/security/security_manager.cpp:595:26: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
  595 |     } catch (const pqxx::sql_error& e) {
      |                          ^~~~~~~~~
      |                          strerror
/home/<USER>/database-service-build/src/security/security_manager.cpp:595:37: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
  595 |     } catch (const pqxx::sql_error& e) {
      |                                     ^
      |                                     std::numbers::e
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:599:7: error: expected primary-expression before ‘catch’
  599 |     } catch (const std::exception& e) {
      |       ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<std::__cxx11::basic_string<char>, dbservice::security::SecurityError> dbservice::security::SecurityManager::hashPassword(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:610:65: error: ‘PasswordHashingFailed’ is not a member of ‘dbservice::security::SecurityErrorType’
  610 |         return std::unexpected(SecurityError{SecurityErrorType::PasswordHashingFailed, errMsg});
      |                                                                 ^~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:620:28: error: ‘sha256’ was not declared in this scope; did you mean ‘SHA256’?
  620 |         std::string hash = sha256(saltedPassword); // Assuming sha256 is robust or throws on OpenSSL errors
      |                            ^~~~~~
      |                            SHA256
/home/<USER>/database-service-build/src/security/security_manager.cpp:628:65: error: ‘PasswordHashingFailed’ is not a member of ‘dbservice::security::SecurityErrorType’
  628 |         return std::unexpected(SecurityError{SecurityErrorType::PasswordHashingFailed, errMsg});
      |                                                                 ^~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::verifyPassword(const std::string&, const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:636:65: error: ‘PasswordVerificationFailed’ is not a member of ‘dbservice::security::SecurityErrorType’
  636 |         return std::unexpected(SecurityError{SecurityErrorType::PasswordVerificationFailed, errMsg});
      |                                                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:641:65: error: ‘PasswordVerificationFailed’ is not a member of ‘dbservice::security::SecurityErrorType’
  641 |         return std::unexpected(SecurityError{SecurityErrorType::PasswordVerificationFailed, errMsg});
      |                                                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:659:65: error: ‘PasswordVerificationFailed’ is not a member of ‘dbservice::security::SecurityErrorType’
  659 |         return std::unexpected(SecurityError{SecurityErrorType::PasswordVerificationFailed, errMsg});
      |                                                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:669:65: error: ‘PasswordVerificationFailed’ is not a member of ‘dbservice::security::SecurityErrorType’
  669 |         return std::unexpected(SecurityError{SecurityErrorType::PasswordVerificationFailed, errMsg});
      |                                                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:675:36: error: ‘sha256’ was not declared in this scope; did you mean ‘SHA256’?
  675 |         std::string computedHash = sha256(saltedPassword); // Assuming sha256 is robust or throws
      |                                    ^~~~~~
      |                                    SHA256
/home/<USER>/database-service-build/src/security/security_manager.cpp:682:69: error: ‘PasswordVerificationFailed’ is not a member of ‘dbservice::security::SecurityErrorType’
  682 |             return std::unexpected(SecurityError{SecurityErrorType::PasswordVerificationFailed, errMsg});
      |                                                                     ^~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:688:65: error: ‘PasswordVerificationFailed’ is not a member of ‘dbservice::security::SecurityErrorType’
  688 |         return std::unexpected(SecurityError{SecurityErrorType::PasswordVerificationFailed, errMsg});
      |                                                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<dbservice::security::TokenPair, dbservice::security::SecurityError> dbservice::security::SecurityManager::generateTokenPair(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:704:50: error: no matching function for call to ‘dbservice::core::ManagedConnection::ManagedConnection(std::shared_ptr<dbservice::core::ConnectionManager>&)’
  704 |         ManagedConnection conn(connectionManager_);
      |                                                  ^
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:14: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |              ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:51: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ConnectionManager&’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |                                ~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:5: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ManagedConnection&&)’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |     ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:43: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ManagedConnection&&’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |                       ~~~~~~~~~~~~~~~~~~~~^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:708:69: error: ‘DatabaseConnectionError’ is not a member of ‘dbservice::security::SecurityErrorType’
  708 |             return std::unexpected(SecurityError{SecurityErrorType::DatabaseConnectionError, errMsg});
      |                                                                     ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:712:33: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  712 |             auto dbResult = conn->executeQuery(query, {username});
      |                                 ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:724:24: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
  724 |         } catch (const pqxx::sql_error& e) {
      |                        ^~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:724:28: error: expected ‘)’ before ‘::’ token
  724 |         } catch (const pqxx::sql_error& e) {
      |                 ~          ^~
      |                            )
/home/<USER>/database-service-build/src/security/security_manager.cpp:724:28: error: expected ‘{’ before ‘::’ token
  724 |         } catch (const pqxx::sql_error& e) {
      |                            ^~
/home/<USER>/database-service-build/src/security/security_manager.cpp:724:30: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
  724 |         } catch (const pqxx::sql_error& e) {
      |                              ^~~~~~~~~
      |                              strerror
/home/<USER>/database-service-build/src/security/security_manager.cpp:724:41: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
  724 |         } catch (const pqxx::sql_error& e) {
      |                                         ^
      |                                         std::numbers::e
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:728:11: error: expected primary-expression before ‘catch’
  728 |         } catch (const std::exception& e) {
      |           ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<dbservice::security::TokenPair, dbservice::security::SecurityError> dbservice::security::SecurityManager::refreshAccessToken(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:829:46: error: no matching function for call to ‘dbservice::core::ManagedConnection::ManagedConnection(std::shared_ptr<dbservice::core::ConnectionManager>&)’
  829 |     ManagedConnection conn(connectionManager_);
      |                                              ^
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:14: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |              ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:51: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ConnectionManager&’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |                                ~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:5: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ManagedConnection&&)’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |     ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:43: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ManagedConnection&&’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |                       ~~~~~~~~~~~~~~~~~~~~^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:835:32: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  835 |             int dbResult = conn->executeNonQuery(query, {refreshToken});
      |                                ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:839:24: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
  839 |         } catch (const pqxx::sql_error& e) {
      |                        ^~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:839:28: error: expected ‘)’ before ‘::’ token
  839 |         } catch (const pqxx::sql_error& e) {
      |                 ~          ^~
      |                            )
/home/<USER>/database-service-build/src/security/security_manager.cpp:839:28: error: expected ‘{’ before ‘::’ token
  839 |         } catch (const pqxx::sql_error& e) {
      |                            ^~
/home/<USER>/database-service-build/src/security/security_manager.cpp:839:30: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
  839 |         } catch (const pqxx::sql_error& e) {
      |                              ^~~~~~~~~
      |                              strerror
/home/<USER>/database-service-build/src/security/security_manager.cpp:839:41: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
  839 |         } catch (const pqxx::sql_error& e) {
      |                                         ^
      |                                         std::numbers::e
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:841:11: error: expected primary-expression before ‘catch’
  841 |         } catch (const std::exception& e) {
      |           ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::invalidateTokens(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:860:46: error: no matching function for call to ‘dbservice::core::ManagedConnection::ManagedConnection(std::shared_ptr<dbservice::core::ConnectionManager>&)’
  860 |     ManagedConnection conn(connectionManager_);
      |                                              ^
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:14: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |              ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:51: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ConnectionManager&’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |                                ~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:5: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ManagedConnection&&)’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |     ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:43: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ManagedConnection&&’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |                       ~~~~~~~~~~~~~~~~~~~~^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:864:65: error: ‘DatabaseConnectionError’ is not a member of ‘dbservice::security::SecurityErrorType’
  864 |         return std::unexpected(SecurityError{SecurityErrorType::DatabaseConnectionError, errMsg});
      |                                                                 ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:869:28: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  869 |         int dbResult = conn->executeNonQuery(query, {username});
      |                            ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:876:69: error: ‘DatabaseQueryError’ is not a member of ‘dbservice::security::SecurityErrorType’; did you mean ‘DatabaseError’?
  876 |             return std::unexpected(SecurityError{SecurityErrorType::DatabaseQueryError, errMsg});
      |                                                                     ^~~~~~~~~~~~~~~~~~
      |                                                                     DatabaseError
/home/<USER>/database-service-build/src/security/security_manager.cpp:881:20: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
  881 |     } catch (const pqxx::sql_error& e) {
      |                    ^~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:881:24: error: expected ‘)’ before ‘::’ token
  881 |     } catch (const pqxx::sql_error& e) {
      |             ~          ^~
      |                        )
/home/<USER>/database-service-build/src/security/security_manager.cpp:881:24: error: expected ‘{’ before ‘::’ token
  881 |     } catch (const pqxx::sql_error& e) {
      |                        ^~
/home/<USER>/database-service-build/src/security/security_manager.cpp:881:26: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
  881 |     } catch (const pqxx::sql_error& e) {
      |                          ^~~~~~~~~
      |                          strerror
/home/<USER>/database-service-build/src/security/security_manager.cpp:881:37: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
  881 |     } catch (const pqxx::sql_error& e) {
      |                                     ^
      |                                     std::numbers::e
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:885:7: error: expected primary-expression before ‘catch’
  885 |     } catch (const std::exception& e) {
      |       ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::storeRefreshToken(const std::string&, const std::string&, const std::chrono::_V2::system_clock::time_point&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:894:46: error: no matching function for call to ‘dbservice::core::ManagedConnection::ManagedConnection(std::shared_ptr<dbservice::core::ConnectionManager>&)’
  894 |     ManagedConnection conn(connectionManager_);
      |                                              ^
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:14: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |              ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:51: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ConnectionManager&’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |                                ~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:5: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ManagedConnection&&)’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |     ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:43: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ManagedConnection&&’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |                       ~~~~~~~~~~~~~~~~~~~~^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:898:65: error: ‘DatabaseConnectionError’ is not a member of ‘dbservice::security::SecurityErrorType’
  898 |         return std::unexpected(SecurityError{SecurityErrorType::DatabaseConnectionError, errMsg});
      |                                                                 ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:917:28: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  917 |         int dbResult = conn->executeNonQuery(query, {refreshToken, username, std::string(expiresBuffer)});
      |                            ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:927:20: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
  927 |     } catch (const pqxx::sql_error& e) {
      |                    ^~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:927:24: error: expected ‘)’ before ‘::’ token
  927 |     } catch (const pqxx::sql_error& e) {
      |             ~          ^~
      |                        )
/home/<USER>/database-service-build/src/security/security_manager.cpp:927:24: error: expected ‘{’ before ‘::’ token
  927 |     } catch (const pqxx::sql_error& e) {
      |                        ^~
/home/<USER>/database-service-build/src/security/security_manager.cpp:927:26: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
  927 |     } catch (const pqxx::sql_error& e) {
      |                          ^~~~~~~~~
      |                          strerror
/home/<USER>/database-service-build/src/security/security_manager.cpp:927:37: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
  927 |     } catch (const pqxx::sql_error& e) {
      |                                     ^
      |                                     std::numbers::e
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:931:7: error: expected primary-expression before ‘catch’
  931 |     } catch (const std::exception& e) {
      |       ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<std::__cxx11::basic_string<char>, dbservice::security::SecurityError> dbservice::security::SecurityManager::validateRefreshToken(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:982:46: error: no matching function for call to ‘dbservice::core::ManagedConnection::ManagedConnection(std::shared_ptr<dbservice::core::ConnectionManager>&)’
  982 |     ManagedConnection conn(connectionManager_);
      |                                              ^
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:14: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |              ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:51: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ConnectionManager&’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |                                ~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:5: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ManagedConnection&&)’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |     ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:43: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ManagedConnection&&’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |                       ~~~~~~~~~~~~~~~~~~~~^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:986:65: error: ‘DatabaseConnectionError’ is not a member of ‘dbservice::security::SecurityErrorType’
  986 |         return std::unexpected(SecurityError{SecurityErrorType::DatabaseConnectionError, errMsg});
      |                                                                 ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:995:29: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  995 |         auto dbResult = conn->executeQuery(query, {refreshToken, usernameFromToken});
      |                             ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:1005:20: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
 1005 |     } catch (const pqxx::sql_error& e) {
      |                    ^~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:1005:24: error: expected ‘)’ before ‘::’ token
 1005 |     } catch (const pqxx::sql_error& e) {
      |             ~          ^~
      |                        )
/home/<USER>/database-service-build/src/security/security_manager.cpp:1005:24: error: expected ‘{’ before ‘::’ token
 1005 |     } catch (const pqxx::sql_error& e) {
      |                        ^~
/home/<USER>/database-service-build/src/security/security_manager.cpp:1005:26: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
 1005 |     } catch (const pqxx::sql_error& e) {
      |                          ^~~~~~~~~
      |                          strerror
/home/<USER>/database-service-build/src/security/security_manager.cpp:1005:37: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
 1005 |     } catch (const pqxx::sql_error& e) {
      |                                     ^
      |                                     std::numbers::e
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:1009:7: error: expected primary-expression before ‘catch’
 1009 |     } catch (const std::exception& e) {
      |       ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘void dbservice::security::SecurityManager::cleanupExpiredRefreshTokens()’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:1017:46: error: no matching function for call to ‘dbservice::core::ManagedConnection::ManagedConnection(std::shared_ptr<dbservice::core::ConnectionManager>&)’
 1017 |     ManagedConnection conn(connectionManager_);
      |                                              ^
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:14: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |              ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:248:51: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ConnectionManager&’
  248 |     explicit ManagedConnection(ConnectionManager& manager);
      |                                ~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:5: note: candidate: ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ManagedConnection&&)’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |     ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:251:43: note:   no known conversion for argument 1 from ‘std::shared_ptr<dbservice::core::ConnectionManager>’ to ‘dbservice::core::ManagedConnection&&’
  251 |     ManagedConnection(ManagedConnection&& other) noexcept;
      |                       ~~~~~~~~~~~~~~~~~~~~^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:1025:28: error: invalid use of incomplete type ‘class dbservice::core::Connection’
 1025 |         int dbResult = conn->executeNonQuery(query, {});
      |                            ^~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:19:7: note: forward declaration of ‘class dbservice::core::Connection’
   19 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:1035:20: error: ISO C++ forbids declaration of ‘pqxx’ with no type [-fpermissive]
 1035 |     } catch (const pqxx::sql_error& e) {
      |                    ^~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:1035:24: error: expected ‘)’ before ‘::’ token
 1035 |     } catch (const pqxx::sql_error& e) {
      |             ~          ^~
      |                        )
/home/<USER>/database-service-build/src/security/security_manager.cpp:1035:24: error: expected ‘{’ before ‘::’ token
 1035 |     } catch (const pqxx::sql_error& e) {
      |                        ^~
/home/<USER>/database-service-build/src/security/security_manager.cpp:1035:26: error: ‘::sql_error’ has not been declared; did you mean ‘strerror’?
 1035 |     } catch (const pqxx::sql_error& e) {
      |                          ^~~~~~~~~
      |                          strerror
/home/<USER>/database-service-build/src/security/security_manager.cpp:1035:37: error: ‘e’ was not declared in this scope; did you mean ‘std::numbers::e’?
 1035 |     } catch (const pqxx::sql_error& e) {
      |                                     ^
      |                                     std::numbers::e
/usr/include/c++/14/numbers:124:27: note: ‘std::numbers::e’ declared here
  124 |   inline constexpr double e = e_v<double>;
      |                           ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:1037:7: error: expected primary-expression before ‘catch’
 1037 |     } catch (const std::exception& e) {
      |       ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: At global scope:
/home/<USER>/database-service-build/src/security/security_manager.cpp:1068:13: error: no declaration matches ‘std::string dbservice::security::SecurityManager::sha256(const std::string&)’
 1068 | std::string SecurityManager::sha256(const std::string& input) {
      |             ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:1068:13: note: no functions named ‘std::string dbservice::security::SecurityManager::sha256(const std::string&)’
/home/<USER>/database-service-build/include/database-service/security/security_manager.hpp:75:7: note: ‘class dbservice::security::SecurityManager’ defined here
   75 | class SecurityManager {
      |       ^~~~~~~~~~~~~~~
[ 77%] Building CXX object CMakeFiles/database-service.dir/src/utils/cache.cpp.o
make[2]: *** [CMakeFiles/database-service.dir/build.make:247: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
