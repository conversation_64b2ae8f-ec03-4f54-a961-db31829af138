#pragma once
#include <memory>
#include <chrono>
#include <expected>
#include <string>
#include <format>
#include "database-service/core/connection.hpp"

namespace dbservice::core {

/**
 * @class Transaction
 * @brief Database transaction
 */
class Transaction {
public:
    /**
     * @brief Constructor
     * @param connection Connection
     */
    Transaction(std::shared_ptr<Connection> connection);

    /**
     * @brief Destructor
     */
    ~Transaction();

    /**
     * @brief Commit the transaction
     * @return Success or error message
     */
    std::expected<void, std::string> commit();

    /**
     * @brief Rollback the transaction
     * @return Success or error message
     */
    std::expected<void, std::string> rollback();

    /**
     * @brief Get the connection
     * @return Connection
     */
    std::shared_ptr<Connection> getConnection() const;

private:
    std::shared_ptr<Connection> connection_;
    bool committed_;
    bool rolledBack_;
    std::chrono::steady_clock::time_point startTime_;
};

} // namespace dbservice::core
