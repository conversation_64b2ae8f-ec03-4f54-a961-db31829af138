Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  5%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:22:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   22 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[ 16%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 22%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
[ 27%] Building CXX object CMakeFiles/database-service.dir/src/core/transaction.cpp.o
[ 33%] Building CXX object CMakeFiles/database-service.dir/src/database_service.cpp.o
[ 38%] Building CXX object CMakeFiles/database-service.dir/src/main.cpp.o
[ 44%] Building CXX object CMakeFiles/database-service.dir/src/metrics/database_metrics.cpp.o
[ 50%] Building CXX object CMakeFiles/database-service.dir/src/metrics/metrics_collector.cpp.o
[ 55%] Building CXX object CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o
[ 61%] Building CXX object CMakeFiles/database-service.dir/src/security/credential_store.cpp.o
/home/<USER>/database-service-build/src/security/credential_store.cpp: In member function ‘bool dbservice::security::CredentialStore::initialize(const std::string&)’:
/home/<USER>/database-service-build/src/security/credential_store.cpp:41:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   41 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/credential_store.cpp:6:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:42:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   42 |     SHA256_Update(&sha256, encryptionKey.c_str(), encryptionKey.length());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:43:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   43 |     SHA256_Final(key, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
[ 66%] Building CXX object CMakeFiles/database-service.dir/src/security/jwt.cpp.o
[ 72%] Building CXX object CMakeFiles/database-service.dir/src/security/security_manager.cpp.o
/home/<USER>/database-service-build/src/security/security_manager.cpp: In function ‘std::string dbservice::security::sha256(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:29:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   29 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/security_manager.cpp:12:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:30:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   30 |     SHA256_Update(&sha256, input.c_str(), input.size());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:31:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   31 |     SHA256_Final(hash, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<dbservice::security::TokenPair, dbservice::security::SecurityError> dbservice::security::SecurityManager::authenticate(const std::string&, const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:184:98: error: cannot convert ‘const std::string’ {aka ‘const std::__cxx11::basic_string<char>’} to ‘int’ in initialization
  184 |         return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg, e.sqlstate()});
      |                                                                                        ~~~~~~~~~~^~
      |                                                                                                  |
      |                                                                                                  const std::string {aka const std::__cxx11::basic_string<char>}
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<std::__cxx11::basic_string<char>, dbservice::security::SecurityError> dbservice::security::SecurityManager::validateToken(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:260:98: error: cannot convert ‘const std::string’ {aka ‘const std::__cxx11::basic_string<char>’} to ‘int’ in initialization
  260 |         return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg, e.sqlstate()});
      |                                                                                        ~~~~~~~~~~^~
      |                                                                                                  |
      |                                                                                                  const std::string {aka const std::__cxx11::basic_string<char>}
/home/<USER>/database-service-build/src/security/security_manager.cpp:264:65: error: ‘UnknownError’ is not a member of ‘dbservice::security::SecurityErrorType’
  264 |         return std::unexpected(SecurityError{SecurityErrorType::UnknownError, errMsg});
      |                                                                 ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError> dbservice::security::SecurityManager::getUserInfo(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:303:20: error: ‘class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘empty’
  303 |         if (result.empty() || result[0].size() < 4) {
      |                    ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:303:37: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ and ‘int’)
  303 |         if (result.empty() || result[0].size() < 4) {
      |                                     ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:309:38: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ and ‘int’)
  309 |         userInfo["username"] = result[0][0];
      |                                      ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:310:35: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ and ‘int’)
  310 |         userInfo["email"] = result[0][1];
      |                                   ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:311:38: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ and ‘int’)
  311 |         userInfo["is_admin"] = result[0][2];
      |                                      ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:312:40: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ and ‘int’)
  312 |         userInfo["created_at"] = result[0][3];
      |                                        ^
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<bool, dbservice::security::SecurityError> dbservice::security::SecurityManager::hasPermission(const std::string&, const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:337:26: error: ‘class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘empty’
  337 |         if (!adminResult.empty() && !adminResult[0].empty() && adminResult[0][0] == "t") {
      |                          ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:337:49: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ and ‘int’)
  337 |         if (!adminResult.empty() && !adminResult[0].empty() && adminResult[0][0] == "t") {
      |                                                 ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:337:75: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ and ‘int’)
  337 |         if (!adminResult.empty() && !adminResult[0].empty() && adminResult[0][0] == "t") {
      |                                                                           ^
/home/<USER>/database-service-build/src/security/security_manager.cpp:345:24: error: ‘class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘empty’
  345 |         return !result.empty();
      |                        ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::grantPermission(const std::string&, const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:368:24: error: ‘class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘empty’
  368 |         if (userResult.empty()) {
      |                        ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:386:57: error: cannot convert ‘std::expected<int, std::__cxx11::basic_string<char> >’ to ‘int’ in initialization
  386 |         int result = connectionManager_->executeNonQuery(query, {username, permission});
      |                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                                         |
      |                                                         std::expected<int, std::__cxx11::basic_string<char> >
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::revokePermission(const std::string&, const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:426:57: error: cannot convert ‘std::expected<int, std::__cxx11::basic_string<char> >’ to ‘int’ in initialization
  426 |         int result = connectionManager_->executeNonQuery(query, {username, permission});
      |                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                                         |
      |                                                         std::expected<int, std::__cxx11::basic_string<char> >
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::createUser(const std::string&, const std::string&, bool)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:458:26: error: ‘class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘empty’
  458 |         if (!checkResult.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:473:57: error: cannot convert ‘std::expected<int, std::__cxx11::basic_string<char> >’ to ‘int’ in initialization
  473 |         int result = connectionManager_->executeNonQuery(query, {username, passwordHash, isAdmin ? "true" : "false"});
      |                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                                         |
      |                                                         std::expected<int, std::__cxx11::basic_string<char> >
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::deleteUser(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:512:50: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘getConnection’
  512 |             int permissionsResult = transaction->getConnection()->executeNonQuery(permissionsQuery, {username});
      |                                                  ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:517:30: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  517 |                 transaction->rollback();
      |                              ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:523:45: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘getConnection’
  523 |             int tokensResult = transaction->getConnection()->executeNonQuery(tokensQuery, {username});
      |                                             ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:528:30: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  528 |                 transaction->rollback();
      |                              ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:534:43: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘getConnection’
  534 |             int userResult = transaction->getConnection()->executeNonQuery(userQuery, {username});
      |                                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:539:30: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  539 |                 transaction->rollback();
      |                              ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:544:31: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘commit’
  544 |             if (!transaction->commit()) {
      |                               ^~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:555:26: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  555 |             transaction->rollback();
      |                          ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::createUsersTable()’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:598:98: error: cannot convert ‘const std::string’ {aka ‘const std::__cxx11::basic_string<char>’} to ‘int’ in initialization
  598 |         return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg, e.sqlstate()});
      |                                                                                        ~~~~~~~~~~^~
      |                                                                                                  |
      |                                                                                                  const std::string {aka const std::__cxx11::basic_string<char>}
/home/<USER>/database-service-build/src/security/security_manager.cpp:602:65: error: ‘UnknownError’ is not a member of ‘dbservice::security::SecurityErrorType’
  602 |         return std::unexpected(SecurityError{SecurityErrorType::UnknownError, errMsg});
      |                                                                 ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::createPermissionsTable()’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:637:98: error: cannot convert ‘const std::string’ {aka ‘const std::__cxx11::basic_string<char>’} to ‘int’ in initialization
  637 |         return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg, e.sqlstate()});
      |                                                                                        ~~~~~~~~~~^~
      |                                                                                                  |
      |                                                                                                  const std::string {aka const std::__cxx11::basic_string<char>}
/home/<USER>/database-service-build/src/security/security_manager.cpp:641:65: error: ‘UnknownError’ is not a member of ‘dbservice::security::SecurityErrorType’
  641 |         return std::unexpected(SecurityError{SecurityErrorType::UnknownError, errMsg});
      |                                                                 ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::createRefreshTokensTable()’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:677:98: error: cannot convert ‘const std::string’ {aka ‘const std::__cxx11::basic_string<char>’} to ‘int’ in initialization
  677 |         return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg, e.sqlstate()});
      |                                                                                        ~~~~~~~~~~^~
      |                                                                                                  |
      |                                                                                                  const std::string {aka const std::__cxx11::basic_string<char>}
/home/<USER>/database-service-build/src/security/security_manager.cpp:681:65: error: ‘UnknownError’ is not a member of ‘dbservice::security::SecurityErrorType’
  681 |         return std::unexpected(SecurityError{SecurityErrorType::UnknownError, errMsg});
      |                                                                 ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<dbservice::security::TokenPair, dbservice::security::SecurityError> dbservice::security::SecurityManager::generateTokenPair(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:806:102: error: cannot convert ‘const std::string’ {aka ‘const std::__cxx11::basic_string<char>’} to ‘int’ in initialization
  806 |             return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg, e.sqlstate()});
      |                                                                                            ~~~~~~~~~~^~
      |                                                                                                      |
      |                                                                                                      const std::string {aka const std::__cxx11::basic_string<char>}
/home/<USER>/database-service-build/src/security/security_manager.cpp:810:69: error: ‘UnknownError’ is not a member of ‘dbservice::security::SecurityErrorType’
  810 |             return std::unexpected(SecurityError{SecurityErrorType::UnknownError, errMsg});
      |                                                                     ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::invalidateTokens(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:963:98: error: cannot convert ‘const std::string’ {aka ‘const std::__cxx11::basic_string<char>’} to ‘int’ in initialization
  963 |         return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg, e.sqlstate()});
      |                                                                                        ~~~~~~~~~~^~
      |                                                                                                  |
      |                                                                                                  const std::string {aka const std::__cxx11::basic_string<char>}
/home/<USER>/database-service-build/src/security/security_manager.cpp:967:65: error: ‘UnknownError’ is not a member of ‘dbservice::security::SecurityErrorType’
  967 |         return std::unexpected(SecurityError{SecurityErrorType::UnknownError, errMsg});
      |                                                                 ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::storeRefreshToken(const std::string&, const std::string&, const std::chrono::_V2::system_clock::time_point&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:1009:98: error: cannot convert ‘const std::string’ {aka ‘const std::__cxx11::basic_string<char>’} to ‘int’ in initialization
 1009 |         return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg, e.sqlstate()});
      |                                                                                        ~~~~~~~~~~^~
      |                                                                                                  |
      |                                                                                                  const std::string {aka const std::__cxx11::basic_string<char>}
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<std::__cxx11::basic_string<char>, dbservice::security::SecurityError> dbservice::security::SecurityManager::validateRefreshToken(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:1087:98: error: cannot convert ‘const std::string’ {aka ‘const std::__cxx11::basic_string<char>’} to ‘int’ in initialization
 1087 |         return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg, e.sqlstate()});
      |                                                                                        ~~~~~~~~~~^~
      |                                                                                                  |
      |                                                                                                  const std::string {aka const std::__cxx11::basic_string<char>}
/home/<USER>/database-service-build/src/security/security_manager.cpp:1091:65: error: ‘UnknownError’ is not a member of ‘dbservice::security::SecurityErrorType’
 1091 |         return std::unexpected(SecurityError{SecurityErrorType::UnknownError, errMsg});
      |                                                                 ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: At global scope:
/home/<USER>/database-service-build/src/security/security_manager.cpp:1147:13: error: no declaration matches ‘std::string dbservice::security::SecurityManager::sha256(const std::string&)’
 1147 | std::string SecurityManager::sha256(const std::string& input) {
      |             ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:1147:13: note: no functions named ‘std::string dbservice::security::SecurityManager::sha256(const std::string&)’
In file included from /home/<USER>/database-service-build/src/security/security_manager.cpp:1:
/home/<USER>/database-service-build/include/database-service/security/security_manager.hpp:75:7: note: ‘class dbservice::security::SecurityManager’ defined here
   75 | class SecurityManager {
      |       ^~~~~~~~~~~~~~~
[ 77%] Building CXX object CMakeFiles/database-service.dir/src/utils/cache.cpp.o
make[2]: *** [CMakeFiles/database-service.dir/build.make:247: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
