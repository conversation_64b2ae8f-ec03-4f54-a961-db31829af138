CMake Warning at CMakeLists.txt:82 (message):
  std::print is not available.  Will fall back to iostream.


CMake Warning (dev) at CMakeLists.txt:90 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- 
-- C++23 Features:
--   std::expected: 1
--   std::format: 1
--   std::print: 
-- ===============================================
-- 
-- Configuring done (0.1s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
[1/16] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
FAILED: CMakeFiles/database-service.dir/src/api/route_controller.cpp.o 
/usr/bin/g++-14 -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -I/home/<USER>/database-service-build/include -I/usr/include/postgresql -O3 -DNDEBUG -O3 -std=c++23 -Wall -Wextra -Wpedantic -MD -MT CMakeFiles/database-service.dir/src/api/route_controller.cpp.o -MF CMakeFiles/database-service.dir/src/api/route_controller.cpp.o.d -o CMakeFiles/database-service.dir/src/api/route_controller.cpp.o -c /home/<USER>/database-service-build/src/api/route_controller.cpp
In file included from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:14,
                 from /home/<USER>/database-service-build/include/database-service/api/route_controller.hpp:3,
                 from /home/<USER>/database-service-build/src/api/route_controller.cpp:1:
/home/<USER>/database-service-build/include/database-service/api/api_types.hpp:51:8: error: using typedef-name ‘using dbservice::api::Response = class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’ after ‘struct’
   51 | struct Response {
      |        ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_types.hpp:16:7: note: ‘using dbservice::api::Response = class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’ has a previous declaration here
   16 | using Response = std::expected<std::string, std::string>;
      |       ^~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘void dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:15:43: error: cannot convert ‘dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘dbservice::api::RouteHandler’ {aka ‘std::function<std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’}
   15 |     server.addRoute("GET", "/api/health", [this](const ParsedRequest& request) {
      |                                           ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                           |
      |                                           dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)::<lambda(const dbservice::api::ParsedRequest&)>
   16 |         return handleHealthCheck(request);
      |         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ 
   17 |     });
      |     ~                                      
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:114:84: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, dbservice::api::RouteHandler)’
  114 |     void addRoute(const std::string& method, const std::string& path, RouteHandler handler);
      |                                                                       ~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:27:14: error: ‘using dbservice::api::Response = class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’ {aka ‘class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’} has no member named ‘headers’
   27 |     response.headers["Content-Type"] = "application/json";
      |              ^~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:28:14: error: ‘using dbservice::api::Response = class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’ {aka ‘class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’} has no member named ‘body’
   28 |     response.body = response_body.dump();
      |              ^~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:22:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   22 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[2/16] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
FAILED: CMakeFiles/database-service.dir/src/api/api_server.cpp.o 
/usr/bin/g++-14 -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -I/home/<USER>/database-service-build/include -I/usr/include/postgresql -O3 -DNDEBUG -O3 -std=c++23 -Wall -Wextra -Wpedantic -MD -MT CMakeFiles/database-service.dir/src/api/api_server.cpp.o -MF CMakeFiles/database-service.dir/src/api/api_server.cpp.o.d -o CMakeFiles/database-service.dir/src/api/api_server.cpp.o -c /home/<USER>/database-service-build/src/api/api_server.cpp
In file included from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:14,
                 from /home/<USER>/database-service-build/src/api/api_server.cpp:1:
/home/<USER>/database-service-build/include/database-service/api/api_types.hpp:51:8: error: using typedef-name ‘using dbservice::api::Response = class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’ after ‘struct’
   51 | struct Response {
      |        ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_types.hpp:16:7: note: ‘using dbservice::api::Response = class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’ has a previous declaration here
   16 | using Response = std::expected<std::string, std::string>;
      |       ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:6: error: no declaration matches ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, RouteHandler)’
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, RouteHandler handler) {
      |      ^~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:114:10: note: candidate is: ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, dbservice::api::RouteHandler)’
  114 |     void addRoute(const std::string& method, const std::string& path, RouteHandler handler);
      |          ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:55:7: note: ‘class dbservice::api::ApiServer’ defined here
   55 | class ApiServer {
      |       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:179:78: error: ‘using dbservice::api::ApiServer::RouteHandler = class std::function<std::expected<std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’ is private within this context
  179 | void ApiServer::addRoute(const std::string& method, const std::string& path, RouteHandler handler) {
      |                                                                              ^~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:209:11: note: declared private here
  209 |     using RouteHandler = std::function<std::expected<Response, std::string>(const ParsedRequest&)>;
      |           ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:256:26: error: ‘Response’ is not a member of ‘dbservice::api::ApiServer’
  256 | std::expected<ApiServer::Response, std::string> ApiServer::handleRequest(ParsedRequest& request) {
      |                          ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:256:47: error: template argument 1 is invalid
  256 | std::expected<ApiServer::Response, std::string> ApiServer::handleRequest(ParsedRequest& request) {
      |                                               ^
/home/<USER>/database-service-build/src/api/api_server.cpp:256:49: error: no declaration matches ‘int dbservice::api::ApiServer::handleRequest(dbservice::api::ParsedRequest&)’
  256 | std::expected<ApiServer::Response, std::string> ApiServer::handleRequest(ParsedRequest& request) {
      |                                                 ^~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:124:46: note: candidate is: ‘std::expected<std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::handleRequest(dbservice::api::ParsedRequest&)’
  124 |         std::expected<Response, std::string> handleRequest(ParsedRequest& request);
      |                                              ^~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:55:7: note: ‘class dbservice::api::ApiServer’ defined here
   55 | class ApiServer {
      |       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:443:30: error: no matching function for call to ‘dbservice::api::ApiServer::parseRequest(std::string&, std::string&, std::string&, std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, std::string&)’
  443 |             if (!parseRequest(request, method, path, headers, body)) {
      |                  ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:159:47: note: candidate: ‘std::expected<dbservice::api::ParsedRequest, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::parseRequest(const std::string&)’
  159 |     std::expected<ParsedRequest, std::string> parseRequest(const std::string& request);
      |                                               ^~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:159:47: note:   candidate expects 1 argument, 5 provided
/home/<USER>/database-service-build/src/api/api_server.cpp:445:59: error: conversion from ‘dbservice::api::Response’ {aka ‘std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’} to non-scalar type ‘std::string’ {aka ‘std::__cxx11::basic_string<char>’} requested
  445 |                 std::string response = createErrorResponse(400, "Bad Request", "Invalid request format");
      |                                        ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:452:65: error: no matching function for call to ‘dbservice::api::ApiServer::handleRequest(std::string&, std::string&, std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, std::string&)’
  452 |             auto [responseHeaders, responseBody] = handleRequest(method, path, headers, body);
      |                                                    ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:124:46: note: candidate: ‘std::expected<std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::handleRequest(dbservice::api::ParsedRequest&)’
  124 |         std::expected<Response, std::string> handleRequest(ParsedRequest& request);
      |                                              ^~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:124:46: note:   candidate expects 1 argument, 4 provided
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:499:5: error: ‘optionsHandlers_’ was not declared in this scope
  499 |     optionsHandlers_["/api"] = [this](const auto& headers, const auto& body) {
      |     ^~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:504:5: error: ‘getHandlers_’ was not declared in this scope
  504 |     getHandlers_["/api/health"] = [this](const auto& headers, const auto& body) {
      |     ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:531:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  531 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:536:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  536 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:536:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:536:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:536:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  536 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:536:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:536:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:566:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  566 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:571:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  571 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:571:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:571:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:571:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  571 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:571:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:571:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:601:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  601 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:606:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  606 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:606:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:606:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:606:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  606 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:606:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:606:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:623:5: error: ‘postHandlers_’ was not declared in this scope
  623 |     postHandlers_["/api/auth/login"] = [this](const auto& headers, const auto& body) {
      |     ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:696:22: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  696 |         if (userInfo.empty()) {
      |                      ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:701:34: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  701 |             {"username", userInfo["username"]},
      |                                  ^
/home/<USER>/database-service-build/src/api/api_server.cpp:701:34: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:701:34: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:702:31: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [6]’)
  702 |             {"email", userInfo["email"]},
      |                               ^
/home/<USER>/database-service-build/src/api/api_server.cpp:702:31: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:702:31: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:703:34: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  703 |             {"is_admin", userInfo["is_admin"] == "t" || userInfo["is_admin"] == "true"},
      |                                  ^
/home/<USER>/database-service-build/src/api/api_server.cpp:703:34: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:703:34: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:703:65: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  703 |             {"is_admin", userInfo["is_admin"] == "t" || userInfo["is_admin"] == "true"},
      |                                                                 ^
/home/<USER>/database-service-build/src/api/api_server.cpp:703:65: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:703:65: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:704:36: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [11]’)
  704 |             {"created_at", userInfo["created_at"]}
      |                                    ^
/home/<USER>/database-service-build/src/api/api_server.cpp:704:36: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:704:36: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:726:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  726 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:730:44: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  730 |             std::string username = userInfo["username"];
      |                                            ^
/home/<USER>/database-service-build/src/api/api_server.cpp:730:44: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:730:44: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:764:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  764 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:769:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  769 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:769:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:769:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:769:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  769 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:769:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:769:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:819:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  819 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:824:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  824 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:824:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:824:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:824:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  824 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:824:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:824:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:857:5: error: ‘deleteHandlers_’ was not declared in this scope
  857 |     deleteHandlers_["/api/credentials/remove"] = [this](const auto& headers, const auto& body) {
      |     ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:873:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  873 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:878:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  878 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:878:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:878:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:878:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  878 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:878:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:878:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:931:33: error: ‘class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘size’
  931 |                 {"rows", result.size()},
      |                                 ^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:935:36: error: no matching function for call to ‘begin(std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >&)’
  935 |             for (const auto& row : result) {
      |                                    ^~~~~~
In file included from /usr/include/c++/14/bits/range_access.h:36,
                 from /usr/include/c++/14/string:53,
                 from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:2:
/usr/include/c++/14/initializer_list:88:5: note: candidate: ‘template<class _Tp> constexpr const _Tp* std::begin(initializer_list<_Tp>)’
   88 |     begin(initializer_list<_Tp> __ils) noexcept
      |     ^~~~~
/usr/include/c++/14/initializer_list:88:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:935:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘std::initializer_list<_Tp>’
  935 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:52:5: note: candidate: ‘template<class _Container> constexpr decltype (__cont.begin()) std::begin(_Container&)’
   52 |     begin(_Container& __cont) -> decltype(__cont.begin())
      |     ^~~~~
/usr/include/c++/14/bits/range_access.h:52:5: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/range_access.h: In substitution of ‘template<class _Container> constexpr decltype (__cont.begin()) std::begin(_Container&) [with _Container = std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >]’:
/home/<USER>/database-service-build/src/api/api_server.cpp:935:36:   required from here
  935 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:52:50: error: ‘class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘begin’
   52 |     begin(_Container& __cont) -> decltype(__cont.begin())
      |                                           ~~~~~~~^~~~~
/usr/include/c++/14/bits/range_access.h:63:5: note: candidate: ‘template<class _Container> constexpr decltype (__cont.begin()) std::begin(const _Container&)’
   63 |     begin(const _Container& __cont) -> decltype(__cont.begin())
      |     ^~~~~
/usr/include/c++/14/bits/range_access.h:63:5: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/range_access.h: In substitution of ‘template<class _Container> constexpr decltype (__cont.begin()) std::begin(const _Container&) [with _Container = std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >]’:
/home/<USER>/database-service-build/src/api/api_server.cpp:935:36:   required from here
  935 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:63:56: error: ‘const class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘begin’
   63 |     begin(const _Container& __cont) -> decltype(__cont.begin())
      |                                                 ~~~~~~~^~~~~
/usr/include/c++/14/bits/range_access.h:95:5: note: candidate: ‘template<class _Tp, long unsigned int _Nm> constexpr _Tp* std::begin(_Tp (&)[_Nm])’
   95 |     begin(_Tp (&__arr)[_Nm]) noexcept
      |     ^~~~~
/usr/include/c++/14/bits/range_access.h:95:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:935:36: note:   mismatched types ‘_Tp [_Nm]’ and ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’
  935 |             for (const auto& row : result) {
      |                                    ^~~~~~
In file included from /usr/include/nlohmann/detail/conversions/from_json.hpp:21,
                 from /usr/include/nlohmann/adl_serializer.hpp:14,
                 from /usr/include/nlohmann/json.hpp:34,
                 from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:9:
/usr/include/c++/14/valarray:1227:5: note: candidate: ‘template<class _Tp> _Tp* std::begin(valarray<_Tp>&)’
 1227 |     begin(valarray<_Tp>& __va) noexcept
      |     ^~~~~
/usr/include/c++/14/valarray:1227:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:935:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘std::valarray<_Tp>’
  935 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/valarray:1238:5: note: candidate: ‘template<class _Tp> const _Tp* std::begin(const valarray<_Tp>&)’
 1238 |     begin(const valarray<_Tp>& __va) noexcept
      |     ^~~~~
/usr/include/c++/14/valarray:1238:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:935:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘const std::valarray<_Tp>’
  935 |             for (const auto& row : result) {
      |                                    ^~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:935:36: error: no matching function for call to ‘end(std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >&)’
/usr/include/c++/14/initializer_list:99:5: note: candidate: ‘template<class _Tp> constexpr const _Tp* std::end(initializer_list<_Tp>)’
   99 |     end(initializer_list<_Tp> __ils) noexcept
      |     ^~~
/usr/include/c++/14/initializer_list:99:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:935:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘std::initializer_list<_Tp>’
  935 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:74:5: note: candidate: ‘template<class _Container> constexpr decltype (__cont.end()) std::end(_Container&)’
   74 |     end(_Container& __cont) -> decltype(__cont.end())
      |     ^~~
/usr/include/c++/14/bits/range_access.h:74:5: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/range_access.h: In substitution of ‘template<class _Container> constexpr decltype (__cont.end()) std::end(_Container&) [with _Container = std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >]’:
/home/<USER>/database-service-build/src/api/api_server.cpp:935:36:   required from here
  935 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:74:48: error: ‘class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘end’
   74 |     end(_Container& __cont) -> decltype(__cont.end())
      |                                         ~~~~~~~^~~
/usr/include/c++/14/bits/range_access.h:85:5: note: candidate: ‘template<class _Container> constexpr decltype (__cont.end()) std::end(const _Container&)’
   85 |     end(const _Container& __cont) -> decltype(__cont.end())
      |     ^~~
/usr/include/c++/14/bits/range_access.h:85:5: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/range_access.h: In substitution of ‘template<class _Container> constexpr decltype (__cont.end()) std::end(const _Container&) [with _Container = std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >]’:
/home/<USER>/database-service-build/src/api/api_server.cpp:935:36:   required from here
  935 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:85:54: error: ‘const class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘end’
   85 |     end(const _Container& __cont) -> decltype(__cont.end())
      |                                               ~~~~~~~^~~
/usr/include/c++/14/bits/range_access.h:106:5: note: candidate: ‘template<class _Tp, long unsigned int _Nm> constexpr _Tp* std::end(_Tp (&)[_Nm])’
  106 |     end(_Tp (&__arr)[_Nm]) noexcept
      |     ^~~
/usr/include/c++/14/bits/range_access.h:106:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:935:36: note:   mismatched types ‘_Tp [_Nm]’ and ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’
  935 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/valarray:1249:5: note: candidate: ‘template<class _Tp> _Tp* std::end(valarray<_Tp>&)’
 1249 |     end(valarray<_Tp>& __va) noexcept
      |     ^~~
/usr/include/c++/14/valarray:1249:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:935:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘std::valarray<_Tp>’
  935 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/valarray:1265:5: note: candidate: ‘template<class _Tp> const _Tp* std::end(const valarray<_Tp>&)’
 1265 |     end(const valarray<_Tp>& __va) noexcept
      |     ^~~
/usr/include/c++/14/valarray:1265:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:935:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘const std::valarray<_Tp>’
  935 |             for (const auto& row : result) {
      |                                    ^~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: At global scope:
/home/<USER>/database-service-build/src/api/api_server.cpp:980:26: error: ‘ParsedRequest’ is not a member of ‘dbservice::api::ApiServer’
  980 | std::expected<ApiServer::ParsedRequest, std::string> ApiServer::parseRequest(const std::string& request) {
      |                          ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:980:52: error: template argument 1 is invalid
  980 | std::expected<ApiServer::ParsedRequest, std::string> ApiServer::parseRequest(const std::string& request) {
      |                                                    ^
/home/<USER>/database-service-build/src/api/api_server.cpp:980:54: error: no declaration matches ‘int dbservice::api::ApiServer::parseRequest(const std::string&)’
  980 | std::expected<ApiServer::ParsedRequest, std::string> ApiServer::parseRequest(const std::string& request) {
      |                                                      ^~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:159:47: note: candidate is: ‘std::expected<dbservice::api::ParsedRequest, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::parseRequest(const std::string&)’
  159 |     std::expected<ParsedRequest, std::string> parseRequest(const std::string& request);
      |                                               ^~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:55:7: note: ‘class dbservice::api::ApiServer’ defined here
   55 | class ApiServer {
      |       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1021:31: error: ‘request’ was not declared in this scope
 1021 |     std::istringstream stream(request);
      |                               ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘dbservice::api::Response dbservice::api::ApiServer::createSuccessResponse(const nlohmann::json_abi_v3_11_3::json&)’:
/home/<USER>/database-service-build/src/api/api_server.cpp:1027:14: error: ‘using dbservice::api::Response = class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’ {aka ‘class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’} has no member named ‘headers’
 1027 |     response.headers["Content-Type"] = "application/json";
      |              ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1028:14: error: ‘using dbservice::api::Response = class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’ {aka ‘class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’} has no member named ‘body’
 1028 |     response.body = nlohmann::json{
      |              ^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘dbservice::api::Response dbservice::api::ApiServer::createSuccessResponseWithHeaders(const nlohmann::json_abi_v3_11_3::json&)’:
/home/<USER>/database-service-build/src/api/api_server.cpp:1037:14: error: ‘using dbservice::api::Response = class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’ {aka ‘class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’} has no member named ‘headers’
 1037 |     response.headers["Content-Type"] = "application/json";
      |              ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1038:14: error: ‘using dbservice::api::Response = class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’ {aka ‘class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’} has no member named ‘body’
 1038 |     response.body = nlohmann::json{
      |              ^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘dbservice::api::Response dbservice::api::ApiServer::createErrorResponse(int, const std::string&, const std::string&)’:
/home/<USER>/database-service-build/src/api/api_server.cpp:1048:14: error: ‘using dbservice::api::Response = class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’ {aka ‘class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’} has no member named ‘headers’
 1048 |     response.headers["Content-Type"] = "application/json";
      |              ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1049:14: error: ‘using dbservice::api::Response = class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’ {aka ‘class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’} has no member named ‘headers’
 1049 |     response.headers["Status"] = std::to_string(statusCode) + " " + statusText;
      |              ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1050:14: error: ‘using dbservice::api::Response = class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’ {aka ‘class std::expected<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >’} has no member named ‘body’
 1050 |     response.body = nlohmann::json{
      |              ^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::parseQueryParams(std::string_view)’:
/home/<USER>/database-service-build/src/api/api_server.cpp:1067:39: error: no matching function for call to ‘std::__cxx11::basic_istringstream<char>::basic_istringstream(std::string_view&)’
 1067 |     std::istringstream iss(queryString);
      |                                       ^
In file included from /usr/include/c++/14/bits/quoted_string.h:38,
                 from /usr/include/c++/14/iomanip:50,
                 from /usr/include/c++/14/bits/fs_path.h:38,
                 from /usr/include/c++/14/filesystem:52,
                 from /usr/include/nlohmann/detail/meta/std_fs.hpp:22,
                 from /usr/include/nlohmann/detail/conversions/from_json.hpp:27:
/usr/include/c++/14/sstream:663:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  663 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:663:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:1067:39: note:   ‘std::string_view’ {aka ‘std::basic_string_view<char>’} is not derived from ‘const std::__cxx11::basic_string<char, std::char_traits<char>, _Alloc>’
 1067 |     std::istringstream iss(queryString);
      |                                       ^
/usr/include/c++/14/sstream:655:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, std::ios_base::openmode, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  655 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:655:9: note:   candidate expects 3 arguments, 1 provided
/usr/include/c++/14/sstream:649:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  649 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:649:9: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/sstream:643:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(__string_type&&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; __string_type = std::__cxx11::basic_string<char>; std::ios_base::openmode = std::ios_base::openmode]’
  643 |       basic_istringstream(__string_type&& __str,
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:643:43: note:   no known conversion for argument 1 from ‘std::string_view’ {aka ‘std::basic_string_view<char>’} to ‘std::__cxx11::basic_istringstream<char>::__string_type&&’ {aka ‘std::__cxx11::basic_string<char>&&’}
  643 |       basic_istringstream(__string_type&& __str,
      |                           ~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:638:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::ios_base::openmode, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; std::ios_base::openmode = std::ios_base::openmode; allocator_type = std::allocator<char>]’
  638 |       basic_istringstream(ios_base::openmode __mode, const allocator_type& __a)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:638:7: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/sstream:632:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>&&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  632 |       basic_istringstream(basic_istringstream&& __rhs)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:632:49: note:   no known conversion for argument 1 from ‘std::string_view’ {aka ‘std::basic_string_view<char>’} to ‘std::__cxx11::basic_istringstream<char>&&’
  632 |       basic_istringstream(basic_istringstream&& __rhs)
      |                           ~~~~~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:615:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const __string_type&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; __string_type = std::__cxx11::basic_string<char>; std::ios_base::openmode = std::ios_base::openmode]’
  615 |       basic_istringstream(const __string_type& __str,
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:615:48: note:   no known conversion for argument 1 from ‘std::string_view’ {aka ‘std::basic_string_view<char>’} to ‘const std::__cxx11::basic_istringstream<char>::__string_type&’ {aka ‘const std::__cxx11::basic_string<char>&’}
  615 |       basic_istringstream(const __string_type& __str,
      |                           ~~~~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:597:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; std::ios_base::openmode = std::ios_base::openmode]’
  597 |       basic_istringstream(ios_base::openmode __mode)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:597:46: note:   no known conversion for argument 1 from ‘std::string_view’ {aka ‘std::basic_string_view<char>’} to ‘std::ios_base::openmode’
  597 |       basic_istringstream(ios_base::openmode __mode)
      |                           ~~~~~~~~~~~~~~~~~~~^~~~~~
/usr/include/c++/14/sstream:580:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream() [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  580 |       basic_istringstream()
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:580:7: note:   candidate expects 0 arguments, 1 provided
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘std::string dbservice::api::ApiServer::urlDecode(std::string_view)’:
/home/<USER>/database-service-build/src/api/api_server.cpp:1094:58: error: no matching function for call to ‘std::__cxx11::basic_istringstream<char>::basic_istringstream(std::basic_string_view<char>)’
 1094 |             std::istringstream iss(input.substr(i + 1, 2));
      |                                                          ^
/usr/include/c++/14/sstream:663:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  663 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:663:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:1094:58: note:   ‘std::basic_string_view<char>’ is not derived from ‘const std::__cxx11::basic_string<char, std::char_traits<char>, _Alloc>’
 1094 |             std::istringstream iss(input.substr(i + 1, 2));
      |                                                          ^
/usr/include/c++/14/sstream:655:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, std::ios_base::openmode, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  655 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:655:9: note:   candidate expects 3 arguments, 1 provided
/usr/include/c++/14/sstream:649:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  649 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:649:9: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/sstream:643:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(__string_type&&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; __string_type = std::__cxx11::basic_string<char>; std::ios_base::openmode = std::ios_base::openmode]’
  643 |       basic_istringstream(__string_type&& __str,
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:643:43: note:   no known conversion for argument 1 from ‘std::basic_string_view<char>’ to ‘std::__cxx11::basic_istringstream<char>::__string_type&&’ {aka ‘std::__cxx11::basic_string<char>&&’}
  643 |       basic_istringstream(__string_type&& __str,
      |                           ~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:638:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::ios_base::openmode, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; std::ios_base::openmode = std::ios_base::openmode; allocator_type = std::allocator<char>]’
  638 |       basic_istringstream(ios_base::openmode __mode, const allocator_type& __a)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:638:7: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/sstream:632:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>&&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  632 |       basic_istringstream(basic_istringstream&& __rhs)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:632:49: note:   no known conversion for argument 1 from ‘std::basic_string_view<char>’ to ‘std::__cxx11::basic_istringstream<char>&&’
  632 |       basic_istringstream(basic_istringstream&& __rhs)
      |                           ~~~~~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:615:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const __string_type&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; __string_type = std::__cxx11::basic_string<char>; std::ios_base::openmode = std::ios_base::openmode]’
  615 |       basic_istringstream(const __string_type& __str,
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:615:48: note:   no known conversion for argument 1 from ‘std::basic_string_view<char>’ to ‘const std::__cxx11::basic_istringstream<char>::__string_type&’ {aka ‘const std::__cxx11::basic_string<char>&’}
  615 |       basic_istringstream(const __string_type& __str,
      |                           ~~~~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:597:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; std::ios_base::openmode = std::ios_base::openmode]’
  597 |       basic_istringstream(ios_base::openmode __mode)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:597:46: note:   no known conversion for argument 1 from ‘std::basic_string_view<char>’ to ‘std::ios_base::openmode’
  597 |       basic_istringstream(ios_base::openmode __mode)
      |                           ~~~~~~~~~~~~~~~~~~~^~~~~~
/usr/include/c++/14/sstream:580:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream() [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  580 |       basic_istringstream()
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:580:7: note:   candidate expects 0 arguments, 1 provided
[3/16] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
ninja: build stopped: subcommand failed.
