Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  5%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:22:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   22 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[ 16%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 22%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
[ 27%] Building CXX object CMakeFiles/database-service.dir/src/core/transaction.cpp.o
[ 33%] Building CXX object CMakeFiles/database-service.dir/src/database_service.cpp.o
[ 38%] Building CXX object CMakeFiles/database-service.dir/src/main.cpp.o
[ 44%] Building CXX object CMakeFiles/database-service.dir/src/metrics/database_metrics.cpp.o
[ 50%] Building CXX object CMakeFiles/database-service.dir/src/metrics/metrics_collector.cpp.o
[ 55%] Building CXX object CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o
[ 61%] Building CXX object CMakeFiles/database-service.dir/src/security/credential_store.cpp.o
/home/<USER>/database-service-build/src/security/credential_store.cpp: In member function ‘bool dbservice::security::CredentialStore::initialize(const std::string&)’:
/home/<USER>/database-service-build/src/security/credential_store.cpp:41:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   41 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/credential_store.cpp:6:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:42:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   42 |     SHA256_Update(&sha256, encryptionKey.c_str(), encryptionKey.length());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:43:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   43 |     SHA256_Final(key, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
[ 66%] Building CXX object CMakeFiles/database-service.dir/src/security/jwt.cpp.o
[ 72%] Building CXX object CMakeFiles/database-service.dir/src/security/security_manager.cpp.o
/home/<USER>/database-service-build/src/security/security_manager.cpp: In function ‘std::string dbservice::security::sha256(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:29:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   29 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/security_manager.cpp:12:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:30:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   30 |     SHA256_Update(&sha256, input.c_str(), input.size());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:31:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   31 |     SHA256_Final(hash, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<dbservice::security::TokenPair, dbservice::security::SecurityError> dbservice::security::SecurityManager::authenticate(const std::string&, const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:127:29: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  127 |         auto dbResult = conn->executeQuery(queryUser, {username});
      |                             ^~
In file included from /home/<USER>/database-service-build/src/security/security_manager.cpp:3:
/home/<USER>/database-service-build/include/database-service/core/transaction.hpp:11:7: note: forward declaration of ‘class dbservice::core::Connection’
   11 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:153:38: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  153 |             int updateOpResult = conn->executeNonQuery(updateQuery, {username});
      |                                      ^~
/home/<USER>/database-service-build/include/database-service/core/transaction.hpp:11:7: note: forward declaration of ‘class dbservice::core::Connection’
   11 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<std::__cxx11::basic_string<char>, dbservice::security::SecurityError> dbservice::security::SecurityManager::validateToken(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:247:29: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  247 |         auto dbResult = conn->executeQuery(query, {username});
      |                             ^~
/home/<USER>/database-service-build/include/database-service/core/transaction.hpp:11:7: note: forward declaration of ‘class dbservice::core::Connection’
   11 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::deleteUser(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:560:51: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘getConnection’
  560 |             auto permissionsResult = transaction->getConnection()->executeNonQuery(permissionsQuery, {username});
      |                                                   ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:565:52: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  565 |                 auto rollbackResult = transaction->rollback();
      |                                                    ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:575:52: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  575 |                 auto rollbackResult = transaction->rollback();
      |                                                    ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:584:46: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘getConnection’
  584 |             auto tokensResult = transaction->getConnection()->executeNonQuery(tokensQuery, {username});
      |                                              ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:589:52: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  589 |                 auto rollbackResult = transaction->rollback();
      |                                                    ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:599:52: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  599 |                 auto rollbackResult = transaction->rollback();
      |                                                    ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:608:44: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘getConnection’
  608 |             auto userResult = transaction->getConnection()->executeNonQuery(userQuery, {username});
      |                                            ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:613:52: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  613 |                 auto rollbackResult = transaction->rollback();
      |                                                    ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:623:52: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  623 |                 auto rollbackResult = transaction->rollback();
      |                                                    ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:631:46: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘commit’
  631 |             auto commitResult = transaction->commit();
      |                                              ^~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:643:48: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  643 |             auto rollbackResult = transaction->rollback();
      |                                                ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::createUsersTable()’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:678:26: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  678 |         int result = conn->executeNonQuery(query, {});
      |                          ^~
/home/<USER>/database-service-build/include/database-service/core/transaction.hpp:11:7: note: forward declaration of ‘class dbservice::core::Connection’
   11 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::createPermissionsTable()’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:717:26: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  717 |         int result = conn->executeNonQuery(query, {});
      |                          ^~
/home/<USER>/database-service-build/include/database-service/core/transaction.hpp:11:7: note: forward declaration of ‘class dbservice::core::Connection’
   11 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::createRefreshTokensTable()’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:757:26: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  757 |         int result = conn->executeNonQuery(query, {});
      |                          ^~
/home/<USER>/database-service-build/include/database-service/core/transaction.hpp:11:7: note: forward declaration of ‘class dbservice::core::Connection’
   11 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<dbservice::security::TokenPair, dbservice::security::SecurityError> dbservice::security::SecurityManager::generateTokenPair(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:882:33: error: invalid use of incomplete type ‘class dbservice::core::Connection’
  882 |             auto dbResult = conn->executeQuery(query, {username});
      |                                 ^~
/home/<USER>/database-service-build/include/database-service/core/transaction.hpp:11:7: note: forward declaration of ‘class dbservice::core::Connection’
   11 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<dbservice::security::TokenPair, dbservice::security::SecurityError> dbservice::security::SecurityManager::refreshAccessToken(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:1005:32: error: invalid use of incomplete type ‘class dbservice::core::Connection’
 1005 |             int dbResult = conn->executeNonQuery(query, {refreshToken});
      |                                ^~
/home/<USER>/database-service-build/include/database-service/core/transaction.hpp:11:7: note: forward declaration of ‘class dbservice::core::Connection’
   11 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::invalidateTokens(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:1039:28: error: invalid use of incomplete type ‘class dbservice::core::Connection’
 1039 |         int dbResult = conn->executeNonQuery(query, {username});
      |                            ^~
/home/<USER>/database-service-build/include/database-service/core/transaction.hpp:11:7: note: forward declaration of ‘class dbservice::core::Connection’
   11 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::storeRefreshToken(const std::string&, const std::string&, const std::chrono::_V2::system_clock::time_point&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:1087:28: error: invalid use of incomplete type ‘class dbservice::core::Connection’
 1087 |         int dbResult = conn->executeNonQuery(query, {refreshToken, username, std::string(expiresBuffer)});
      |                            ^~
/home/<USER>/database-service-build/include/database-service/core/transaction.hpp:11:7: note: forward declaration of ‘class dbservice::core::Connection’
   11 | class Connection;
      |       ^~~~~~~~~~
[ 77%] Building CXX object CMakeFiles/database-service.dir/src/utils/cache.cpp.o
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<std::__cxx11::basic_string<char>, dbservice::security::SecurityError> dbservice::security::SecurityManager::validateRefreshToken(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:1165:29: error: invalid use of incomplete type ‘class dbservice::core::Connection’
 1165 |         auto dbResult = conn->executeQuery(query, {refreshToken, usernameFromToken});
      |                             ^~
/home/<USER>/database-service-build/include/database-service/core/transaction.hpp:11:7: note: forward declaration of ‘class dbservice::core::Connection’
   11 | class Connection;
      |       ^~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘void dbservice::security::SecurityManager::cleanupExpiredRefreshTokens()’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:1195:28: error: invalid use of incomplete type ‘class dbservice::core::Connection’
 1195 |         int dbResult = conn->executeNonQuery(query, {});
      |                            ^~
/home/<USER>/database-service-build/include/database-service/core/transaction.hpp:11:7: note: forward declaration of ‘class dbservice::core::Connection’
   11 | class Connection;
      |       ^~~~~~~~~~
make[2]: *** [CMakeFiles/database-service.dir/build.make:247: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
