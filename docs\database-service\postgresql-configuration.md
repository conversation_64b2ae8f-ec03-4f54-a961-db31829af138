# PostgreSQL Configuration

This document provides comprehensive information about the PostgreSQL configuration for the Database Service, including the dedicated volume setup, configuration details, and management procedures.

## Overview

The Database Service uses PostgreSQL 17.5 as its primary database server, configured with a dedicated 98GB volume for optimal performance and data isolation.

## Server Configuration

### PostgreSQL Installation Details

- **Version**: PostgreSQL 17.5 (Ubuntu 17.5-1.pgdg24.04+1)
- **Server**: git.chcit.org (10.0.10.110)
- **Operating System**: Ubuntu 24.04 LTS
- **Installation Method**: APT package manager

### Data Directory Configuration

**Current Configuration (Post-Migration):**
- **Data Directory**: `/pgsql/data/17/main`
- **Volume**: Dedicated 98GB volume (`/dev/xvdb1`)
- **Available Space**: 93GB (as of migration)
- **Mount Point**: `/pgsql/data`
- **File System**: ext4

**Previous Configuration (Pre-Migration):**
- **Data Directory**: `/var/lib/postgresql/17/main` (system volume)
- **Status**: Migrated to dedicated volume on 2025-06-19

### Volume Information

```bash
# Check volume status
df -h /pgsql/data

# Expected output:
Filesystem      Size  Used Avail Use% Mounted on
/dev/xvdb1       98G   40M   93G   1% /pgsql/data
```

## Configuration Files

### Primary Configuration

- **Main Config**: `/etc/postgresql/17/main/postgresql.conf`
- **Authentication**: `/etc/postgresql/17/main/pg_hba.conf`
- **Environment**: `/etc/postgresql/17/main/environment`

### Key Configuration Changes

The following configuration was updated during the dedicated volume migration:

```conf
# /etc/postgresql/17/main/postgresql.conf
data_directory = '/pgsql/data/17/main'
```

### Service Configuration

- **Systemd Service**: `postgresql.service`
- **Service Status**: Active and enabled
- **Auto-start**: Enabled on boot

## Database Architecture

### Multi-Database Setup

The PostgreSQL server hosts multiple databases for clear application separation:

```
PostgreSQL Server (git.chcit.org:5432)
├── database_service (Central Management Database)
│   ├── public schema
│   │   ├── applications
│   │   ├── users
│   │   ├── permissions
│   │   ├── database_instances
│   │   ├── application_databases
│   │   ├── audit_log
│   │   └── schema_version
│   └── metadata schema
├── git_repo_db (Git Repository Service Database)
│   ├── public schema
│   │   ├── repositories
│   │   ├── commits
│   │   └── branches
│   └── metadata schema
│       ├── repository_stats
│       └── user_activity
└── logging_db (Logging Service Database)
    ├── public schema
    │   ├── logs
    │   └── log_levels
    ├── archive schema
    │   └── historical_logs
    └── stats schema
        ├── log_statistics
        └── error_trends
```

### Database Users and Permissions

- **Superuser**: `postgres` (system administration)
- **Service User**: `database_service` (application access)
- **Application Users**: Created per application as needed

## Automated Migration Process

The PostgreSQL migration to the dedicated volume is handled automatically by the Install-Dependencies module (Option 8) in the deployment script.

### Migration Process

1. **Detection**: Checks if PostgreSQL is using system volume
2. **Volume Verification**: Confirms dedicated volume availability
3. **Service Stop**: Safely stops PostgreSQL service
4. **Data Copy**: Uses `rsync` to copy data to dedicated volume
5. **Configuration Update**: Updates `postgresql.conf` with new data directory
6. **Permissions**: Sets proper ownership and permissions
7. **Service Start**: Restarts PostgreSQL service
8. **Verification**: Confirms successful migration

### Migration Command Sequence

```bash
# 1. Stop PostgreSQL
sudo systemctl stop postgresql

# 2. Prepare dedicated volume
sudo chown postgres:postgres /pgsql/data
sudo chmod 700 /pgsql/data
sudo mkdir -p /pgsql/data/17
sudo chown postgres:postgres /pgsql/data/17

# 3. Copy data
sudo rsync -av /var/lib/postgresql/17/main/ /pgsql/data/17/main/
sudo chown -R postgres:postgres /pgsql/data/17/

# 4. Update configuration
sudo sed -i 's|/var/lib/postgresql/17/main|/pgsql/data/17/main|g' /etc/postgresql/17/main/postgresql.conf

# 5. Start PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

## Connection Configuration

### Database Service Connection

The Database Service connects to PostgreSQL using the following configuration:

```json
{
  "database": {
    "host": "localhost",
    "port": 5432,
    "name": "database_service",
    "user": "database_service",
    "password": "password2311",
    "ssl": {
      "enabled": true,
      "cert_path": "/etc/letsencrypt/live/git.chcit.org/fullchain.pem",
      "key_path": "/etc/letsencrypt/live/git.chcit.org/privkey.pem",
      "ca_path": "/etc/letsencrypt/live/git.chcit.org/chain.pem"
    },
    "pool": {
      "max_connections": 10
    }
  }
}
```

### Connection Pool Settings

- **Maximum Connections**: 10 (configurable)
- **Connection Timeout**: 30 seconds
- **Pool Management**: Automatic connection recycling
- **SSL Mode**: Required for production

## Performance Optimization

### Dedicated Volume Benefits

1. **I/O Isolation**: Database I/O separated from system operations
2. **Performance**: Dedicated disk bandwidth for database operations
3. **Scalability**: Independent volume can be resized as needed
4. **Backup**: Volume-level snapshots and backups
5. **Monitoring**: Dedicated volume metrics and monitoring

### Recommended Settings

For the 98GB dedicated volume, consider these PostgreSQL settings:

```conf
# Memory settings
shared_buffers = 2GB
effective_cache_size = 6GB
work_mem = 64MB
maintenance_work_mem = 512MB

# Checkpoint settings
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Connection settings
max_connections = 100
```

## Monitoring and Maintenance

### Health Checks

```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check data directory
sudo -u postgres psql -c "SHOW data_directory;"

# Check database sizes
sudo -u postgres psql -c "SELECT datname, pg_size_pretty(pg_database_size(datname)) FROM pg_database;"

# Check volume usage
df -h /pgsql/data
```

### Log Locations

- **PostgreSQL Logs**: `/var/log/postgresql/postgresql-17-main.log`
- **System Logs**: `journalctl -u postgresql`
- **Database Service Logs**: `/var/log/database-service/database-service.log`

### Backup Considerations

With the dedicated volume setup:

1. **Volume Snapshots**: Create EBS snapshots of `/dev/xvdb1`
2. **Logical Backups**: Use `pg_dump` for individual databases
3. **Point-in-Time Recovery**: Configure WAL archiving if needed

## Troubleshooting

### Common Issues

#### PostgreSQL Not Starting After Migration

```bash
# Check data directory permissions
ls -la /pgsql/data/17/main/
# Should be owned by postgres:postgres with 700 permissions

# Check configuration
sudo -u postgres psql -c "SHOW data_directory;"
# Should show /pgsql/data/17/main
```

#### Connection Issues

```bash
# Check if PostgreSQL is listening
sudo netstat -tuln | grep 5432

# Check authentication configuration
sudo cat /etc/postgresql/17/main/pg_hba.conf
```

#### Volume Issues

```bash
# Check volume mount
mount | grep pgsql

# Check volume space
df -h /pgsql/data

# Check volume health
sudo fsck /dev/xvdb1
```

### Recovery Procedures

If migration fails or data corruption occurs:

1. **Stop PostgreSQL**: `sudo systemctl stop postgresql`
2. **Restore from Backup**: Restore data to `/pgsql/data/17/main/`
3. **Fix Permissions**: `sudo chown -R postgres:postgres /pgsql/data/17/`
4. **Start Service**: `sudo systemctl start postgresql`

## Security Considerations

### File Permissions

- **Data Directory**: `700` (postgres:postgres)
- **Configuration Files**: `644` (root:root)
- **SSL Certificates**: `600` (postgres:postgres)

### Network Security

- **Local Connections**: Unix socket authentication
- **Remote Connections**: SSL required
- **Firewall**: Port 5432 restricted to application servers

### Access Control

- **Superuser Access**: Limited to system administrators
- **Application Access**: Dedicated service accounts
- **Audit Logging**: All connections and queries logged

## Deployment Integration

The PostgreSQL configuration is automatically managed by the deployment script:

- **Option 8**: Install Dependencies (includes PostgreSQL migration)
- **Option 11**: Initialize Database (creates databases and schemas)
- **Validation**: Automatic verification of configuration

For manual deployment, ensure PostgreSQL is properly configured before deploying the Database Service application.
