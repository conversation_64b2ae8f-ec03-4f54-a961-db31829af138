#include "database-service/schema/schema_manager.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/utils/logger.hpp"
#include <format> // C++20 feature
#include <filesystem>
#include <fstream>
#include <sstream>
#include <regex>
#include <expected> // Added for std::expected

namespace dbservice::schema {

using dbservice::core::ManagedConnection; // Added for RAII connection handling

SchemaManager::SchemaManager(std::shared_ptr<core::ConnectionManager> connectionManager, 
                           const std::string& schemaDirectory)
    : connectionManager_(connectionManager),
      schemaDirectory_(schemaDirectory),
      initialized_(false) {
}

SchemaManager::~SchemaManager() {
}

std::expected<void, SchemaError> SchemaManager::initialize() {
    if (initialized_) {
        utils::Logger::info("Schema manager already initialized.");
        return {};
    }
    
    utils::Logger::info("Initializing schema manager...");
    
    auto migrationsTableResult = createMigrationsTable();
    if (!migrationsTableResult) {
        // Error already logged by createMigrationsTable
        return std::unexpected(SchemaError{SchemaErrorType::InitializationFailed, 
                               "Failed to create migrations table: " + migrationsTableResult.error().message, 
                               migrationsTableResult.error().code});
    }
    
    auto loadFilesResult = loadMigrationFiles();
    if (!loadFilesResult) {
        // Error already logged by loadMigrationFiles
        return std::unexpected(SchemaError{SchemaErrorType::InitializationFailed, 
                               "Failed to load migration files: " + loadFilesResult.error().message, 
                               loadFilesResult.error().code});
    }
    
    initialized_ = true;
    utils::Logger::info("Schema manager initialized successfully");
    return {};
}

std::expected<void, SchemaError> SchemaManager::createSchema(const std::string& schemaName) {
    if (!initialized_) {
        return std::unexpected(SchemaError{SchemaErrorType::InitializationFailed, "SchemaManager is not initialized. Call initialize() first."});
    }
    
    auto existsResult = schemaExists(schemaName);
    if (!existsResult) {
        return std::unexpected(SchemaError{SchemaErrorType::SchemaCreationFailure, 
                               "Failed to check if schema '" + schemaName + "' exists: " + existsResult.error().message, 
                               existsResult.error().code});
    }
    if (*existsResult) {
        utils::Logger::info(std::format("Schema {} already exists. Creation skipped.", schemaName));
        return {};
    }

    ManagedConnection managed_conn(*connectionManager_);
    if (!managed_conn) {
        std::string err_msg = "Failed to acquire database connection for creating schema '" + schemaName + "'.";
        if (managed_conn.getError()) err_msg += " Details: " + *managed_conn.getError();
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::ConnectionFailed, err_msg});
    }

    try {
        std::string query = std::format("CREATE SCHEMA {}", schemaName); // IF NOT EXISTS is handled by prior check
        int result = managed_conn->executeNonQuery(query, {});
        
        if (result < 0) {
            std::string err_msg = std::format("Query execution failed to create schema '{}'.", schemaName);
            utils::Logger::error(err_msg);
            return std::unexpected(SchemaError{SchemaErrorType::QueryFailed, err_msg, result});
        }
        
        utils::Logger::info(std::format("Schema {} created successfully.", schemaName));
        return {};
    } catch (const pqxx::sql_error& e) {
        std::string err_msg = std::format("SQL error during schema '{}' creation: {}. Query: {}", schemaName, e.what(), e.query());
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::SchemaCreationFailure, err_msg});
    } catch (const std::exception& e) {
        std::string err_msg = std::format("Exception during schema '{}' creation: {}", schemaName, e.what());
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::SchemaCreationFailure, err_msg});
    }
}

std::expected<void, SchemaError> SchemaManager::dropSchema(const std::string& schemaName) {
    if (!initialized_) {
        return std::unexpected(SchemaError{SchemaErrorType::InitializationFailed, "SchemaManager is not initialized. Call initialize() first."});
    }

    auto existsResult = schemaExists(schemaName);
    if (!existsResult) {
        return std::unexpected(SchemaError{SchemaErrorType::SchemaDropFailure, 
                               "Failed to check if schema '" + schemaName + "' exists before dropping: " + existsResult.error().message, 
                               existsResult.error().code});
    }
    if (!*existsResult) {
        utils::Logger::info(std::format("Schema {} does not exist. Drop skipped.", schemaName));
        return {};
    }

    ManagedConnection managed_conn(*connectionManager_);
    if (!managed_conn) {
        std::string err_msg = "Failed to acquire database connection for dropping schema '" + schemaName + "'.";
        if (managed_conn.getError()) err_msg += " Details: " + *managed_conn.getError();
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::ConnectionFailed, err_msg});
    }

    try {
        std::string query = std::format("DROP SCHEMA {} CASCADE", schemaName); // IF EXISTS handled by prior check
        int result = managed_conn->executeNonQuery(query, {});
        
        if (result < 0) {
            std::string err_msg = std::format("Query execution failed to drop schema '{}'.", schemaName);
            utils::Logger::error(err_msg);
            return std::unexpected(SchemaError{SchemaErrorType::QueryFailed, err_msg, result});
        }
        
        utils::Logger::info(std::format("Schema {} dropped successfully.", schemaName));
        return {};
    } catch (const pqxx::sql_error& e) {
        std::string err_msg = std::format("SQL error during schema '{}' drop: {}. Query: {}", schemaName, e.what(), e.query());
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::SchemaDropFailure, err_msg});
    } catch (const std::exception& e) {
        std::string err_msg = std::format("Exception during schema '{}' drop: {}", schemaName, e.what());
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::SchemaDropFailure, err_msg});
    }
}

std::expected<bool, SchemaError> SchemaManager::schemaExists(const std::string& schemaName) {
    if (!initialized_) {
        // This is a read operation, so perhaps allow calling without full init if it only checks DB?
        // For consistency, let's require initialization.
        return std::unexpected(SchemaError{SchemaErrorType::InitializationFailed, "SchemaManager is not initialized. Call initialize() first."});
    }
    
    ManagedConnection managed_conn(*connectionManager_);
    if (!managed_conn) {
        std::string err_msg = "Failed to acquire database connection for schema existence check ('" + schemaName + "').";
        if (managed_conn.getError()) err_msg += " Details: " + *managed_conn.getError();
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::ConnectionFailed, err_msg});
    }

    try {
        std::string query = "SELECT schema_name FROM information_schema.schemata WHERE schema_name = $1";
        // Assuming Connection class's executeQuery returns std::vector<std::vector<std::string>> or throws.
        auto result_rows = managed_conn->executeQuery(query, {schemaName});
        return !result_rows.empty();
    } catch (const pqxx::sql_error& e) {
        std::string err_msg = std::format("SQL error during schema '{}' existence check: {}. Query: {}", schemaName, e.what(), e.query());
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::QueryFailed, err_msg});
    } catch (const std::exception& e) {
        std::string err_msg = std::format("Exception during schema '{}' existence check: {}", schemaName, e.what());
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::Unknown, err_msg});
    }
}

std::expected<std::vector<std::string>, SchemaError> SchemaManager::getSchemas() {
    if (!initialized_) {
        return std::unexpected(SchemaError{SchemaErrorType::InitializationFailed, "SchemaManager is not initialized. Call initialize() first."});
    }
    
    ManagedConnection managed_conn(*connectionManager_);
    if (!managed_conn) {
        std::string err_msg = "Failed to acquire database connection for retrieving schema list.";
        if (managed_conn.getError()) err_msg += " Details: " + *managed_conn.getError();
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::ConnectionFailed, err_msg});
    }

    std::vector<std::string> schemas;
    try {
        std::string query = "SELECT schema_name FROM information_schema.schemata WHERE schema_name NOT IN ('pg_catalog', 'information_schema', 'pg_toast') AND schema_name NOT LIKE 'pg_temp_%' AND schema_name NOT LIKE 'pg_toast_temp_%' ORDER BY schema_name";
        auto result_rows = managed_conn->executeQuery(query, {});
        
        for (const auto& row : result_rows) {
            if (!row.empty()) {
                schemas.push_back(row[0]);
            }
        }
        return schemas;
    } catch (const pqxx::sql_error& e) {
        std::string err_msg = std::format("SQL error during schema list retrieval: {}. Query: {}", e.what(), e.query());
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::QueryFailed, err_msg});
    } catch (const std::exception& e) {
        std::string err_msg = std::format("Exception during schema list retrieval: {}", e.what());
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::Unknown, err_msg});
    }
}

std::expected<void, SchemaError> SchemaManager::applyMigration(const std::string& schemaName, const std::string& migrationName) {
    if (!initialized_) {
        return std::unexpected(SchemaError{SchemaErrorType::InitializationFailed, "SchemaManager is not initialized. Call initialize() first."});
    }
    
    if (migrationFiles_.find(migrationName) == migrationFiles_.end()) {
        std::string err_msg = std::format("Migration file '{}' not found in loaded migration files.", migrationName);
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::MigrationFileLoadFailure, err_msg});
    }

    auto appliedResult = getAppliedMigrations(schemaName);
    if (!appliedResult) {
        return std::unexpected(SchemaError{SchemaErrorType::MigrationApplyFailure, 
                               "Failed to get applied migrations for schema '" + schemaName + "': " + appliedResult.error().message,
                               appliedResult.error().code});
    }
    const auto& appliedMigrations = *appliedResult;
    if (std::find(appliedMigrations.begin(), appliedMigrations.end(), migrationName) != appliedMigrations.end()) {
        utils::Logger::info(std::format("Migration '{}' already applied to schema '{}'. Skipping.", migrationName, schemaName));
        return {};
    }

    std::shared_ptr<core::Transaction> transaction; // Assuming core::Transaction is the type
    try {
        transaction = connectionManager_->beginTransaction(); // This might need to return std::expected too.
        if (!transaction) {
            std::string err_msg = "Failed to begin database transaction for applying migration.";
            utils::Logger::error(err_msg);
            return std::unexpected(SchemaError{SchemaErrorType::ConnectionFailed, err_msg});
        }
    } catch (const std::exception& e) {
        std::string err_msg = std::format("Exception when trying to begin transaction for migration: {}", e.what());
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::ConnectionFailed, err_msg});
    }
    
    try {
        std::string script = migrationFiles_[migrationName];
        // Assuming Transaction::executeNonQuery returns int or throws
        int script_result = transaction->executeNonQuery(script, {});
        if (script_result < 0) {
            std::string err_msg = std::format("Failed to execute migration script '{}' for schema '{}'.", migrationName, schemaName);
            utils::Logger::error(err_msg);
            transaction->rollback(); // Attempt rollback
            return std::unexpected(SchemaError{SchemaErrorType::QueryFailed, err_msg, script_result});
        }
        
        std::string record_query = "INSERT INTO schema_migrations (schema_name, migration_name, applied_at) VALUES ($1, $2, NOW())";
        int record_result = transaction->executeNonQuery(record_query, {schemaName, migrationName});
        if (record_result < 0) {
            std::string err_msg = std::format("Failed to record migration '{}' for schema '{}' in schema_migrations table.", migrationName, schemaName);
            utils::Logger::error(err_msg);
            transaction->rollback(); // Attempt rollback
            return std::unexpected(SchemaError{SchemaErrorType::QueryFailed, err_msg, record_result});
        }
        
        if (!transaction->commit()) {
            std::string err_msg = std::format("Failed to commit transaction for migration '{}', schema '{}'.", migrationName, schemaName);
            utils::Logger::error(err_msg);
            // Rollback might have been implicitly done by failed commit, or might need explicit call depending on Transaction impl.
            return std::unexpected(SchemaError{SchemaErrorType::QueryFailed, err_msg});
        }
        
        utils::Logger::info(std::format("Migration '{}' applied to schema '{}' successfully.", migrationName, schemaName));
        return {};
    } catch (const pqxx::sql_error& e) {
        std::string err_msg = std::format("SQL error during migration '{}' application for schema '{}': {}. Query: {}", migrationName, schemaName, e.what(), e.query());
        utils::Logger::error(err_msg);
        if(transaction) transaction->rollback();
        return std::unexpected(SchemaError{SchemaErrorType::MigrationApplyFailure, err_msg});
    } catch (const std::exception& e) {
        std::string err_msg = std::format("Generic exception during migration '{}' application for schema '{}': {}", migrationName, schemaName, e.what());
        utils::Logger::error(err_msg);
        if(transaction) transaction->rollback();
        return std::unexpected(SchemaError{SchemaErrorType::MigrationApplyFailure, err_msg});
    }
}

std::expected<std::vector<std::string>, SchemaError> SchemaManager::getAppliedMigrations(const std::string& schemaName) {
    if (!initialized_) {
        return std::unexpected(SchemaError{SchemaErrorType::InitializationFailed, "SchemaManager is not initialized. Call initialize() first."});
    }

    ManagedConnection managed_conn(*connectionManager_);
    if (!managed_conn) {
        std::string err_msg = "Failed to acquire database connection for retrieving applied migrations for schema '" + schemaName + "'.";
        if (managed_conn.getError()) err_msg += " Details: " + *managed_conn.getError();
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::ConnectionFailed, err_msg});
    }

    std::vector<std::string> migrations;
    try {
        std::string query = "SELECT migration_name FROM schema_migrations WHERE schema_name = $1 ORDER BY applied_at";
        auto result_rows = managed_conn->executeQuery(query, {schemaName});
        
        for (const auto& row : result_rows) {
            if (!row.empty()) {
                migrations.push_back(row[0]);
            }
        }
        return migrations;
    } catch (const pqxx::sql_error& e) {
        std::string err_msg = std::format("SQL error retrieving applied migrations for schema '{}': {}. Query: {}", schemaName, e.what(), e.query());
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::QueryFailed, err_msg});
    } catch (const std::exception& e) {
        std::string err_msg = std::format("Exception retrieving applied migrations for schema '{}': {}", schemaName, e.what());
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::Unknown, err_msg});
    }
}

std::expected<void, SchemaError> SchemaManager::createMigrationsTable() {
    ManagedConnection managed_conn(*connectionManager_);
    if (!managed_conn) {
        std::string err_msg = "Failed to acquire database connection for creating migrations table.";
        if (managed_conn.getError()) {
            err_msg += " Details: " + *managed_conn.getError();
        }
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::ConnectionFailed, err_msg});
    }

    try {
        std::string query = R"(
            CREATE TABLE IF NOT EXISTS schema_migrations (
                id SERIAL PRIMARY KEY,
                schema_name VARCHAR(255) NOT NULL,
                migration_name VARCHAR(255) NOT NULL,
                applied_at TIMESTAMP NOT NULL,
                UNIQUE (schema_name, migration_name)
            )
        )";
        
        // Assuming Connection class's executeNonQuery returns int or throws.
        // Replace with actual Connection method if different.
        int result = managed_conn->executeNonQuery(query, {}); 
        
        if (result < 0) { 
            std::string err_msg = "Query execution failed to create migrations table.";
            utils::Logger::error(err_msg);
            return std::unexpected(SchemaError{SchemaErrorType::QueryFailed, err_msg, result});
        }
        
        utils::Logger::info("Migrations table created or already exists.");
        return {};
    } catch (const pqxx::sql_error& e) { 
        std::string err_msg = std::format("SQL error during migrations table creation: {}. Query: {}", e.what(), e.query());
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::QueryFailed, err_msg}); 
    } catch (const std::exception& e) {
        std::string err_msg = std::format("Exception during migrations table creation: {}", e.what());
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::Unknown, err_msg});
    }
}

std::expected<void, SchemaError> SchemaManager::loadMigrationFiles() {
    try {
        if (schemaDirectory_.empty()) {
            utils::Logger::info("Schema directory path is empty. No migration files will be loaded.");
            migrationFiles_.clear();
            return {}; 
        }
                if (!std::filesystem::exists(std::filesystem::path(schemaDirectory_))) {
            utils::Logger::info(std::format("Schema directory not found: {}. No migration files will be loaded.", schemaDirectory_));
            migrationFiles_.clear();
            return {}; 
        }
        if (!std::filesystem::is_directory(schemaDirectory_)) {
             std::string err_msg = std::format("Schema directory path is not a directory: {}", schemaDirectory_);
             utils::Logger::error(err_msg);
             return std::unexpected(SchemaError{SchemaErrorType::FilesystemError, err_msg});
        }

        migrationFiles_.clear();
        for (const auto& entry : std::filesystem::directory_iterator(schemaDirectory_)) {
            if (entry.is_regular_file() && entry.path().extension() == ".sql") {
                std::string filename = entry.path().filename().string();
                std::ifstream file(entry.path());
                if (!file.is_open()) {
                    std::string err_msg = std::format("Failed to open migration file: {}. Skipping.", filename);
                    utils::Logger::warning(err_msg);
                    // To strictly match old behavior of continuing, we don't return error here.
                    // If one failed file should abort, uncomment below:
                    // return std::unexpected(SchemaError{SchemaErrorType::FilesystemError, err_msg});
                    continue; 
                }
                std::stringstream buffer;
                buffer << file.rdbuf();
                migrationFiles_[filename] = buffer.str();
                utils::Logger::debug(std::format("Loaded migration file: {}", filename));
            }
        }
        utils::Logger::info(std::format("Loaded {} migration files from {}.", migrationFiles_.size(), schemaDirectory_));
        return {};
    } catch (const std::filesystem::filesystem_error& e) {
        std::string err_msg = std::format("Filesystem error during migration files loading from directory '{}': {}. Error code: {}.", schemaDirectory_, e.what(), e.code().value());
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::FilesystemError, err_msg, e.code().value()});
    } catch (const std::exception& e) { 
        std::string err_msg = std::format("Generic exception during migration files loading: {}", e.what());
        utils::Logger::error(err_msg);
        return std::unexpected(SchemaError{SchemaErrorType::Unknown, err_msg});
    }
}

} // namespace dbservice::schema
