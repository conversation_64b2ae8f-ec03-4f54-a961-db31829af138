Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  4%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
[  9%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:91:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   91 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[ 13%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 18%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<std::shared_ptr<dbservice::core::Connection>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::getConnection(std::chrono::milliseconds)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:187:31: warning: unused parameter ‘timeout’ [-Wunused-parameter]
  187 |     std::chrono::milliseconds timeout) {
      |     ~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<nlohmann::json_abi_v3_11_3::basic_json<>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::executeCachedQuery(const std::string&, const std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > >&, std::chrono::milliseconds)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:328:31: warning: unused parameter ‘ttl’ [-Wunused-parameter]
  328 |     std::chrono::milliseconds ttl) {
      |     ~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<std::shared_ptr<dbservice::core::Transaction>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::beginTransaction(dbservice::core::TransactionIsolationLevel, bool, bool)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:370:31: warning: unused parameter ‘isolationLevel’ [-Wunused-parameter]
  370 |     TransactionIsolationLevel isolationLevel,
      |     ~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:371:10: warning: unused parameter ‘readOnly’ [-Wunused-parameter]
  371 |     bool readOnly,
      |     ~~~~~^~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:372:10: warning: unused parameter ‘deferrable’ [-Wunused-parameter]
  372 |     bool deferrable) {
      |     ~~~~~^~~~~~~~~~
[ 22%] Building CXX object CMakeFiles/database-service.dir/src/core/transaction.cpp.o
[ 27%] Building CXX object CMakeFiles/database-service.dir/src/database_service.cpp.o
[ 31%] Building CXX object CMakeFiles/database-service.dir/src/main.cpp.o
[ 36%] Building CXX object CMakeFiles/database-service.dir/src/metrics/database_metrics.cpp.o
[ 40%] Building CXX object CMakeFiles/database-service.dir/src/metrics/metrics_collector.cpp.o
[ 45%] Building CXX object CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o
[ 50%] Building CXX object CMakeFiles/database-service.dir/src/security/audit_logger.cpp.o
[ 54%] Building CXX object CMakeFiles/database-service.dir/src/security/credential_store.cpp.o
/home/<USER>/database-service-build/src/security/credential_store.cpp: In member function ‘bool dbservice::security::CredentialStore::initialize(const std::string&)’:
/home/<USER>/database-service-build/src/security/credential_store.cpp:46:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   46 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/credential_store.cpp:6:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:47:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   47 |     SHA256_Update(&sha256, encryptionKey.c_str(), encryptionKey.length());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:48:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   48 |     SHA256_Final(key, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
[ 59%] Building CXX object CMakeFiles/database-service.dir/src/security/jwt.cpp.o
[ 63%] Building CXX object CMakeFiles/database-service.dir/src/security/rate_limiter.cpp.o
[ 68%] Building CXX object CMakeFiles/database-service.dir/src/security/security_manager.cpp.o
/home/<USER>/database-service-build/src/security/rate_limiter.cpp: In member function ‘dbservice::security::RateLimitResult dbservice::security::RateLimiter::checkLimit(const std::string&, const std::string&, const std::string&)’:
/home/<USER>/database-service-build/src/security/rate_limiter.cpp:62:24: warning: unused parameter ‘userAgent’ [-Wunused-parameter]
   62 |     const std::string& userAgent) {
      |     ~~~~~~~~~~~~~~~~~~~^~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In function ‘std::string dbservice::security::sha256(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:30:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   30 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/security_manager.cpp:13:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:31:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   31 |     SHA256_Update(&sha256, input.c_str(), input.size());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:32:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   32 |     SHA256_Final(hash, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
[ 72%] Building CXX object CMakeFiles/database-service.dir/src/service/application_manager.cpp.o
[ 77%] Building CXX object CMakeFiles/database-service.dir/src/service/database_instance_manager.cpp.o
[ 81%] Building CXX object CMakeFiles/database-service.dir/src/utils/cache.cpp.o
[ 86%] Building CXX object CMakeFiles/database-service.dir/src/utils/config_manager.cpp.o
[ 90%] Building CXX object CMakeFiles/database-service.dir/src/utils/logger.cpp.o
[ 95%] Building CXX object CMakeFiles/database-service.dir/src/utils/thread_pool.cpp.o
[100%] Linking CXX executable bin/database-service
/usr/bin/ld: CMakeFiles/database-service.dir/src/api/route_controller.cpp.o: in function `std::_Function_handler<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > (dbservice::api::ParsedRequest const&), dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)::{lambda(dbservice::api::ParsedRequest const&)#3}>::_M_invoke(std::_Any_data const&, dbservice::api::ParsedRequest const&)':
route_controller.cpp:(.text+0x1e): undefined reference to `dbservice::api::RouteController::handleLogout[abi:cxx11](dbservice::api::ParsedRequest const&)'
/usr/bin/ld: CMakeFiles/database-service.dir/src/api/route_controller.cpp.o: in function `std::_Function_handler<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > (dbservice::api::ParsedRequest const&), dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)::{lambda(dbservice::api::ParsedRequest const&)#6}>::_M_invoke(std::_Any_data const&, dbservice::api::ParsedRequest const&)':
route_controller.cpp:(.text+0x5e): undefined reference to `dbservice::api::RouteController::handleTransaction[abi:cxx11](dbservice::api::ParsedRequest const&)'
/usr/bin/ld: CMakeFiles/database-service.dir/src/api/route_controller.cpp.o: in function `std::_Function_handler<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > (dbservice::api::ParsedRequest const&), dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)::{lambda(dbservice::api::ParsedRequest const&)#7}>::_M_invoke(std::_Any_data const&, dbservice::api::ParsedRequest const&)':
route_controller.cpp:(.text+0x9e): undefined reference to `dbservice::api::RouteController::handleDatabaseMetrics[abi:cxx11](dbservice::api::ParsedRequest const&)'
/usr/bin/ld: CMakeFiles/database-service.dir/src/api/route_controller.cpp.o: in function `std::_Function_handler<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > (dbservice::api::ParsedRequest const&), dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)::{lambda(dbservice::api::ParsedRequest const&)#8}>::_M_invoke(std::_Any_data const&, dbservice::api::ParsedRequest const&)':
route_controller.cpp:(.text+0xde): undefined reference to `dbservice::api::RouteController::handleStoreCredentials[abi:cxx11](dbservice::api::ParsedRequest const&)'
/usr/bin/ld: CMakeFiles/database-service.dir/src/api/route_controller.cpp.o: in function `std::_Function_handler<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > (dbservice::api::ParsedRequest const&), dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)::{lambda(dbservice::api::ParsedRequest const&)#9}>::_M_invoke(std::_Any_data const&, dbservice::api::ParsedRequest const&)':
route_controller.cpp:(.text+0x11e): undefined reference to `dbservice::api::RouteController::handleGetCredentials[abi:cxx11](dbservice::api::ParsedRequest const&)'
/usr/bin/ld: CMakeFiles/database-service.dir/src/api/route_controller.cpp.o: in function `std::_Function_handler<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > (dbservice::api::ParsedRequest const&), dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)::{lambda(dbservice::api::ParsedRequest const&)#11}>::_M_invoke(std::_Any_data const&, dbservice::api::ParsedRequest const&)':
route_controller.cpp:(.text+0x15e): undefined reference to `dbservice::api::RouteController::handleListApplications[abi:cxx11](dbservice::api::ParsedRequest const&)'
/usr/bin/ld: CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o: in function `dbservice::core::ConnectionManager::ConnectionManager(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, unsigned long, dbservice::core::SSLConfig const&)':
connection_manager.cpp:(.text+0x44e7): undefined reference to `dbservice::core::ConnectionManager::sslModeToString[abi:cxx11](dbservice::core::SSLMode)'
/usr/bin/ld: CMakeFiles/database-service.dir/src/database_service.cpp.o: in function `dbservice::DatabaseService::stop()':
database_service.cpp:(.text+0x34c8): undefined reference to `dbservice::core::ConnectionManager::shutdown()'
/usr/bin/ld: CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o: in function `dbservice::schema::SchemaManager::getSchemas[abi:cxx11]()':
schema_manager.cpp:(.text+0x1233): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: schema_manager.cpp:(.text+0x123b): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x1352): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x158b): undefined reference to `dbservice::core::ManagedConnection::getError[abi:cxx11]() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x159e): undefined reference to `dbservice::core::ManagedConnection::getError[abi:cxx11]() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x16b6): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o: in function `dbservice::schema::SchemaManager::getAppliedMigrations(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)':
schema_manager.cpp:(.text+0x1ac5): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: schema_manager.cpp:(.text+0x1acd): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x1beb): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x1ef1): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: schema_manager.cpp:(.text+0x1fb2): undefined reference to `dbservice::core::ManagedConnection::getError[abi:cxx11]() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x1fbf): undefined reference to `dbservice::core::ManagedConnection::getError[abi:cxx11]() const'
/usr/bin/ld: CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o: in function `dbservice::schema::SchemaManager::schemaExists(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)':
schema_manager.cpp:(.text+0x23c6): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: schema_manager.cpp:(.text+0x23ce): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x24d7): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x261f): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: schema_manager.cpp:(.text+0x26e1): undefined reference to `dbservice::core::ManagedConnection::getError[abi:cxx11]() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x26ee): undefined reference to `dbservice::core::ManagedConnection::getError[abi:cxx11]() const'
/usr/bin/ld: CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o: in function `dbservice::schema::SchemaManager::dropSchema(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)':
schema_manager.cpp:(.text+0x3bea): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: schema_manager.cpp:(.text+0x3bf2): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x3caa): undefined reference to `dbservice::core::ManagedConnection::getError[abi:cxx11]() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x3cb7): undefined reference to `dbservice::core::ManagedConnection::getError[abi:cxx11]() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x3dea): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: schema_manager.cpp:(.text+0x405e): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o: in function `dbservice::schema::SchemaManager::createSchema(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)':
schema_manager.cpp:(.text+0x450a): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: schema_manager.cpp:(.text+0x4512): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x45ca): undefined reference to `dbservice::core::ManagedConnection::getError[abi:cxx11]() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x45d7): undefined reference to `dbservice::core::ManagedConnection::getError[abi:cxx11]() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x470a): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: schema_manager.cpp:(.text+0x497e): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o: in function `dbservice::schema::SchemaManager::createMigrationsTable()':
schema_manager.cpp:(.text+0x6228): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: schema_manager.cpp:(.text+0x6230): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x62a5): undefined reference to `dbservice::core::ManagedConnection::getError[abi:cxx11]() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x62b6): undefined reference to `dbservice::core::ManagedConnection::getError[abi:cxx11]() const'
/usr/bin/ld: schema_manager.cpp:(.text+0x647f): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: schema_manager.cpp:(.text+0x6522): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o: in function `dbservice::schema::SchemaManager::getSchemas[abi:cxx11]() [clone .cold]':
schema_manager.cpp:(.text.unlikely+0x1d0): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o: in function `dbservice::schema::SchemaManager::getAppliedMigrations(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&) [clone .cold]':
schema_manager.cpp:(.text.unlikely+0x618): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o: in function `dbservice::schema::SchemaManager::schemaExists(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&) [clone .cold]':
schema_manager.cpp:(.text.unlikely+0xa66): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o: in function `dbservice::schema::SchemaManager::dropSchema(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&) [clone .cold]':
schema_manager.cpp:(.text.unlikely+0x14c7): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o: in function `dbservice::schema::SchemaManager::createSchema(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&) [clone .cold]':
schema_manager.cpp:(.text.unlikely+0x1879): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o:schema_manager.cpp:(.text.unlikely+0x2502): more undefined references to `dbservice::core::ManagedConnection::~ManagedConnection()' follow
/usr/bin/ld: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o: in function `dbservice::security::SecurityManager::createUsersTable()':
security_manager.cpp:(.text+0x2c68): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: security_manager.cpp:(.text+0x2c70): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: security_manager.cpp:(.text+0x2d9f): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: security_manager.cpp:(.text+0x2de8): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o: in function `dbservice::security::SecurityManager::createPermissionsTable()':
security_manager.cpp:(.text+0x3318): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: security_manager.cpp:(.text+0x3320): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: security_manager.cpp:(.text+0x344f): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: security_manager.cpp:(.text+0x3498): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o: in function `dbservice::security::SecurityManager::createRefreshTokensTable()':
security_manager.cpp:(.text+0x39c8): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: security_manager.cpp:(.text+0x39d0): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: security_manager.cpp:(.text+0x3aff): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: security_manager.cpp:(.text+0x3b48): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o: in function `dbservice::security::SecurityManager::cleanupExpiredRefreshTokens()':
security_manager.cpp:(.text+0x4f5e): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: security_manager.cpp:(.text+0x4f66): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: security_manager.cpp:(.text+0x4fcf): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: security_manager.cpp:(.text+0x50b2): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o: in function `dbservice::security::SecurityManager::validateRefreshToken(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)':
security_manager.cpp:(.text+0x640d): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: security_manager.cpp:(.text+0x6415): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: security_manager.cpp:(.text+0x6546): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: security_manager.cpp:(.text+0x66c6): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o: in function `dbservice::security::SecurityManager::validateToken(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)':
security_manager.cpp:(.text+0x7493): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: security_manager.cpp:(.text+0x749b): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: security_manager.cpp:(.text+0x75d5): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: security_manager.cpp:(.text+0x768a): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o: in function `dbservice::security::SecurityManager::storeRefreshToken(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::chrono::time_point<std::chrono::_V2::system_clock, std::chrono::duration<long, std::ratio<1l, 1000000000l> > > const&)':
security_manager.cpp:(.text+0xd5f1): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: security_manager.cpp:(.text+0xd5f9): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: security_manager.cpp:(.text+0xd79a): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: security_manager.cpp:(.text+0xdbdd): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o: in function `dbservice::security::SecurityManager::generateTokenPair(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)':
security_manager.cpp:(.text+0xe430): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: security_manager.cpp:(.text+0xe438): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: security_manager.cpp:(.text+0xe4b0): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: security_manager.cpp:(.text+0xe6af): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: security_manager.cpp:(.text+0xe757): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o: in function `dbservice::security::SecurityManager::authenticate(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)':
security_manager.cpp:(.text+0xf033): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: security_manager.cpp:(.text+0xf03b): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: security_manager.cpp:(.text+0xf221): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: security_manager.cpp:(.text+0xf2ce): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: security_manager.cpp:(.text+0xf8bd): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o: in function `dbservice::security::SecurityManager::refreshAccessToken(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)':
security_manager.cpp:(.text+0x10293): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: security_manager.cpp:(.text+0x1029b): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: security_manager.cpp:(.text+0x10311): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: security_manager.cpp:(.text+0x103f2): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o: in function `dbservice::security::SecurityManager::invalidateTokens(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)':
security_manager.cpp:(.text+0x11aab): undefined reference to `dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)'
/usr/bin/ld: security_manager.cpp:(.text+0x11ab3): undefined reference to `dbservice::core::ManagedConnection::operator bool() const'
/usr/bin/ld: security_manager.cpp:(.text+0x11b39): undefined reference to `dbservice::core::ManagedConnection::operator->() const'
/usr/bin/ld: security_manager.cpp:(.text+0x11c30): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o: in function `dbservice::security::SecurityManager::createUsersTable() [clone .cold]':
security_manager.cpp:(.text.unlikely+0x583): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o: in function `dbservice::security::SecurityManager::createPermissionsTable() [clone .cold]':
security_manager.cpp:(.text.unlikely+0x7df): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o: in function `dbservice::security::SecurityManager::createRefreshTokensTable() [clone .cold]':
security_manager.cpp:(.text.unlikely+0xa3b): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o: in function `dbservice::security::SecurityManager::cleanupExpiredRefreshTokens() [clone .cold]':
security_manager.cpp:(.text.unlikely+0x1007): undefined reference to `dbservice::core::ManagedConnection::~ManagedConnection()'
/usr/bin/ld: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o:security_manager.cpp:(.text.unlikely+0x1336): more undefined references to `dbservice::core::ManagedConnection::~ManagedConnection()' follow
collect2: error: ld returned 1 exit status
make[2]: *** [CMakeFiles/database-service.dir/build.make:427: bin/database-service] Error 1
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
