# Import the Logger module as the very first step
Import-Module "$PSScriptRoot\Modules\Logger\Logger.psm1" -Force
# Only initialize logger ONCE, right after import
if (-not (Get-LogFilePath)) {
    Initialize-Logger -Level "Debug" -ToConsole $true -ToFile $true
}
$logPath = Get-LogFilePath
Write-Host "Logger initialized. Log file path: $logPath" -ForegroundColor Cyan
if (-not $logPath) {
    Write-Host "[DIAG WARNING] Logger did NOT create a log file!" -ForegroundColor Red
}

# Database Service Deployment Script (Modular Version)

<#
.SYNOPSIS
    Main deployment script for the Database Service.

.DESCRIPTION
    This script provides a menu-driven interface for deploying the Database Service.
    It handles configuration management, SSH connections, and deployment tasks.

.NOTES
    File Name      : deploy-database-service-modular.ps1
    Author         : Database Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>

# Suppress output during module loading
$ErrorActionPreference = 'SilentlyContinue'
$ProgressPreference = 'SilentlyContinue'
$WarningPreference = 'SilentlyContinue'

# IMPORTANT: Disable file logging in this script to avoid permission issues
# $script:LoggingEnabled = $false
# $global:LoggingEnabled = $false

# Print script root and expected logs directory for diagnostics
Write-Host "[DIAG] PSScriptRoot: $PSScriptRoot" -ForegroundColor Yellow
$expectedLogsDir = Join-Path -Path $PSScriptRoot -ChildPath "logs"
Write-Host "[DIAG] Expected logs directory: $expectedLogsDir" -ForegroundColor Yellow

# Make sure the logging functions are available in the global scope
if (-not (Get-Command -Name Write-SimpleLog -ErrorAction SilentlyContinue)) {
    function global:Write-SimpleLog {
        param(
            [string]$Message, 
            [System.ConsoleColor]$Color = [System.ConsoleColor]::White
        )
        # For UI output, don't show timestamp
        Write-Host $Message -ForegroundColor $Color
    }
}

if (-not (Get-Command -Name Write-Log -ErrorAction SilentlyContinue)) {
    function global:Write-Log {
        param(
            [string]$Message,
            [string]$Level = "Info",
            [string]$Component = "General",
            [System.ConsoleColor]$ForegroundColor = [System.ConsoleColor]::White,
            [switch]$NoNewline
        )
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        # For UI level, don't include timestamp
        if ($Level -eq "UI") {
            Write-Host $Message -ForegroundColor $ForegroundColor -NoNewline:$NoNewline
        } else {
            Write-Host "[$timestamp] [$Level] [$Component] $Message" -ForegroundColor $ForegroundColor -NoNewline:$NoNewline
        }
    }
}

# Must clear existing modules to avoid circular dependencies
$coreMods = @('Configuration', 'Common', 'Edit-ProjectSettings', 'Manage-DeploymentConfigurations', 'SSHManager', 'Test-SSHConnection', 'Build-Project')
foreach ($mod in $coreMods) {
    if (Get-Module -Name $mod) {
        Write-Host "Removing module: $mod" -ForegroundColor Yellow
        Remove-Module -Name $mod -Force -ErrorAction SilentlyContinue
    }
}

# Explicitly load Configuration module first
Write-Host "Loading core modules..." -ForegroundColor Cyan
try {
    $configModule = "$PSScriptRoot\Modules\Configuration.psm1"
    if (Test-Path $configModule) {
        Import-Module -Name $configModule -DisableNameChecking -ErrorAction Stop
        Write-Host "Configuration module loaded" -ForegroundColor Green

        # Initialize configuration explicitly once
        $config = Get-Configuration
        if ($config) {
            Write-Host "Configuration loaded successfully" -ForegroundColor Green
        }
    } else {
        Write-Host "Configuration module not found" -ForegroundColor Red
    }

    # Then load Common module
    $commonModule = "$PSScriptRoot\Common.psm1"
    if (Test-Path $commonModule) {
        Import-Module -Name $commonModule -DisableNameChecking -ErrorAction Stop
        Write-Host "Common module loaded" -ForegroundColor Green
    } else {
        Write-Host "Common module not found" -ForegroundColor Red
    }
} catch {
    Write-Host "Error loading core modules: $_" -ForegroundColor Red
}

# Now load all other modules
Write-Host "`nLoading functional modules..." -ForegroundColor Cyan

# Helper function to check if a file exists and determine its extension
function Get-ModuleFile {
    param([string]$BaseName)

    # Special handling for Logger module
    if ($BaseName -eq "Logger") {
        $psmFile = "$PSScriptRoot\Modules\Logger\Logger.psm1"
        if (Test-Path $psmFile) {
            return $psmFile
        } else {
            return $null
        }
    }
    $psmFile = "$PSScriptRoot\Modules\$BaseName.psm1"
    $ps1File = "$PSScriptRoot\Modules\$BaseName.ps1"

    if (Test-Path $psmFile) {
        return $psmFile
    } elseif (Test-Path $ps1File) {
        return $ps1File
    } else {
        return $null
    }
}

# Define core modules that should be loaded first (in this specific order)
$coreModuleNames = @(
    "Logger",
    "SSHManager",
    "Test-SSHConnection"
)

# Define all other modules
$moduleNames = @(
    "Build-Project",
    "Edit-ProjectSettings",
    "Get-ServiceStatus",
    "Initialize-Database",
    "Install-Dependencies",
    "Install-Service",
    "Invoke-CustomCommand",
    "Manage-ConfigurationBackups",
    "Manage-DatabaseSchemas",
    "Manage-DeploymentConfigurations",
    "Set-Environment",
    "Set-SSHKeys",
    "Setup-CertificateAccess",
    "Standardize-Configurations",
    "Start-DatabaseService",
    "Test-ServerReadiness",
    "Update-ServerConfig",
    "Build-UI",
    "Deploy-UI"
)

# Remove core modules from the regular module list to avoid duplicates
foreach ($coreMod in $coreModuleNames) {
    $moduleNames = $moduleNames | Where-Object { $_ -ne $coreMod }
}

$moduleList = @()
foreach ($name in $moduleNames) {
    $modulePath = Get-ModuleFile -BaseName $name
    if ($modulePath) {
        $moduleList += $modulePath
    } else {
        Write-SimpleLog "Module file not found for: $name" -Color Red
    }
}

# First load core modules in the specific order
Write-Host "`nLoading core modules in specific order..." -ForegroundColor Cyan
$coreModuleList = @()
foreach ($name in $coreModuleNames) {
    $modulePath = Get-ModuleFile -BaseName $name
    if ($modulePath) {
        $coreModuleList += $modulePath
    } else {
        Write-SimpleLog "Core module file not found for: $name" -Color Red
    }
}

# Load core modules first
$coreLoadedCount = 0
$totalCoreModules = $coreModuleList.Count

foreach ($module in $coreModuleList) {
    if (Test-Path $module) {
        try {
            # Extract module name for logging
            $moduleName = [System.IO.Path]::GetFileNameWithoutExtension($module)
            Write-Host "  Loading core module: $moduleName" -ForegroundColor Cyan

            # Import module with error handling - use Global scope for core modules
            Import-Module -Name $module -DisableNameChecking -Global -ErrorAction Stop
            $coreLoadedCount++
            Write-Host "  Successfully loaded core module: $moduleName" -ForegroundColor Green
        } catch {
            Write-Host "  Failed to import core module: $moduleName" -ForegroundColor Red
            Write-Host "  Error: $_" -ForegroundColor Red
        }
    } else {
        Write-Host "  Core module not found: $([System.IO.Path]::GetFileName($module))" -ForegroundColor Red
    }
}

Write-Host "Successfully loaded $coreLoadedCount of $totalCoreModules core modules" -ForegroundColor $(if ($coreLoadedCount -eq $totalCoreModules) { "Green" } else { "Yellow" })

# Now load the rest of the modules
Write-Host "`nLoading remaining modules..." -ForegroundColor Cyan

# Use a counter to show how many modules loaded successfully
$loadedCount = 0
$totalModules = $moduleList.Count

foreach ($module in $moduleList) {
    if (Test-Path $module) {
        try {
            # Extract module name for logging
            $moduleName = [System.IO.Path]::GetFileNameWithoutExtension($module)
            Write-Host "  Loading: $moduleName" -ForegroundColor Gray

            # Import module with error handling
            Import-Module -Name $module -DisableNameChecking -ErrorAction Stop
            $loadedCount++
        } catch {
            Write-Host "  Failed to import module: $moduleName" -ForegroundColor Red
            Write-Host "  Error: $_" -ForegroundColor Red
        }
    } else {
        Write-Host "  Module not found: $([System.IO.Path]::GetFileName($module))" -ForegroundColor Red
    }
}

Write-Host "Successfully loaded $loadedCount of $totalModules modules" -ForegroundColor $(if ($loadedCount -eq $totalModules) { "Green" } else { "Yellow" })

$script:CurrentBuildOutputPath = $null

# Validate function availability
$requiredFunctions = @(
    'Build-Project',
    'Edit-ProjectSettings',
    'Get-ServiceStatus',
    'Initialize-Database',
    'Install-Dependencies',
    'Install-Service',
    'Invoke-CustomCommand',
    'Manage-DeploymentConfigurations',
    'Set-Environment',
    'Set-SSHKeys',
    'Setup-CertificateAccess',
    'Start-DatabaseService',
    'Test-SSHConnection',
    'Test-ServerReadiness',
    'Update-ServerConfig'
)
foreach ($func in $requiredFunctions) {
    if (-not (Get-Command -Name $func -ErrorAction SilentlyContinue)) {
        Write-Host "Required function not found after module import: $func" -ForegroundColor Red
    }
}

# Restore preferences after modules are loaded
$ErrorActionPreference = 'Continue'
$ProgressPreference = 'Continue'
$WarningPreference = 'Continue'

# Ensure UI functions are available (these are critical for proper menu operation)
if (-not (Get-Command -Name Enable-UIMode -ErrorAction SilentlyContinue)) {
    function Enable-UIMode {
        [CmdletBinding()]
        param()
        Write-Host "UI Mode enabled" -ForegroundColor Gray
    }
}

if (-not (Get-Command -Name Disable-UIMode -ErrorAction SilentlyContinue)) {
    function Disable-UIMode {
        [CmdletBinding()]
        param()
        Write-Host "UI Mode disabled (emergency fallback)" -ForegroundColor Gray
    }
}

# Direct implementation of configuration-related functions
function Script:Import-Configuration {
    [CmdletBinding()]
    param ([string]$ConfigFile)

    try {
        if (Test-Path -Path $ConfigFile) {
            $configContent = Get-Content -Path $ConfigFile -Raw | ConvertFrom-Json
            $script:Config = $configContent
            $global:Config = $configContent
            Write-Host "Loaded configuration from $ConfigFile" -ForegroundColor Green
            return $true
        } else {
            Write-Host "Configuration file not found: $ConfigFile" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "Error loading configuration: $_" -ForegroundColor Red
        return $false
    }
}

function Script:New-DeploymentConfiguration {
    [CmdletBinding()]
    param ([string]$Environment = "development")

    # Local logging function to ensure it's available
    function script:Write-LocalMessage {
        param([string]$Message, [ConsoleColor]$Color = [ConsoleColor]::White)
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Write-Host "[$timestamp] $Message" -ForegroundColor $Color
    }

    try {
        # Create basic configuration structure using the same format as backup file
        $script:Config = @{
            project = @{
                local_source_dir = "D:\Augment\project-tracker\database-service"
                name = "database-service"
                remote_install_dir = "/opt/database-service"
                description = "database-service for $Environment environment"
                remote_build_dir = "/home/<USER>/database-service-build"
            }
            ssh = @{
                local_key_path = "C:\Users\<USER>\.ssh\id_rsa"
                username = "btaylor-admin"
                port = 22
                host = "git.chcit.org"
            }
            service = @{
                user = "database-service"
                name = "database-service"
                group = "database-service"
                description = "database-service for $Environment environment"
            }
            database = @{
                user = "database_service"
                name = "database_service"
                port = 5432
                password = "password2311"
                host = "localhost"
            }
            version = @{
                number = 2
                updated = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
                created = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
            }
        } | ConvertTo-PSObject

        # Save the configuration
        $configFile = Join-Path -Path $script:ConfigDir -ChildPath "database-service-$($Environment.ToLower()).json"
        $script:Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configFile -Encoding UTF8

        Write-LocalMessage "Created new configuration for environment: $Environment" -Color Green
        return $true
    } catch {
        Write-LocalMessage "Failed to create new configuration: $_" -Color Red
        return $false
    }
}

function Script:Update-ConfigurationPaths {
    # Updates paths in the configuration based on active environment
    if ($null -eq $script:Config) {
        Write-Host "No configuration loaded, cannot update paths" -ForegroundColor Yellow
        return $false
    }

    # Set environment-specific paths here if needed
    return $true
}

function Script:Get-LogFilePath {
    # Return the current log file path, or create one if it doesn't exist
    $logDir = Join-Path -Path $PSScriptRoot -ChildPath "logs"
    if (-not (Test-Path -Path $logDir)) {
        New-Item -Path $logDir -ItemType Directory -Force | Out-Null
    }

    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $logFile = Join-Path -Path $logDir -ChildPath "deployment_$timestamp.log"

    return $logFile
}

function Script:ConvertTo-PSObject {
    param ([Parameter(ValueFromPipeline=$true)]$InputObject)

    process {
        if ($InputObject -is [System.Collections.IDictionary]) {
            $obj = New-Object PSObject
            foreach ($key in $InputObject.Keys) {
                $value = $InputObject[$key]
                if ($value -is [System.Collections.IDictionary]) {
                    $value = $value | ConvertTo-PSObject
                } elseif ($value -is [System.Collections.IEnumerable] -and $value -isnot [string]) {
                    $value = @($value | ForEach-Object { $_ | ConvertTo-PSObject })
                }
                $obj | Add-Member -MemberType NoteProperty -Name $key -Value $value
            }
            return $obj
        } else {
            return $InputObject
        }
    }
}

# Script variables
$script:ConfigDir = Join-Path -Path $PSScriptRoot -ChildPath "config"
$script:Environment = "development" # Default environment

function Show-ProgressBar {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Activity,
        [Parameter(Mandatory=$false)]
        [int]$PercentComplete = 0
    )
    Write-Progress -Activity $Activity -PercentComplete $PercentComplete
    Write-Log -Message ("$($Activity): $($PercentComplete)% complete") -Component "Menu"
}

function Update-UIProgress {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Activity,

        [Parameter(Mandatory=$true)]
        [int]$PercentComplete
    )
    Write-Progress -Activity $Activity -PercentComplete $PercentComplete
    Write-Host "${Activity}: ${PercentComplete}% complete" -ForegroundColor Cyan
}

<#
.SYNOPSIS
    Initializes the deployment script.

.DESCRIPTION
    Sets up the environment, loads configuration, and displays the main menu.

.EXAMPLE
    Start-Script
#>
function Start-Script {
    [CmdletBinding()]
    param()

    # Local logging function to ensure it's available
    function script:Write-StartupLog {
        param([string]$Message, [ConsoleColor]$Color = [ConsoleColor]::White)
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Write-Host "[$timestamp] $Message" -ForegroundColor $Color
    }

    Write-StartupLog "Starting database service deployment script" -Color Cyan

    # Create config directory if it doesn't exist
    if (-not (Test-Path -Path $script:ConfigDir)) {
        try {
            New-Item -Path $script:ConfigDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
            Write-StartupLog ("Created configuration directory: $($script:ConfigDir)") -Color Green
        } catch {
            Write-StartupLog ("Failed to create configuration directory: $($_)") -Color Red
            exit 1
        }
    }

    # Load or create configuration
    $configFile = Join-Path -Path $script:ConfigDir -ChildPath "database-service-$($script:Environment.ToLower()).json"

    if (Test-Path -Path $configFile) {
        try {
            $success = Import-Configuration -ConfigFile $configFile
            if (-not $success) {
                Write-StartupLog ("Failed to load configuration from $($configFile)") -Color Red

                # Try to create a new configuration
                Write-StartupLog "Attempting to create a new configuration..." -Color Yellow
                New-DeploymentConfiguration -Environment $script:Environment
            } else {
                Write-StartupLog ("Loaded configuration from $($configFile)") -Color Green
            }
        } catch {
            Write-StartupLog ("Exception loading configuration: $($_.Exception.Message)") -Color Red

            # Try to create a new configuration
            Write-StartupLog "Attempting to create a new configuration..." -Color Yellow
            New-DeploymentConfiguration -Environment $script:Environment
        }
    } else {
        Write-StartupLog ("No configuration found for environment: $($script:Environment)") -Color Yellow
        Write-StartupLog "Creating a new configuration..." -Color Cyan

        # Create a new configuration
        try {
            New-DeploymentConfiguration -Environment $script:Environment
        } catch {
            Write-StartupLog ("Failed to create new configuration: $($_.Exception.Message)") -Color Red
            exit 1
        }
    }

    # Update configuration paths if needed
    Update-ConfigurationPaths

    # Modules have already been loaded at the beginning of the script
    # No need to reload them again

    # Pause before showing the main menu so user can see any errors or output
    Write-Host "`nPress Enter to continue to the main menu..." -ForegroundColor Yellow
    Read-Host | Out-Null
    Show-MainMenu
}

# Exit script
function Exit-Script {
    Write-Host "Exiting deployment script..." -ForegroundColor Yellow
    exit 0
}

# Show backup management menu
function Show-BackupManagementMenu {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "           Configuration Backup Management             " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    Write-Host "[1] View All Backups" -ForegroundColor White
    Write-Host "[2] View Backups by Project" -ForegroundColor White
    Write-Host "[3] View Backups by Environment" -ForegroundColor White
    Write-Host "[4] Restore a Backup" -ForegroundColor White
    Write-Host "[5] Delete a Backup" -ForegroundColor White
    Write-Host "[6] Delete a Configuration" -ForegroundColor White
    Write-Host "---------------------------------------" -ForegroundColor Gray
    Write-Host "[0] Back to Main Menu" -ForegroundColor White

    Write-Host
    $choice = Read-Host "Select an option (0-6)"

    switch ($choice) {
        "1" {
            Show-ConfigurationBackups
            Wait-ForUser
            Show-BackupManagementMenu
        }
        "2" {
            $project = Read-Host "Enter project name filter (or press Enter for all)"
            Show-ConfigurationBackups -ProjectFilter $project
            Wait-ForUser
            Show-BackupManagementMenu
        }
        "3" {
            $env = Read-Host "Enter environment filter (or press Enter for all)"
            Show-ConfigurationBackups -EnvironmentFilter $env
            Wait-ForUser
            Show-BackupManagementMenu
        }
        "4" {
            Restore-ConfigurationBackup
            Wait-ForUser
            Show-BackupManagementMenu
        }
        "5" {
            Remove-ConfigurationBackup
            Wait-ForUser
            Show-BackupManagementMenu
        }
        "6" {
            Remove-Configuration
            Wait-ForUser
            Show-BackupManagementMenu
        }
        "0" { Show-MainMenu }
        default {
            Write-Host "Invalid option. Please try again." -ForegroundColor Red
            Start-Sleep -Seconds 1
            Show-BackupManagementMenu
        }
    }
}

function Show-MainMenu {
    while ($true) {
        Clear-Host
        Write-Host "`n=== Database Service Deployment Main Menu ===`n" -ForegroundColor Cyan
        Write-Host "[1] Set Environment"
        Write-Host "[2] Manage Deployment Configurations"
        Write-Host "[3] Edit Project Settings"
        Write-Host "[4] Set SSH Keys"
        Write-Host "[5] Setup Certificate Access"
        Write-Host "[6] Test SSH Connection"
        Write-Host "[7] Test Server Readiness"
        Write-Host "[8] Install Dependencies"
        Write-Host "[9] Build Project"
        Write-Host "[10] Install Service"
        Write-Host "[11] Initialize Database"
        Write-Host "[12] Start Database Service"
        Write-Host "[13] Get Service Status"
        Write-Host "[14] Update Server Config"
        Write-Host "[15] Manage Configuration Backups"
        Write-Host "[16] Invoke Custom Command"
        Write-Host "[17] Build UI"
        Write-Host "[18] Deploy UI"
        Write-Host "[19] Manage Database Schemas"
        Write-Host "[L] View Log File"
        Write-Host "[Q] Quit"
        $option = Read-Host "Select an option"
        try {
            switch ($option) {
                '1' {
                    Write-Host "[Menu] Set-Environment selected. Current Environment: $($script:Environment)" -ForegroundColor Cyan
                    Set-Environment
                    Write-Host "[Menu] Set-Environment completed." -ForegroundColor Green
                }
                '2' {
                    Write-Host "[Menu] Show-DeploymentConfigurationsUI selected." -ForegroundColor Cyan
                    if (Get-Command -Name Show-DeploymentConfigurationsUI -ErrorAction SilentlyContinue) {
                        Show-DeploymentConfigurationsUI
                    } elseif (Get-Command -Name Show-DeploymentConfigurations -ErrorAction SilentlyContinue) {
                        Show-DeploymentConfigurations
                    } else {
                        Write-Host "Configuration management module not found." -ForegroundColor Red
                        Start-Sleep -Seconds 2
                    }
                    Write-Host "[Menu] Show-DeploymentConfigurationsUI completed." -ForegroundColor Green
                }
                '3' {
                    Write-Host "[Menu] Edit-ProjectSettings selected." -ForegroundColor Cyan
                    Edit-ProjectSettings
                    Write-Host "[Menu] Edit-ProjectSettings completed." -ForegroundColor Green
                }
                '4' {
                    Write-Host "[Menu] Set-SSHKeys selected." -ForegroundColor Cyan
                    Set-SSHKeys
                    Write-Host "[Menu] Set-SSHKeys completed." -ForegroundColor Green
                }
                '5' {
                    Write-Host "[Menu] Setup-CertificateAccess selected." -ForegroundColor Cyan
                    Setup-CertificateAccess
                    Write-Host "[Menu] Setup-CertificateAccess completed." -ForegroundColor Green
                }
                '6' {
                    Write-Host "[Menu] Test-SSHConnection selected." -ForegroundColor Cyan
                    Test-SSHConnectionUI
                    Write-Host "[Menu] Test-SSHConnection completed." -ForegroundColor Green
                }
                '7' {
                    Write-Host "[Menu] Test-ServerReadiness selected." -ForegroundColor Cyan
                    Test-ServerReadiness
                    Write-Host "[Menu] Test-ServerReadiness completed." -ForegroundColor Green
                }
                '8' {
                    Write-Host "[Menu] Install-Dependencies selected." -ForegroundColor Cyan
                    Install-Dependencies
                    Write-Host "[Menu] Install-Dependencies completed." -ForegroundColor Green
                }
                '9' {
                    Write-Host "[Menu] Build-Project selected." -ForegroundColor Cyan
                    Build-Project
                    Write-Host "[Menu] Build-Project completed." -ForegroundColor Green
                }
                '10' {
                    Write-Host "[Menu] Install-Service selected." -ForegroundColor Cyan
                    try {
                        # Force reload the Install-Service module to ensure fresh import
                        Import-Module "$PSScriptRoot\Modules\Install-Service.psm1" -Force -ErrorAction Stop
                        Install-Service
                        Write-Host "[Menu] Install-Service completed." -ForegroundColor Green
                    } catch {
                        Write-Host "Error in Install-Service: $_" -ForegroundColor Red
                        Write-Host "Error details: $($_.Exception.ToString())" -ForegroundColor Red
                        Write-Host "Press Enter to continue..." -ForegroundColor Yellow
                        Read-Host | Out-Null
                    }
                }
                '11' {
                    Write-Host "[Menu] Initialize-Database selected." -ForegroundColor Cyan
                    Initialize-Database
                    Write-Host "[Menu] Initialize-Database completed." -ForegroundColor Green
                }
                '12' {
                    Write-Host "[Menu] Start-DatabaseService selected." -ForegroundColor Cyan
                    Start-DatabaseService
                    Write-Host "[Menu] Start-DatabaseService completed." -ForegroundColor Green
                }
                '13' {
                    Write-Host "[Menu] Get-ServiceStatus selected." -ForegroundColor Cyan
                    Get-ServiceStatus
                    Write-Host "[Menu] Get-ServiceStatus completed." -ForegroundColor Green
                }
                '14' {
                    Write-Host "[Menu] Update-ServerConfig selected." -ForegroundColor Cyan
                    Update-ServerConfig
                    Write-Host "[Menu] Update-ServerConfig completed." -ForegroundColor Green
                }
                '15' {
                    Write-Host "[Menu] Show-BackupManagementMenu selected." -ForegroundColor Cyan
                    Show-BackupManagementMenu
                    Write-Host "[Menu] Show-BackupManagementMenu completed." -ForegroundColor Green
                }
                '16' {
                    Write-Host "[Menu] Invoke-CustomCommand selected." -ForegroundColor Cyan
                    Invoke-CustomCommand
                    Write-Host "[Menu] Invoke-CustomCommand completed." -ForegroundColor Green
                }
                '17' {
                    Write-Host "[Menu] Build-UI selected." -ForegroundColor Cyan
                    try {
                        Import-Module "$PSScriptRoot\Modules\Build-UI.psm1" -Force # Ensure module is loaded/reloaded
                        # Invoke-BuildUI no longer needs ConfigDirectory
                        # It now returns the path to the build output or $null on failure
                        $buildPath = Invoke-BuildUI 
                        if ($buildPath) {
                            $script:CurrentBuildOutputPath = $buildPath
                            Write-Host "[Menu] Build-UI completed successfully. Output at: $script:CurrentBuildOutputPath" -ForegroundColor Green
                        } else {
                            Write-Host "[Menu] Build-UI failed." -ForegroundColor Red
                            $script:CurrentBuildOutputPath = $null # Ensure it's null on failure
                        }
                    } catch {
                        Write-Host "Error in Build-UI: $_" -ForegroundColor Red
                        $script:CurrentBuildOutputPath = $null # Ensure it's null on error
                    }
                    Write-Host "Press Enter to continue..." -ForegroundColor Yellow
                    Read-Host | Out-Null
                }
                '18' {
                    Write-Host "[Menu] Deploy-UI selected." -ForegroundColor Cyan
                    if (-not $script:CurrentBuildOutputPath) {
                        Write-Host "[Deploy-UI] No build output found. Please run 'Build UI' (Option 17) first." -ForegroundColor Yellow
                    } else {
                        try {
                            Import-Module "$PSScriptRoot\Modules\Deploy-UI.psm1" -Force # Ensure module is loaded/reloaded
                            # Pass the stored build output path to Invoke-DeployUI
                            Invoke-DeployUI -BuildOutputDir $script:CurrentBuildOutputPath
                            Write-Host "[Menu] Deploy-UI completed successfully." -ForegroundColor Green
                        } catch {
                            Write-Host "Error in Deploy-UI: $_" -ForegroundColor Red
                        }
                    }
                    Write-Host "Press Enter to continue..." -ForegroundColor Yellow
                    Read-Host | Out-Null
                }
                '19' {
                    Write-Host "[Menu] Manage Database Schemas selected." -ForegroundColor Cyan
                    if (Get-Command -Name Show-SchemaManagementMenu -ErrorAction SilentlyContinue) {
                        Show-SchemaManagementMenu
                    } else {
                        Write-Host "Schema management module not found." -ForegroundColor Red
                        Start-Sleep -Seconds 2
                    }
                    Write-Host "[Menu] Manage Database Schemas completed." -ForegroundColor Green
                }
                'L' {
                    Write-Host "[Menu] View Log File selected." -ForegroundColor Cyan
                    
                    # Get all possible log file locations
                    $logPaths = @()
                    
                    # Try the main log path from logger
                    $mainLogPath = Get-LogFilePath
                    if ($mainLogPath) { $logPaths += $mainLogPath }
                    
                    # Check deployment_scripts/logs directory
                    $scriptsLogDir = Join-Path -Path $PSScriptRoot -ChildPath "logs"
                    if (Test-Path $scriptsLogDir) {
                        $latestLog = Get-ChildItem -Path $scriptsLogDir -Filter "deployment_*.log" | 
                                     Sort-Object LastWriteTime -Descending | 
                                     Select-Object -First 1 -ExpandProperty FullName
                        if ($latestLog) { $logPaths += $latestLog }
                    }
                    
                    # Check Modules/logs directory
                    $modulesLogDir = Join-Path -Path $PSScriptRoot -ChildPath "Modules\logs"
                    if (Test-Path $modulesLogDir) {
                        $latestModuleLog = Get-ChildItem -Path $modulesLogDir -Filter "deployment_*.log" | 
                                           Sort-Object LastWriteTime -Descending | 
                                           Select-Object -First 1 -ExpandProperty FullName
                        if ($latestModuleLog) { $logPaths += $latestModuleLog }
                    }
                    
                    Write-Host "[DIAG] Found these potential log files:" -ForegroundColor Yellow
                    $logPaths | ForEach-Object { Write-Host "  $_" -ForegroundColor Yellow }
                    
                    # Try each log path until we find one that exists
                    $foundLog = $false
                    foreach ($logPath in $logPaths) {
                        if (Test-Path $logPath) {
                            Write-Host "`nDisplaying log file: $logPath" -ForegroundColor Green
                            Write-Host "`n--- Last 40 lines of log ---`n" -ForegroundColor Yellow
                            Get-Content $logPath -Tail 40 | ForEach-Object { Write-Host $_ }
                            Write-Host "`n--- End of log ---`n" -ForegroundColor Yellow
                            $foundLog = $true
                            break
                        }
                    }
                    
                    if (-not $foundLog) {
                        Write-Host "No log files found in any of the expected locations." -ForegroundColor Red
                    }
                    
                    Read-Host "Press Enter to return to menu"
                }
                'Q' {
                    Write-Host "[Menu] Quit selected." -ForegroundColor Cyan
                    Write-Host "Exiting..." -ForegroundColor Yellow
                    Exit-Script
                }
                default {
                    Write-Host "Invalid option selected. Please choose a valid option." -ForegroundColor Red
                    Start-Sleep -Seconds 2
                }
            }
        } catch {
            Write-Host "Error executing option $option : $_" -ForegroundColor Red
            Write-Host "Press Enter to continue..." -ForegroundColor Yellow
            Read-Host | Out-Null
        }
    }
}

Start-Script
