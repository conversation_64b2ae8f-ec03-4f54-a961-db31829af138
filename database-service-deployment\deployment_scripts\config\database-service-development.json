{"database": {"port": 5432, "host": "localhost", "user": "database_service", "password": "password2311", "name": "database_service"}, "project": {"remote_build_dir": "/home/<USER>/database-service-build", "description": "database-service for development environment", "name": "database-service", "remote_install_dir": "/opt/database-service", "local_source_dir": "D:\\Augment\\project-tracker\\database-service"}, "ssh": {"username": "btaylor-admin", "port": 22, "local_key_path": "C:\\Users\\<USER>\\.ssh\\id_rsa", "host": "git.chcit.org", "key_path": "C:\\Users\\<USER>\\.ssh\\id_rsa"}, "service": {"group": "database-service", "description": "database-service for development environment", "user": "database-service", "name": "database-service"}, "version": {"number": 2, "created": "2025-04-16 20:58:00", "updated": "2025-04-16 20:58:00"}, "dependencies": [{"name": "GCC 14 Compiler", "description": "GCC 14 compiler with C++23 support (required for modules and coroutines)", "command": "apt-get install -y g++-14", "check": "g++-14 --version"}, {"name": "CMake", "description": "CMake build system (minimum version 3.20.0)", "command": "apt-get install -y cmake", "check": "cmake --version"}, {"name": "Build Essential", "description": "Build tools (make, etc.)", "command": "apt-get install -y build-essential", "check": "dpkg -s build-essential"}, {"name": "Boost Libraries", "description": "Boost C++ libraries (system, program_options components required)", "command": "apt-get install -y libboost-all-dev", "check": "dpkg -l | grep libboost"}, {"name": "PostgreSQL Client Libraries", "description": "PostgreSQL client development libraries", "command": "apt-get install -y libpq-dev", "check": "dpkg -s libpq-dev"}, {"name": "pkg-config", "description": "Helper tool for discovering installed libraries", "command": "apt-get install -y pkg-config", "check": "dpkg -s pkg-config"}, {"name": "PostgreSQL C++ API", "description": "C++ client API for PostgreSQL", "command": "apt-get install -y libpqxx-dev", "check": "dpkg -s libpqxx-dev"}, {"name": "OpenSSL Development", "description": "OpenSSL development libraries", "command": "apt-get install -y libssl-dev", "check": "dpkg -s libssl-dev"}, {"name": "JSON Library", "description": "JSON for Modern C++", "command": "apt-get install -y nlohmann-json3-dev", "check": "dpkg -s n<PERSON><PERSON>-json3-dev"}, {"name": "PostgreSQL Server", "description": "PostgreSQL database server", "command": "apt-get install -y postgresql postgresql-contrib", "check": "psql --version", "postInstall": true}, {"name": "Ninja Build System", "description": "High-speed build system required for C++ modules support in CMake", "command": "apt-get install -y ninja-build", "check": "dpkg -s ninja-build"}, {"name": "Git", "description": "Version control system (for source code management)", "command": "apt-get install -y git", "check": "git --version"}, {"name": "<PERSON><PERSON><PERSON>", "description": "Command line tool for transferring data (for API testing)", "command": "apt-get install -y curl", "check": "curl --version"}, {"name": "Net Tools", "description": "Network utilities (for port checking and diagnostics)", "command": "apt-get install -y net-tools", "check": "netstat --version"}]}