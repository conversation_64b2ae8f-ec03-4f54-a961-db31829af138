{"database": {"port": 5432, "host": "localhost", "user": "database_service", "password": "password2311", "name": "database_service"}, "project": {"remote_build_dir": "/home/<USER>/database-service-build", "description": "database-service for development environment", "name": "database-service", "remote_install_dir": "/opt/database-service", "local_source_dir": "D:\\Augment\\project-tracker\\database-service"}, "ssh": {"username": "btaylor-admin", "port": 22, "local_key_path": "C:\\Users\\<USER>\\.ssh\\id_rsa", "host": "git.chcit.org", "key_path": "C:\\Users\\<USER>\\.ssh\\id_rsa"}, "service": {"group": "database-service", "description": "database-service for development environment", "user": "database-service", "name": "database-service"}, "version": {"number": 2, "created": "2025-04-16 20:58:00", "updated": "2025-04-16 20:58:00"}, "dependencies": [{"name": "GCC 14 Compiler", "check": "g++-14 --version", "description": "GCC 14 compiler with C++23 support (required for modules and coroutines)", "command": "apt-get install -y g++-14"}, {"name": "CMake", "check": "cmake --version", "description": "CMake build system (minimum version 3.20.0)", "command": "apt-get install -y cmake"}, {"name": "Build Essential", "check": "dpkg -s build-essential", "description": "Build tools (make, etc.)", "command": "apt-get install -y build-essential"}, {"name": "Boost Libraries", "check": "dpkg -l | grep libboost", "description": "Boost C++ libraries (system, program_options components required)", "command": "apt-get install -y libboost-all-dev"}, {"name": "PostgreSQL Client Libraries", "check": "dpkg -s libpq-dev", "description": "PostgreSQL client development libraries", "command": "apt-get install -y libpq-dev"}, {"name": "pkg-config", "check": "dpkg -s pkg-config", "description": "Helper tool for discovering installed libraries", "command": "apt-get install -y pkg-config"}, {"name": "PostgreSQL C++ API", "check": "dpkg -s libpqxx-dev", "description": "C++ client API for PostgreSQL", "command": "apt-get install -y libpqxx-dev"}, {"name": "OpenSSL Development", "check": "dpkg -s libssl-dev", "description": "OpenSSL development libraries", "command": "apt-get install -y libssl-dev"}, {"name": "JSON Library", "check": "dpkg -s n<PERSON><PERSON>-json3-dev", "description": "JSON for Modern C++", "command": "apt-get install -y nlohmann-json3-dev"}, {"name": "PostgreSQL Server", "check": "psql --version", "description": "PostgreSQL database server", "command": "apt-get install -y postgresql postgresql-contrib"}, {"name": "Ninja Build System", "check": "dpkg -s ninja-build", "description": "High-speed build system required for C++ modules support in CMake", "command": "apt-get install -y ninja-build"}]}