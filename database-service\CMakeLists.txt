cmake_minimum_required(VERSION 3.16)
project(database-service VERSION 1.0.0 LANGUAGES CXX)

# Options
option(BUILD_TESTS "Build tests" ON)

# Require C++23
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Add compiler warnings
add_compile_options(-Wall -Wextra -Wpedantic)

# Add optimization flags for Release builds
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3")

# Find required packages
find_package(Boost REQUIRED COMPONENTS system program_options)
find_package(PostgreSQL REQUIRED)
find_package(OpenSSL REQUIRED)
find_package(Threads REQUIRED)
find_package(PkgConfig REQUIRED)

# Find nlohmann/json using pkg-config
pkg_check_modules(NLOHMANN_JSON REQUIRED nlohmann_json)

# Find pqxx using pkg-config
pkg_check_modules(PQXX REQUIRED libpqxx)

# Source files
file(GLOB_RECURSE SOURCES "src/*.cpp")

# Create executable
add_executable(database-service ${SOURCES})

# Include directories
target_include_directories(database-service PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${Boost_INCLUDE_DIRS}
    ${PostgreSQL_INCLUDE_DIRS}
    ${OPENSSL_INCLUDE_DIR}
    ${NLOHMANN_JSON_INCLUDE_DIRS}
    ${PQXX_INCLUDE_DIRS}
)

# Link libraries
target_link_libraries(database-service PRIVATE
    ${Boost_LIBRARIES}
    ${PostgreSQL_LIBRARIES}
    ${OPENSSL_LIBRARIES}
    ${NLOHMANN_JSON_LIBRARIES}
    ${PQXX_LIBRARIES}
    Threads::Threads
)

# Install targets
install(TARGETS database-service DESTINATION bin)

# Add tests (optional)
if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# Print basic configuration
message(STATUS "Database Service v${PROJECT_VERSION}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")
