cmake_minimum_required(VERSION 3.20)
project(database-service VERSION 1.0.0 LANGUAGES CXX)

# Set CMake policies for modern practices
cmake_policy(SET CMP0167 NEW) # Use modern FindBoost module

# Options
option(BUILD_TESTS "Build tests" ON)
option(ENABLE_COVERAGE "Enable code coverage" OFF)

# Require C++23
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Ensure compiler supports C++23
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU")
    if(CMAKE_CXX_COMPILER_VERSION VERSION_LESS 14.0)
        message(FATAL_ERROR "GCC version must be at least 14.0 for C++23 support")
    endif()
elseif(CMAKE_CXX_COMPILER_ID MATCHES "Clang")
    if(CMAKE_CXX_COMPILER_VERSION VERSION_LESS 16.0)
        message(FATAL_ERROR "Clang version must be at least 16.0 for C++23 support")
    endif()
elseif(CMAKE_CXX_COMPILER_ID MATCHES "MSVC")
    if(CMAKE_CXX_COMPILER_VERSION VERSION_LESS 19.30)
        message(FATAL_ERROR "MSVC version must be at least 19.30 for C++23 support")
    endif()
endif()

# Add compiler warnings
add_compile_options(-Wall -Wextra -Wpedantic)

# Add optimization flags for Release builds
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3")

# Code coverage support
if(ENABLE_COVERAGE AND CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    add_compile_options(--coverage -O0 -g)
    add_link_options(--coverage)
    message(STATUS "Code coverage enabled")
endif()

# Check for C++23 features
include(CheckCXXSourceCompiles)

# Check for std::expected
check_cxx_source_compiles("
#include <expected>
#include <string>
int main() {
    std::expected<int, std::string> result = 42;
    return result.has_value() ? 0 : 1;
}
" HAVE_STD_EXPECTED)

# Check for std::format
check_cxx_source_compiles("
#include <format>
#include <string>
int main() {
    std::string formatted = std::format(\"Hello, {}!\", \"world\");
    return formatted.empty() ? 1 : 0;
}
" HAVE_STD_FORMAT)

# Check for std::print
check_cxx_source_compiles("
#include <print>
int main() {
    std::print(\"Hello, world!\\n\");
    return 0;
}
" HAVE_STD_PRINT)

if(NOT HAVE_STD_EXPECTED)
    message(FATAL_ERROR "std::expected is required but not available. Please use a C++23 compliant compiler.")
endif()

if(NOT HAVE_STD_FORMAT)
    message(WARNING "std::format is not available. Some formatting features may not work.")
endif()

if(NOT HAVE_STD_PRINT)
    message(WARNING "std::print is not available. Will fall back to iostream.")
endif()

# Output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Find required packages
find_package(Boost REQUIRED COMPONENTS system program_options)
find_package(PostgreSQL REQUIRED)
find_package(OpenSSL REQUIRED)
find_package(Threads REQUIRED)

# Find nlohmann/json
find_package(nlohmann_json QUIET)
if(NOT nlohmann_json_FOUND)
    message(STATUS "nlohmann/json package not found, will attempt to use system libraries")
endif()

# Try to find pqxx package
find_package(pqxx QUIET)
if(NOT pqxx_FOUND)
    message(STATUS "pqxx package not found, will attempt to use system libraries")
endif()

# Source files
file(GLOB_RECURSE SOURCES "src/*.cpp")

# Create executable
add_executable(database-service ${SOURCES})

# Modern target-based includes and compile features
# Set include directories for the target
# Boost, PostgreSQL, OpenSSL, and CURL include directories are added to the target
# C++23 standard is enforced via target_compile_features
# (CMAKE_CXX_STANDARD and related checks are still kept for toolchain enforcement)
target_include_directories(database-service PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${Boost_INCLUDE_DIRS}
    ${PostgreSQL_INCLUDE_DIRS}
    ${OPENSSL_INCLUDE_DIR}
)

# Require C++23 features for this target
# This ensures the target is built with C++23 features
# (This is in addition to the global CMAKE_CXX_STANDARD setting)
target_compile_features(database-service PRIVATE cxx_std_23)

# Link libraries
target_link_libraries(database-service
    PRIVATE
        Boost::system
        Boost::program_options
        ${PostgreSQL_LIBRARIES}
        OpenSSL::Crypto
        OpenSSL::SSL
        Threads::Threads
)

# Add nlohmann/json library
if(nlohmann_json_FOUND)
    target_link_libraries(database-service PRIVATE nlohmann_json::nlohmann_json)
else()
    # Try to find system nlohmann/json headers
    find_path(NLOHMANN_JSON_INCLUDE_DIR nlohmann/json.hpp)
    if(NLOHMANN_JSON_INCLUDE_DIR)
        target_include_directories(database-service PRIVATE ${NLOHMANN_JSON_INCLUDE_DIR})
    else()
        message(WARNING "nlohmann/json not found. Please install nlohmann-json-dev package.")
    endif()
endif()

# Add pqxx library
if(pqxx_FOUND)
    target_link_libraries(database-service PRIVATE pqxx::pqxx)
else()
    target_link_libraries(database-service PRIVATE pqxx)
endif()

# Install targets
install(TARGETS database-service
    RUNTIME DESTINATION bin
)

# Install configuration files
install(FILES
    ${CMAKE_CURRENT_SOURCE_DIR}/config/config.json
    ${CMAKE_CURRENT_SOURCE_DIR}/config/config-dev.json
    ${CMAKE_CURRENT_SOURCE_DIR}/config/config-prod.json
    DESTINATION etc/database-service
)

# Create directories
install(DIRECTORY
    DESTINATION var/log/database-service
)

# Install systemd service file
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/systemd/database-service.service.in
    ${CMAKE_CURRENT_BINARY_DIR}/systemd/database-service.service
    @ONLY
)

install(FILES
    ${CMAKE_CURRENT_BINARY_DIR}/systemd/database-service.service
    DESTINATION lib/systemd/system
)

# Install scripts
install(PROGRAMS
    ${CMAKE_CURRENT_SOURCE_DIR}/scripts/integrate-with-git-server.sh
    ${CMAKE_CURRENT_SOURCE_DIR}/scripts/test-integration.sh
    DESTINATION bin
)

# Install nginx configuration
install(FILES
    ${CMAKE_CURRENT_SOURCE_DIR}/nginx/database-service.conf
    DESTINATION share/database-service/nginx
)

# Install documentation
install(FILES
    ${CMAKE_CURRENT_SOURCE_DIR}/README.md
    ${CMAKE_CURRENT_SOURCE_DIR}/QUICKSTART.md
    DESTINATION share/doc/database-service
)

# Add tests (optional)
if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
else()
    message(STATUS "Tests disabled. Use -DBUILD_TESTS=ON to enable.")
endif()

# Print configuration summary
message(STATUS "")
message(STATUS "=== Database Service Configuration Summary ===")
message(STATUS "Version: ${PROJECT_VERSION}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")
message(STATUS "Build tests: ${BUILD_TESTS}")
message(STATUS "Code coverage: ${ENABLE_COVERAGE}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "")
message(STATUS "Dependencies:")
message(STATUS "  Boost: ${Boost_VERSION}")
message(STATUS "  PostgreSQL: ${PostgreSQL_VERSION}")
message(STATUS "  OpenSSL: ${OPENSSL_VERSION}")
if(nlohmann_json_FOUND)
    message(STATUS "  nlohmann/json: Found")
else()
    message(STATUS "  nlohmann/json: System headers")
endif()
if(pqxx_FOUND)
    message(STATUS "  pqxx: Found")
else()
    message(STATUS "  pqxx: System library")
endif()
if(BUILD_TESTS AND GTest_FOUND)
    message(STATUS "  GTest: ${GTest_VERSION}")
endif()
message(STATUS "")
message(STATUS "C++23 Features:")
message(STATUS "  std::expected: ${HAVE_STD_EXPECTED}")
message(STATUS "  std::format: ${HAVE_STD_FORMAT}")
message(STATUS "  std::print: ${HAVE_STD_PRINT}")
message(STATUS "===============================================")
message(STATUS "")
