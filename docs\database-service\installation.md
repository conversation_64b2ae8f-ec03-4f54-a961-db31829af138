
# Installation Guide

This guide provides detailed instructions for building, installing, and deploying the Database Service.

## Building from Source

> **Note:** The project is organized into two main directories:
> - `database-service/`: Main C++23 application source code
> - `database-service-deployment/`: PowerShell deployment scripts and configuration files

### Prerequisites

Before building the Database Service, ensure you have the following prerequisites installed:

- C++23 compatible compiler (traditional headers, not modules)
  - GCC 14.2+ on Linux
  - MSVC 19.36+ on Windows
  - Clang 16+ on macOS
- CMake 3.20+
- Boost 1.74+ (system, thread, program_options)
- PostgreSQL 17+ with development libraries
- OpenSSL 1.1.1+
- nlohmann/json 3.9.0+
- libpqxx (PostgreSQL C++ client library)

### Ubuntu/Debian

```bash
# Install dependencies
sudo apt update
sudo apt install -y build-essential cmake libboost-all-dev libpq-dev libssl-dev libpqxx-dev nlohmann-json3-dev

# Install GCC 14.2 (required for C++23 features)
sudo add-apt-repository -y ppa:ubuntu-toolchain-r/test
sudo apt update
sudo apt install -y gcc-14 g++-14
sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-14 140 --slave /usr/bin/g++ g++ /usr/bin/g++-14

# Install PostgreSQL (if not already installed)
sudo apt install -y postgresql postgresql-contrib

# Clone repository
git clone https://github.com/your-org/database-service.git
cd database-service

# Create build directory
mkdir build
cd build

# Configure with CMake (explicitly specify GCC 14.2)
cmake .. -DCMAKE_CXX_COMPILER=g++-14 -DCMAKE_CXX_STANDARD=23

# Build
make -j$(nproc)

# Install (optional)
sudo make install
```

### Windows Development Environment (For Deployment Only)

The Database Service is a Linux application, but the deployment process involves using PowerShell scripts on a Windows server to deploy and build on the Ubuntu Git server. This section describes the Windows deployment environment.

```powershell
# Clone repository (if not already available)
git clone https://github.com/your-org/database-service.git
cd database-service

# Navigate to deployment scripts
cd database-service-deployment\deployment_scripts

# Run the deployment script
powershell -ExecutionPolicy Bypass -File "deploy-database-service-modular.ps1"

# The script provides a menu with options:
# 7. Validate dependencies and deployment readiness
# 8. Install dependencies on Ubuntu server
# 9. Build/compile the database service
# 10. Install the database service
# 11. Initialize database
# 12. Start database service
```

## Configuration

The Database Service is configured using a JSON configuration file. A template is provided in `config/config.template.json`. Copy this file to `config/config.json` and modify it to suit your needs.

### Configuration Options

#### Application

| Option | Description | Default |
| ------ | ----------- | ------- |
| `name` | Application name | `database-service` |
| `environment` | Environment (production, development, test) | `production` |
| `log_level` | Log level (trace, debug, info, warning, error, critical) | `info` |

#### Database

| Option | Description | Default |
| ------ | ----------- | ------- |
| `host` | PostgreSQL host | `localhost` |
| `port` | PostgreSQL port | `5432` |
| `admin_db` | Central management database name | `database_service` |
| `user` | PostgreSQL username | `postgres` |
| `password` | PostgreSQL password | `postgres` |
| `ssl.enabled` | Enable SSL for PostgreSQL connections | `true` |
| `ssl.cert_path` | Path to SSL certificate | `/etc/letsencrypt/live/git.chcit.org/fullchain.pem` |
| `ssl.key_path` | Path to SSL private key | `/etc/letsencrypt/live/git.chcit.org/privkey.pem` |
| `ssl.ca_path` | Path to SSL CA certificate | `/etc/letsencrypt/live/git.chcit.org/chain.pem` |
| `pool_size` | Connection pool size | `10` |
| `connection_timeout` | Connection timeout in seconds | `30` |

#### Applications

| Option | Description | Default |
| ------ | ----------- | ------- |
| `name` | Application name | - |
| `database` | Database name for this application | - |
| `schemas` | List of schemas for this application | `["public"]` |

#### API

| Option | Description | Default |
| ------ | ----------- | ------- |
| `host` | API server host | `0.0.0.0` |
| `port` | API server port | `8080` |
| `workers` | Number of worker threads | `4` |
| `ssl.enabled` | Enable SSL for API server | `true` |
| `ssl.cert_path` | Path to SSL certificate | `/etc/letsencrypt/live/git.chcit.org/fullchain.pem` |
| `ssl.key_path` | Path to SSL private key | `/etc/letsencrypt/live/git.chcit.org/privkey.pem` |

#### Security

| Option | Description | Default |
| ------ | ----------- | ------- |
| `token_expiration_minutes` | Token expiration time in minutes | `60` |
| `encryption_key` | Encryption key for sensitive data | `your-encryption-key` |

### Environment Variables

Configuration options can also be provided via environment variables. Environment variables take precedence over configuration file values.

| Environment Variable | Configuration Option |
| -------------------- | -------------------- |
| `DB_HOST` | `database.host` |
| `DB_PORT` | `database.port` |
| `DB_ADMIN_DB` | `database.admin_db` |
| `DB_USER` | `database.user` |
| `DB_PASSWORD` | `database.password` |
| `DB_SSL_ENABLED` | `database.ssl.enabled` |
| `SSL_CERT_PATH` | `database.ssl.cert_path` |
| `SSL_KEY_PATH` | `database.ssl.key_path` |
| `SSL_CA_PATH` | `database.ssl.ca_path` |
| `API_HOST` | `api.host` |
| `API_PORT` | `api.port` |
| `LOG_LEVEL` | `application.log_level` |
| `ENVIRONMENT` | `application.environment` |

## Database Setup

The Database Service requires a PostgreSQL database server with multiple databases:

1. **Central Management Database**: Stores application registrations, schema versions, and user permissions
2. **Application-Specific Databases**: Each application has its own dedicated database

> **📋 PostgreSQL Configuration**: For detailed PostgreSQL configuration information, including the dedicated volume setup and migration process, see [PostgreSQL Configuration](./postgresql-configuration.md).

### PostgreSQL Infrastructure

The Database Service uses PostgreSQL 17.5 with a dedicated 98GB volume configuration:

- **Server**: git.chcit.org (***********)
- **Data Directory**: `/pgsql/data/17/main` (dedicated volume)
- **Volume**: 98GB dedicated volume (`/dev/xvdb1`)
- **Migration**: Automatically handled by deployment script Option 8

### Setting Up Databases

You can use the provided script to set up the databases:

```bash
# Set up central management database
./scripts/setup-database.bat server-name database_service

# Set up application-specific databases
./scripts/setup-database.bat server-name git_repo_db
./scripts/setup-database.bat server-name logging_db
```

Or manually:

```sql
-- Create central management database
CREATE DATABASE database_service;

-- Connect to central management database
\c database_service

-- Create schema version tracking table
CREATE TABLE schema_version (
  id SERIAL PRIMARY KEY,
  application_name VARCHAR(100) NOT NULL,
  database_name VARCHAR(100) NOT NULL,
  version VARCHAR(50) NOT NULL,
  description TEXT,
  applied_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create applications table
CREATE TABLE applications (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  database_name VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create application-specific databases
CREATE DATABASE git_repo_db;
CREATE DATABASE logging_db;
```

## Running

### Command Line Options

The Database Service supports the following command line options:

| Option | Description | Default |
| ------ | ----------- | ------- |
| `--help` | Display help message | |
| `--config` | Path to configuration file | `config/config.json` |
| `--port` | API server port | `8080` |
| `--log-level` | Log level | `info` |
| `--log-file` | Path to log file | |

### Running as a Service on Linux

The Database Service is designed to run as a systemd service on Linux. A systemd service file is automatically installed during the installation process, but you can also create it manually.

Create a systemd service file at `/etc/systemd/system/database-service.service`:

```ini
[Unit]
Description=Database Service
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=database-service
Group=database-service
ExecStart=/opt/database-service/bin/database-service --config /opt/database-service/config/database-service.json
Restart=on-failure
WorkingDirectory=/opt/database-service
Environment="LD_LIBRARY_PATH=/opt/database-service/lib"

# Security enhancements
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
```

Enable and start the service:

```bash
sudo systemctl daemon-reload
sudo systemctl enable database-service
sudo systemctl start database-service
```

Check the service status:

```bash
sudo systemctl status database-service
```

View service logs:

```bash
sudo journalctl -u database-service
```

## Deployment

The Database Service includes a comprehensive PowerShell deployment script for deploying from a Windows development environment to a Linux server. The script provides a menu-driven interface for all deployment operations.

### Windows-to-Linux Deployment

```powershell
# Navigate to deployment scripts directory
cd database-service-deployment\deployment_scripts

# Run the main deployment script
powershell -ExecutionPolicy Bypass -File "deploy-database-service-modular.ps1"
```

### Deployment Menu Options

The deployment script provides the following menu options:

1. **Option 7**: Validate dependencies and deployment readiness
   - Tests SSH connectivity to the Ubuntu server
   - Validates all required software dependencies
   - Checks for proper directory structure

2. **Option 8**: Install dependencies and configure PostgreSQL
   - Installs GCC 14.2, CMake, Boost, PostgreSQL, OpenSSL, and other dependencies
   - **PostgreSQL Migration**: Automatically migrates PostgreSQL to dedicated 98GB volume
   - **Data Directory**: Moves from `/var/lib/postgresql/17/main` to `/pgsql/data/17/main`
   - **Volume Setup**: Configures dedicated volume with proper permissions
   - **Service Configuration**: Updates PostgreSQL configuration and restarts service
   - Configures the build environment

3. **Option 9**: Build/compile the database service
   - Copies source code to the Ubuntu server build workspace (`/home/<USER>/database-service-build/`)
   - Configures CMake with C++23 support
   - Compiles the application using GCC 14.2 in the build workspace
   - **Build timeout**: 5 minutes (300 seconds)
   - Creates executable at `/home/<USER>/database-service-build/build/bin/database-service`

4. **Option 10**: Install the database service
   - Copies the compiled executable from build workspace to production directory (`/opt/database-service/`)
   - Sets up systemd service files
   - Configures proper permissions and ownership

5. **Option 11**: Initialize database
   - Creates required PostgreSQL databases
   - Sets up database schemas and tables
   - Configures database users and permissions

6. **Option 12**: Start database service
   - Starts the systemd service
   - Enables automatic startup on boot
   - Validates service status

### Deployment Process Flow

The typical deployment process follows these steps:

1. **Validation** (Option 7): Ensure server readiness
2. **Dependencies** (Option 8): Install required software
3. **Build** (Option 9): Compile the application
4. **Install** (Option 10): Deploy the service
5. **Database** (Option 11): Initialize database
6. **Start** (Option 12): Start the service

### Build Success Verification

After a successful build (Option 9), the compiled executable will be located in the build workspace:
```
/home/<USER>/database-service-build/build/bin/database-service
```

After installation (Option 10), the executable will be in the production directory:
```
/opt/database-service/bin/database-service
```

The deployment script will report "BUILD SUCCESS - No errors detected in build log" upon successful compilation.

### Manual Linux Deployment

If you prefer to deploy manually on Linux, follow these steps:

1. Ensure GCC 14.2 is installed on the Linux server
   ```bash
   gcc --version
   # Should show GCC 14.2 or later
   ```

2. Install required dependencies
   ```bash
   sudo apt update
   sudo apt install -y build-essential cmake libboost-all-dev libpq-dev libssl-dev libpqxx-dev nlohmann-json3-dev
   ```

3. Clone the repository on the Linux server
   ```bash
   git clone https://github.com/your-org/database-service.git
   cd database-service
   ```

4. Build the Database Service
   ```bash
   mkdir build
   cd build
   cmake .. -DCMAKE_CXX_COMPILER=g++-14 -DCMAKE_CXX_STANDARD=23
   make -j$(nproc)
   ```

5. Verify the build
   ```bash
   ls -la bin/database-service
   # Should show the compiled executable in the build directory
   ```

6. Install the service to production directory
   ```bash
   sudo make install
   # This copies the executable to /opt/database-service/bin/database-service
   ```

7. Set up the required databases
   ```bash
   sudo -u postgres psql -c "CREATE DATABASE database_service;"
   sudo -u postgres psql -c "CREATE DATABASE git_repo_db;"
   sudo -u postgres psql -c "CREATE DATABASE logging_db;"
   ```

8. Configure the service
   ```bash
   sudo cp config/config-prod.json /opt/database-service/config/config.json
   sudo nano /opt/database-service/config/config.json
   ```

9. Start the service
   ```bash
   sudo systemctl enable database-service
   sudo systemctl start database-service
   sudo systemctl status database-service
   ```

## Multi-Database Architecture

The Database Service uses a multi-database architecture for clear separation between applications:

```
PostgreSQL Server
 database_service (database)
    public (schema)
        schema_version, applications, users, permissions (tables)

 git_repo_db (database)
    public (schema)
       repositories, commits, branches (tables)
    metadata (schema)
        repository_stats, user_activity (tables)

 logging_db (database)
     public (schema)
        logs, log_levels (tables)
     archive (schema)
        historical_logs (tables)
     stats (schema)
         log_statistics, error_trends (tables)
```

This architecture provides:

1. **Complete Isolation**: Each application has its own database
2. **Clear Ownership**: No confusion about which application owns which data
3. **Independent Management**: Each database can be backed up, restored, or migrated independently
4. **Security Boundaries**: Permissions can be set at the database level
5. **Performance Tuning**: Database settings can be optimized for each application's needs

## C++23 Features

The Database Service uses C++23 features supported by GCC 14.2:

### Modern C++ Features

The codebase leverages several modern C++23 features:

```cpp
// Using std::format for string formatting (C++20)
std::string message = std::format("Connected to database {} on {}:{}",
                                 dbName, host, port);

// Using std::expected for error handling (C++23)
std::expected<QueryResult, DatabaseError> executeQuery(const std::string& query) {
    try {
        // Execute query
        auto result = connection_->executeQuery(query);
        return QueryResult{result};
    } catch (const std::exception& e) {
        return std::unexpected(DatabaseError{e.what()});
    }
}

// Using std::filesystem for file operations (C++17)
if (!std::filesystem::exists(configPath)) {
    return std::unexpected(ConfigError{"Configuration file not found"});
}

// Using structured bindings (C++17)
for (const auto& [key, value] : configMap) {
    std::cout << key << ": " << value << std::endl;
}
```

### Multi-threaded Architecture

Instead of coroutines, the Database Service uses a multi-threaded architecture for asynchronous operations:

```cpp
// Asynchronous database query with callbacks
void executeQueryAsync(const std::string& query,
                      std::function<void(Result<QueryResult>)> callback) {
    // Execute query in a separate thread
    std::thread([this, query, callback]() {
        try {
            auto connection = connectionManager_->getConnection();
            auto result = connection->executeQuery(query);
            connectionManager_->returnConnection(connection);
            callback(Result<QueryResult>{result});
        } catch (const std::exception& e) {
            callback(Result<QueryResult>{DatabaseError{e.what()}});
        }
    }).detach();
}
```

### Compiler Requirements

To build the Database Service with C++23 features support, you need:

- **GCC 14.2+**: Use `-std=c++23` flag
- **Clang 16+**: Use `-std=c++23` flag
- **MSVC 19.36+**: Use `/std:c++latest` flag

The CMakeLists.txt file automatically configures the appropriate compiler flags based on the detected compiler.

## Troubleshooting

### Common Issues

#### Connection Refused

If you see "Connection refused" errors, check the following:

- PostgreSQL is running and accessible
- Firewall allows connections to PostgreSQL port
- PostgreSQL is configured to accept remote connections
- Database credentials are correct

#### SSL Certificate Issues

If you see SSL certificate errors, check the following:

- SSL certificates exist and are readable
- SSL certificates are valid and not expired
- SSL certificates are for the correct domain
- SSL private key permissions are correct

#### Permission Denied

If you see "Permission denied" errors, check the following:

- Service user has permission to read configuration files
- Service user has permission to read SSL certificates
- Service user has permission to connect to PostgreSQL
- Service user has permission to write to log files

#### Compiler Errors

If you see compiler errors related to C++23 features, check the following:

- You're using a C++23 compatible compiler (GCC 14.2+, Clang 16+, MSVC 19.36+)
- The compiler is configured with the appropriate C++23 flags
- The CMake configuration is using the correct compiler and flags
- All required libraries (Boost, PostgreSQL, OpenSSL, nlohmann/json, libpqxx) are installed and properly detected by CMake

### SSH Timeout Issues

If builds are failing with SSH timeout errors during deployment:

- The default SSH timeout has been increased to 5 minutes (300 seconds)
- For longer builds, you can modify the timeout in `Build-Project.psm1`:
  ```powershell
  # Line 52: Default timeout
  [int]$Timeout = 300 # Increase if needed

  # Line 333: Build command timeout
  $result = Invoke-RemoteCommand -Server $Server -Command $buildCommand -Timeout 300
  ```

### C++ Const-Correctness Issues

If you encounter mutex-related compilation errors:

- Ensure mutexes used in const member functions are declared as `mutable`
- Example fix applied to `thread_pool.hpp`:
  ```cpp
  // Correct declaration
  mutable std::mutex queueMutex_;
  ```

### Recent Build Fixes (2025-06-18)

The following issues have been resolved in the current codebase:

1. **SSH Timeout**: Extended from 60 seconds to 300 seconds (5 minutes)
2. **Thread Pool Mutex**: Made `queueMutex_` mutable for const member function access
3. **Build Verification**: Added proper build success detection and logging

### Successful Build Indicators

A successful build will show:
- `[100%] Built target database-service`
- `[100%] Linking CXX executable bin/database-service`
- `BUILD SUCCESS - No errors detected in build log`
- Executable created in build workspace: `/home/<USER>/database-service-build/build/bin/database-service`

After installation, the executable will be available in production:
- Production executable: `/opt/database-service/bin/database-service`
- Systemd service: `/etc/systemd/system/database-service.service`

### Logs

Check the service logs for more information:

```bash
# View all logs for the service
sudo journalctl -u database-service

# View only the most recent logs
sudo journalctl -u database-service -n 100

# Follow logs in real-time
sudo journalctl -u database-service -f

# View logs for a specific time period
sudo journalctl -u database-service --since "2023-04-26 10:00:00" --until "2023-04-26 11:00:00"
```

If you specified a log file in the configuration, check that file for detailed logs:

```bash
# View the log file
sudo cat /var/log/database-service/database-service.log

# Follow the log file in real-time
sudo tail -f /var/log/database-service/database-service.log
```




