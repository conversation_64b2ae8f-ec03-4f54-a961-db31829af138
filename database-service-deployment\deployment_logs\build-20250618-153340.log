Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  5%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:22:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   22 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[ 16%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 22%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
[ 27%] Building CXX object CMakeFiles/database-service.dir/src/core/transaction.cpp.o
[ 33%] Building CXX object CMakeFiles/database-service.dir/src/database_service.cpp.o
[ 38%] Building CXX object CMakeFiles/database-service.dir/src/main.cpp.o
[ 44%] Building CXX object CMakeFiles/database-service.dir/src/metrics/database_metrics.cpp.o
[ 50%] Building CXX object CMakeFiles/database-service.dir/src/metrics/metrics_collector.cpp.o
In file included from /usr/include/c++/14/ext/alloc_traits.h:34,
                 from /usr/include/c++/14/bits/basic_string.h:39,
                 from /usr/include/c++/14/string:54,
                 from /home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:2,
                 from /home/<USER>/database-service-build/src/metrics/database_metrics.cpp:1:
/usr/include/c++/14/bits/alloc_traits.h: In instantiation of ‘static constexpr void std::allocator_traits<std::allocator<_CharT> >::construct(allocator_type&, _Up*, _Args&& ...) [with _Up = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Tp = std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true>; allocator_type = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >]’:
/usr/include/c++/14/bits/hashtable_policy.h:2024:36:   required from ‘std::__detail::_Hashtable_alloc<_NodeAlloc>::__node_type* std::__detail::_Hashtable_alloc<_NodeAlloc>::_M_allocate_node(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _NodeAlloc = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >; __node_ptr = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >::value_type*]’
 2024 |             __node_alloc_traits::construct(__alloc, __n->_M_valptr(),
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~
 2025 |                                            std::forward<_Args>(__args)...);
      |                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable.h:312:35:   required from ‘std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::_Scoped_node::_Scoped_node(std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__hashtable_alloc*, _Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__hashtable_alloc = std::_Hashtable<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::__hashtable_alloc]’
  312 |             _M_node(__h->_M_allocate_node(std::forward<_Args>(__args)...))
      |                     ~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable.h:2143:15:   required from ‘std::pair<typename std::__detail::_Insert<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::iterator, bool> std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::_M_emplace(std::true_type, _Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; typename std::__detail::_Insert<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::iterator = std::__detail::_Insert_base<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::iterator; typename _Traits::__constant_iterators = std::__detail::_Hashtable_traits<true, false, true>::__constant_iterators; std::true_type = std::true_type]’
 2143 |         _Scoped_node __node { this, std::forward<_Args>(__args)...  };
      |                      ^~~~~~
/usr/include/c++/14/bits/hashtable.h:1001:21:   required from ‘std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__ireturn_type std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::emplace(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; __ireturn_type = std::_Hashtable<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::__ireturn_type]’
 1001 |         { return _M_emplace(__unique_keys{}, std::forward<_Args>(__args)...); }
      |                  ~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/unordered_map.h:396:23:   required from ‘std::pair<typename std::_Hashtable<_Key, std::pair<const _Key, _Val>, _Alloc, std::__detail::_Select1st, _Pred, _Hash, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<std::__not_<std::__and_<std::__is_fast_hash<_Hash>, std::__is_nothrow_invocable<const _Hash&, const _Key&> > >::value, false, true> >::iterator, bool> std::unordered_map<_Key, _Tp, _Hash, _Pred, _Alloc>::emplace(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Tp = dbservice::metrics::DatabaseMetrics::QueryMetrics; _Hash = std::hash<std::__cxx11::basic_string<char> >; _Pred = std::equal_to<std::__cxx11::basic_string<char> >; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; typename std::_Hashtable<_Key, std::pair<const _Key, _Val>, _Alloc, std::__detail::_Select1st, _Pred, _Hash, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<std::__not_<std::__and_<std::__is_fast_hash<_Hash>, std::__is_nothrow_invocable<const _Hash&, const _Key&> > >::value, false, true> >::iterator = std::__detail::_Insert_base<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::iterator]’
  396 |         { return _M_h.emplace(std::forward<_Args>(__args)...); }
      |                  ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/metrics/database_metrics.cpp:44:30:   required from here
   44 |         queryMetrics_.emplace(queryType, QueryMetrics{});
      |         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/alloc_traits.h:536:28: error: no matching function for call to ‘construct_at(std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>*&, const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics)’
  536 |           std::construct_at(__p, std::forward<_Args>(__args)...);
      |           ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
In file included from /usr/include/c++/14/bits/char_traits.h:57,
                 from /usr/include/c++/14/string:42:
/usr/include/c++/14/bits/stl_construct.h:94:5: note: candidate: ‘template<class _Tp, class ... _Args> constexpr decltype (::new(void*(0)) _Tp) std::construct_at(_Tp*, _Args&& ...)’
   94 |     construct_at(_Tp* __location, _Args&&... __args)
      |     ^~~~~~~~~~~~
/usr/include/c++/14/bits/stl_construct.h:94:5: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/stl_construct.h: In substitution of ‘template<class _Tp, class ... _Args> constexpr decltype (::new(void*(0)) _Tp) std::construct_at(_Tp*, _Args&& ...) [with _Tp = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}]’:
/usr/include/c++/14/bits/alloc_traits.h:536:21:   required from ‘static constexpr void std::allocator_traits<std::allocator<_CharT> >::construct(allocator_type&, _Up*, _Args&& ...) [with _Up = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Tp = std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true>; allocator_type = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >]’
  536 |           std::construct_at(__p, std::forward<_Args>(__args)...);
      |           ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable_policy.h:2024:36:   required from ‘std::__detail::_Hashtable_alloc<_NodeAlloc>::__node_type* std::__detail::_Hashtable_alloc<_NodeAlloc>::_M_allocate_node(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _NodeAlloc = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >; __node_ptr = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >::value_type*]’
 2024 |             __node_alloc_traits::construct(__alloc, __n->_M_valptr(),
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~
 2025 |                                            std::forward<_Args>(__args)...);
      |                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable.h:312:35:   required from ‘std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::_Scoped_node::_Scoped_node(std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__hashtable_alloc*, _Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__hashtable_alloc = std::_Hashtable<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::__hashtable_alloc]’
  312 |             _M_node(__h->_M_allocate_node(std::forward<_Args>(__args)...))
      |                     ~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable.h:2143:15:   required from ‘std::pair<typename std::__detail::_Insert<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::iterator, bool> std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::_M_emplace(std::true_type, _Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; typename std::__detail::_Insert<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::iterator = std::__detail::_Insert_base<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::iterator; typename _Traits::__constant_iterators = std::__detail::_Hashtable_traits<true, false, true>::__constant_iterators; std::true_type = std::true_type]’
 2143 |         _Scoped_node __node { this, std::forward<_Args>(__args)...  };
      |                      ^~~~~~
/usr/include/c++/14/bits/hashtable.h:1001:21:   required from ‘std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__ireturn_type std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::emplace(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; __ireturn_type = std::_Hashtable<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::__ireturn_type]’
 1001 |         { return _M_emplace(__unique_keys{}, std::forward<_Args>(__args)...); }
      |                  ~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/unordered_map.h:396:23:   required from ‘std::pair<typename std::_Hashtable<_Key, std::pair<const _Key, _Val>, _Alloc, std::__detail::_Select1st, _Pred, _Hash, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<std::__not_<std::__and_<std::__is_fast_hash<_Hash>, std::__is_nothrow_invocable<const _Hash&, const _Key&> > >::value, false, true> >::iterator, bool> std::unordered_map<_Key, _Tp, _Hash, _Pred, _Alloc>::emplace(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Tp = dbservice::metrics::DatabaseMetrics::QueryMetrics; _Hash = std::hash<std::__cxx11::basic_string<char> >; _Pred = std::equal_to<std::__cxx11::basic_string<char> >; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; typename std::_Hashtable<_Key, std::pair<const _Key, _Val>, _Alloc, std::__detail::_Select1st, _Pred, _Hash, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<std::__not_<std::__and_<std::__is_fast_hash<_Hash>, std::__is_nothrow_invocable<const _Hash&, const _Key&> > >::value, false, true> >::iterator = std::__detail::_Insert_base<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::iterator]’
  396 |         { return _M_h.emplace(std::forward<_Args>(__args)...); }
      |                  ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/metrics/database_metrics.cpp:44:30:   required from here
   44 |         queryMetrics_.emplace(queryType, QueryMetrics{});
      |         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/stl_construct.h:96:17: error: no matching function for call to ‘std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>::pair(const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics)’
   96 |     -> decltype(::new((void*)0) _Tp(std::declval<_Args>()...))
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
In file included from /usr/include/c++/14/bits/stl_algobase.h:64,
                 from /usr/include/c++/14/string:51:
/usr/include/c++/14/bits/stl_pair.h:534:9: note: candidate: ‘template<class _UPair>  requires (__eligible_pair_like<_UPair, std::pair<_T1, _T2> >) && (_S_constructible_from_pair_like<_UPair>() && _S_dangles_from_pair_like<_UPair>()) constexpr std::pair<_T1, _T2>::pair(_UPair&&) [with _T1 = const std::__cxx11::basic_string<char>; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’ (deleted)
  534 |         pair(_UPair&&) = delete;
      |         ^~~~
/usr/include/c++/14/bits/stl_pair.h:534:9: note:   candidate expects 1 argument, 2 provided
/usr/include/c++/14/bits/stl_pair.h:525:9: note: candidate: ‘template<class _UPair>  requires (__eligible_pair_like<_UPair, std::pair<_T1, _T2> >) && (_S_constructible_from_pair_like<_UPair>() && !_S_dangles_from_pair_like<_UPair>()) constexpr std::pair<_T1, _T2>::pair(_UPair&&) [with _T1 = const std::__cxx11::basic_string<char>; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’
  525 |         pair(_UPair&& __p)
      |         ^~~~
/usr/include/c++/14/bits/stl_pair.h:525:9: note:   candidate expects 1 argument, 2 provided
/usr/include/c++/14/bits/stl_pair.h:517:9: note: candidate: ‘template<class _U1, class _U2>  requires  _S_constructible<const _U1, const _U2>() && _S_dangles<const _U1, const _U2>() constexpr std::pair<_T1, _T2>::pair(const std::pair<_U1, _U2>&&) [with _U2 = _U1; _T1 = const std::__cxx11::basic_string<char>; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’ (deleted)
  517 |         pair(const pair<_U1, _U2>&&) = delete;
      |         ^~~~
/usr/include/c++/14/bits/stl_pair.h:517:9: note:   candidate expects 1 argument, 2 provided
/usr/include/c++/14/bits/stl_pair.h:507:9: note: candidate: ‘template<class _U1, class _U2>  requires  _S_constructible<const _U1, const _U2>() && !_S_dangles<const _U1, const _U2>() constexpr std::pair<_T1, _T2>::pair(const std::pair<_U1, _U2>&&) [with _U2 = _U1; _T1 = const std::__cxx11::basic_string<char>; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’
  507 |         pair(const pair<_U1, _U2>&& __p)
      |         ^~~~
/usr/include/c++/14/bits/stl_pair.h:507:9: note:   candidate expects 1 argument, 2 provided
/usr/include/c++/14/bits/stl_pair.h:500:9: note: candidate: ‘template<class _U1, class _U2>  requires  _S_constructible<_U1&, _U2&>() && _S_dangles<_U1&, _U2&>() constexpr std::pair<_T1, _T2>::pair(std::pair<_U1, _U2>&) [with _U2 = _U1; _T1 = const std::__cxx11::basic_string<char>; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’ (deleted)
  500 |         pair(pair<_U1, _U2>&) = delete;
      |         ^~~~
/usr/include/c++/14/bits/stl_pair.h:500:9: note:   candidate expects 1 argument, 2 provided
/usr/include/c++/14/bits/stl_pair.h:492:9: note: candidate: ‘template<class _U1, class _U2>  requires  _S_constructible<_U1&, _U2&>() && !_S_dangles<_U1&, _U2&>() constexpr std::pair<_T1, _T2>::pair(std::pair<_U1, _U2>&) [with _U2 = _U1; _T1 = const std::__cxx11::basic_string<char>; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’
  492 |         pair(pair<_U1, _U2>& __p)
      |         ^~~~
/usr/include/c++/14/bits/stl_pair.h:492:9: note:   candidate expects 1 argument, 2 provided
/usr/include/c++/14/bits/stl_pair.h:485:9: note: candidate: ‘template<class _U1, class _U2>  requires  _S_constructible<_U1, _U2>() && _S_dangles<_U1, _U2>() constexpr std::pair<_T1, _T2>::pair(std::pair<_U1, _U2>&&) [with _U2 = _U1; _T1 = const std::__cxx11::basic_string<char>; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’ (deleted)
  485 |         pair(pair<_U1, _U2>&&) = delete;
      |         ^~~~
/usr/include/c++/14/bits/stl_pair.h:485:9: note:   candidate expects 1 argument, 2 provided
/usr/include/c++/14/bits/stl_pair.h:476:9: note: candidate: ‘template<class _U1, class _U2>  requires  _S_constructible<_U1, _U2>() && !_S_dangles<_U1, _U2>() constexpr std::pair<_T1, _T2>::pair(std::pair<_U1, _U2>&&) [with _U2 = _U1; _T1 = const std::__cxx11::basic_string<char>; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’
  476 |         pair(pair<_U1, _U2>&& __p)
      |         ^~~~
/usr/include/c++/14/bits/stl_pair.h:476:9: note:   candidate expects 1 argument, 2 provided
/usr/include/c++/14/bits/stl_pair.h:470:9: note: candidate: ‘template<class _U1, class _U2>  requires  _S_constructible<const _U1&, const _U2&>() && _S_dangles<const _U1&, const _U2&>() constexpr std::pair<_T1, _T2>::pair(const std::pair<_U1, _U2>&) [with _U2 = _U1; _T1 = const std::__cxx11::basic_string<char>; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’ (deleted)
  470 |         pair(const pair<_U1, _U2>&) = delete;
      |         ^~~~
/usr/include/c++/14/bits/stl_pair.h:470:9: note:   candidate expects 1 argument, 2 provided
/usr/include/c++/14/bits/stl_pair.h:461:9: note: candidate: ‘template<class _U1, class _U2>  requires  _S_constructible<const _U1&, const _U2&>() && !_S_dangles<_U1, _U2>() constexpr std::pair<_T1, _T2>::pair(const std::pair<_U1, _U2>&) [with _U2 = _U1; _T1 = const std::__cxx11::basic_string<char>; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’
  461 |         pair(const pair<_U1, _U2>& __p)
      |         ^~~~
/usr/include/c++/14/bits/stl_pair.h:461:9: note:   candidate expects 1 argument, 2 provided
/usr/include/c++/14/bits/stl_pair.h:454:9: note: candidate: ‘template<class _U1, class _U2>  requires  _S_constructible<_U1, _U2>() && _S_dangles<_U1, _U2>() constexpr std::pair<_T1, _T2>::pair(_U1&&, _U2&&) [with _U2 = _U1; _T1 = const std::__cxx11::basic_string<char>; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’ (deleted)
  454 |         pair(_U1&&, _U2&&) = delete;
      |         ^~~~
/usr/include/c++/14/bits/stl_pair.h:454:9: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/stl_pair.h:454:9: note: constraints not satisfied
/usr/include/c++/14/bits/stl_pair.h: In substitution of ‘template<class _U1, class _U2>  requires  _S_constructible<_U1, _U2>() && _S_dangles<_U1, _U2>() constexpr std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>::pair(_U1&&, _U2&&) [with _U1 = const std::__cxx11::basic_string<char>; _U2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’:
/usr/include/c++/14/bits/stl_construct.h:96:17:   required by substitution of ‘template<class _Tp, class ... _Args> constexpr decltype (::new(void*(0)) _Tp) std::construct_at(_Tp*, _Args&& ...) [with _Tp = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}]’
   96 |     -> decltype(::new((void*)0) _Tp(std::declval<_Args>()...))
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/alloc_traits.h:536:21:   required from ‘static constexpr void std::allocator_traits<std::allocator<_CharT> >::construct(allocator_type&, _Up*, _Args&& ...) [with _Up = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Tp = std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true>; allocator_type = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >]’
  536 |           std::construct_at(__p, std::forward<_Args>(__args)...);
      |           ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable_policy.h:2024:36:   required from ‘std::__detail::_Hashtable_alloc<_NodeAlloc>::__node_type* std::__detail::_Hashtable_alloc<_NodeAlloc>::_M_allocate_node(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _NodeAlloc = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >; __node_ptr = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >::value_type*]’
 2024 |             __node_alloc_traits::construct(__alloc, __n->_M_valptr(),
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~
 2025 |                                            std::forward<_Args>(__args)...);
      |                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable.h:312:35:   required from ‘std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::_Scoped_node::_Scoped_node(std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__hashtable_alloc*, _Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__hashtable_alloc = std::_Hashtable<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::__hashtable_alloc]’
  312 |             _M_node(__h->_M_allocate_node(std::forward<_Args>(__args)...))
      |                     ~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable.h:2143:15:   required from ‘std::pair<typename std::__detail::_Insert<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::iterator, bool> std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::_M_emplace(std::true_type, _Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; typename std::__detail::_Insert<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::iterator = std::__detail::_Insert_base<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::iterator; typename _Traits::__constant_iterators = std::__detail::_Hashtable_traits<true, false, true>::__constant_iterators; std::true_type = std::true_type]’
 2143 |         _Scoped_node __node { this, std::forward<_Args>(__args)...  };
      |                      ^~~~~~
/usr/include/c++/14/bits/hashtable.h:1001:21:   required from ‘std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__ireturn_type std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::emplace(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; __ireturn_type = std::_Hashtable<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::__ireturn_type]’
 1001 |         { return _M_emplace(__unique_keys{}, std::forward<_Args>(__args)...); }
      |                  ~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/unordered_map.h:396:23:   required from ‘std::pair<typename std::_Hashtable<_Key, std::pair<const _Key, _Val>, _Alloc, std::__detail::_Select1st, _Pred, _Hash, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<std::__not_<std::__and_<std::__is_fast_hash<_Hash>, std::__is_nothrow_invocable<const _Hash&, const _Key&> > >::value, false, true> >::iterator, bool> std::unordered_map<_Key, _Tp, _Hash, _Pred, _Alloc>::emplace(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Tp = dbservice::metrics::DatabaseMetrics::QueryMetrics; _Hash = std::hash<std::__cxx11::basic_string<char> >; _Pred = std::equal_to<std::__cxx11::basic_string<char> >; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; typename std::_Hashtable<_Key, std::pair<const _Key, _Val>, _Alloc, std::__detail::_Select1st, _Pred, _Hash, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<std::__not_<std::__and_<std::__is_fast_hash<_Hash>, std::__is_nothrow_invocable<const _Hash&, const _Key&> > >::value, false, true> >::iterator = std::__detail::_Insert_base<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::iterator]’
  396 |         { return _M_h.emplace(std::forward<_Args>(__args)...); }
      |                  ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/metrics/database_metrics.cpp:44:30:   required from here
   44 |         queryMetrics_.emplace(queryType, QueryMetrics{});
      |         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/stl_pair.h:454:2:   required by the constraints of ‘template<class _T1, class _T2> template<class _U1, class _U2>  requires  _S_constructible<_U1, _U2>() && _S_dangles<_U1, _U2>() constexpr std::pair<_T1, _T2>::pair(_U1&&, _U2&&)’
/usr/include/c++/14/bits/stl_pair.h:452:45: note: the expression ‘_S_constructible<_U1, _U2>() [with _T1 = const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics; _U1 = const char* const&; _U2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’ evaluated to ‘false’
  452 |         requires (_S_constructible<_U1, _U2>()) && (_S_dangles<_U1, _U2>())
      |                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/stl_construct.h: In substitution of ‘template<class _Tp, class ... _Args> constexpr decltype (::new(void*(0)) _Tp) std::construct_at(_Tp*, _Args&& ...) [with _Tp = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}]’:
/usr/include/c++/14/bits/alloc_traits.h:536:21:   required from ‘static constexpr void std::allocator_traits<std::allocator<_CharT> >::construct(allocator_type&, _Up*, _Args&& ...) [with _Up = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Tp = std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true>; allocator_type = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >]’
  536 |           std::construct_at(__p, std::forward<_Args>(__args)...);
      |           ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable_policy.h:2024:36:   required from ‘std::__detail::_Hashtable_alloc<_NodeAlloc>::__node_type* std::__detail::_Hashtable_alloc<_NodeAlloc>::_M_allocate_node(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _NodeAlloc = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >; __node_ptr = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >::value_type*]’
 2024 |             __node_alloc_traits::construct(__alloc, __n->_M_valptr(),
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~
 2025 |                                            std::forward<_Args>(__args)...);
      |                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable.h:312:35:   required from ‘std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::_Scoped_node::_Scoped_node(std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__hashtable_alloc*, _Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__hashtable_alloc = std::_Hashtable<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::__hashtable_alloc]’
  312 |             _M_node(__h->_M_allocate_node(std::forward<_Args>(__args)...))
      |                     ~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable.h:2143:15:   required from ‘std::pair<typename std::__detail::_Insert<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::iterator, bool> std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::_M_emplace(std::true_type, _Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; typename std::__detail::_Insert<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::iterator = std::__detail::_Insert_base<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::iterator; typename _Traits::__constant_iterators = std::__detail::_Hashtable_traits<true, false, true>::__constant_iterators; std::true_type = std::true_type]’
 2143 |         _Scoped_node __node { this, std::forward<_Args>(__args)...  };
      |                      ^~~~~~
/usr/include/c++/14/bits/hashtable.h:1001:21:   required from ‘std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__ireturn_type std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::emplace(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; __ireturn_type = std::_Hashtable<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::__ireturn_type]’
 1001 |         { return _M_emplace(__unique_keys{}, std::forward<_Args>(__args)...); }
      |                  ~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/unordered_map.h:396:23:   required from ‘std::pair<typename std::_Hashtable<_Key, std::pair<const _Key, _Val>, _Alloc, std::__detail::_Select1st, _Pred, _Hash, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<std::__not_<std::__and_<std::__is_fast_hash<_Hash>, std::__is_nothrow_invocable<const _Hash&, const _Key&> > >::value, false, true> >::iterator, bool> std::unordered_map<_Key, _Tp, _Hash, _Pred, _Alloc>::emplace(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Tp = dbservice::metrics::DatabaseMetrics::QueryMetrics; _Hash = std::hash<std::__cxx11::basic_string<char> >; _Pred = std::equal_to<std::__cxx11::basic_string<char> >; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; typename std::_Hashtable<_Key, std::pair<const _Key, _Val>, _Alloc, std::__detail::_Select1st, _Pred, _Hash, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<std::__not_<std::__and_<std::__is_fast_hash<_Hash>, std::__is_nothrow_invocable<const _Hash&, const _Key&> > >::value, false, true> >::iterator = std::__detail::_Insert_base<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::iterator]’
  396 |         { return _M_h.emplace(std::forward<_Args>(__args)...); }
      |                  ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/metrics/database_metrics.cpp:44:30:   required from here
   44 |         queryMetrics_.emplace(queryType, QueryMetrics{});
      |         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/stl_pair.h:442:9: note: candidate: ‘template<class _U1, class _U2>  requires  _S_constructible<_U1, _U2>() && !_S_dangles<_U1, _U2>() constexpr std::pair<_T1, _T2>::pair(_U1&&, _U2&&) [with _U2 = _U1; _T1 = const std::__cxx11::basic_string<char>; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’
  442 |         pair(_U1&& __x, _U2&& __y)
      |         ^~~~
/usr/include/c++/14/bits/stl_pair.h:442:9: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/stl_pair.h:442:9: note: constraints not satisfied
/usr/include/c++/14/bits/stl_pair.h: In substitution of ‘template<class _U1, class _U2>  requires  _S_constructible<_U1, _U2>() && !_S_dangles<_U1, _U2>() constexpr std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>::pair(_U1&&, _U2&&) [with _U1 = const std::__cxx11::basic_string<char>; _U2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’:
/usr/include/c++/14/bits/stl_construct.h:96:17:   required by substitution of ‘template<class _Tp, class ... _Args> constexpr decltype (::new(void*(0)) _Tp) std::construct_at(_Tp*, _Args&& ...) [with _Tp = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}]’
   96 |     -> decltype(::new((void*)0) _Tp(std::declval<_Args>()...))
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/alloc_traits.h:536:21:   required from ‘static constexpr void std::allocator_traits<std::allocator<_CharT> >::construct(allocator_type&, _Up*, _Args&& ...) [with _Up = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Tp = std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true>; allocator_type = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >]’
  536 |           std::construct_at(__p, std::forward<_Args>(__args)...);
      |           ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable_policy.h:2024:36:   required from ‘std::__detail::_Hashtable_alloc<_NodeAlloc>::__node_type* std::__detail::_Hashtable_alloc<_NodeAlloc>::_M_allocate_node(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _NodeAlloc = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >; __node_ptr = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >::value_type*]’
 2024 |             __node_alloc_traits::construct(__alloc, __n->_M_valptr(),
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~
 2025 |                                            std::forward<_Args>(__args)...);
      |                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable.h:312:35:   required from ‘std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::_Scoped_node::_Scoped_node(std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__hashtable_alloc*, _Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__hashtable_alloc = std::_Hashtable<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::__hashtable_alloc]’
  312 |             _M_node(__h->_M_allocate_node(std::forward<_Args>(__args)...))
      |                     ~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable.h:2143:15:   required from ‘std::pair<typename std::__detail::_Insert<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::iterator, bool> std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::_M_emplace(std::true_type, _Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; typename std::__detail::_Insert<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::iterator = std::__detail::_Insert_base<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::iterator; typename _Traits::__constant_iterators = std::__detail::_Hashtable_traits<true, false, true>::__constant_iterators; std::true_type = std::true_type]’
 2143 |         _Scoped_node __node { this, std::forward<_Args>(__args)...  };
      |                      ^~~~~~
/usr/include/c++/14/bits/hashtable.h:1001:21:   required from ‘std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__ireturn_type std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::emplace(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; __ireturn_type = std::_Hashtable<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::__ireturn_type]’
 1001 |         { return _M_emplace(__unique_keys{}, std::forward<_Args>(__args)...); }
      |                  ~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/unordered_map.h:396:23:   required from ‘std::pair<typename std::_Hashtable<_Key, std::pair<const _Key, _Val>, _Alloc, std::__detail::_Select1st, _Pred, _Hash, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<std::__not_<std::__and_<std::__is_fast_hash<_Hash>, std::__is_nothrow_invocable<const _Hash&, const _Key&> > >::value, false, true> >::iterator, bool> std::unordered_map<_Key, _Tp, _Hash, _Pred, _Alloc>::emplace(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Tp = dbservice::metrics::DatabaseMetrics::QueryMetrics; _Hash = std::hash<std::__cxx11::basic_string<char> >; _Pred = std::equal_to<std::__cxx11::basic_string<char> >; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; typename std::_Hashtable<_Key, std::pair<const _Key, _Val>, _Alloc, std::__detail::_Select1st, _Pred, _Hash, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<std::__not_<std::__and_<std::__is_fast_hash<_Hash>, std::__is_nothrow_invocable<const _Hash&, const _Key&> > >::value, false, true> >::iterator = std::__detail::_Insert_base<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::iterator]’
  396 |         { return _M_h.emplace(std::forward<_Args>(__args)...); }
      |                  ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/metrics/database_metrics.cpp:44:30:   required from here
   44 |         queryMetrics_.emplace(queryType, QueryMetrics{});
      |         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/stl_pair.h:442:2:   required by the constraints of ‘template<class _T1, class _T2> template<class _U1, class _U2>  requires  _S_constructible<_U1, _U2>() && !_S_dangles<_U1, _U2>() constexpr std::pair<_T1, _T2>::pair(_U1&&, _U2&&)’
/usr/include/c++/14/bits/stl_pair.h:440:45: note: the expression ‘_S_constructible<_U1, _U2>() [with _T1 = const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics; _U1 = const char* const&; _U2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’ evaluated to ‘false’
  440 |         requires (_S_constructible<_U1, _U2>()) && (!_S_dangles<_U1, _U2>())
      |                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/stl_construct.h: In substitution of ‘template<class _Tp, class ... _Args> constexpr decltype (::new(void*(0)) _Tp) std::construct_at(_Tp*, _Args&& ...) [with _Tp = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}]’:
/usr/include/c++/14/bits/alloc_traits.h:536:21:   required from ‘static constexpr void std::allocator_traits<std::allocator<_CharT> >::construct(allocator_type&, _Up*, _Args&& ...) [with _Up = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Tp = std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true>; allocator_type = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >]’
  536 |           std::construct_at(__p, std::forward<_Args>(__args)...);
      |           ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable_policy.h:2024:36:   required from ‘std::__detail::_Hashtable_alloc<_NodeAlloc>::__node_type* std::__detail::_Hashtable_alloc<_NodeAlloc>::_M_allocate_node(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _NodeAlloc = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >; __node_ptr = std::allocator<std::__detail::_Hash_node<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, true> >::value_type*]’
 2024 |             __node_alloc_traits::construct(__alloc, __n->_M_valptr(),
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~
 2025 |                                            std::forward<_Args>(__args)...);
      |                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable.h:312:35:   required from ‘std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::_Scoped_node::_Scoped_node(std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__hashtable_alloc*, _Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__hashtable_alloc = std::_Hashtable<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::__hashtable_alloc]’
  312 |             _M_node(__h->_M_allocate_node(std::forward<_Args>(__args)...))
      |                     ~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/hashtable.h:2143:15:   required from ‘std::pair<typename std::__detail::_Insert<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::iterator, bool> std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::_M_emplace(std::true_type, _Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; typename std::__detail::_Insert<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::iterator = std::__detail::_Insert_base<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::iterator; typename _Traits::__constant_iterators = std::__detail::_Hashtable_traits<true, false, true>::__constant_iterators; std::true_type = std::true_type]’
 2143 |         _Scoped_node __node { this, std::forward<_Args>(__args)...  };
      |                      ^~~~~~
/usr/include/c++/14/bits/hashtable.h:1001:21:   required from ‘std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::__ireturn_type std::_Hashtable<_Key, _Value, _Alloc, _ExtractKey, _Equal, _Hash, _RangeHash, _Unused, _RehashPolicy, _Traits>::emplace(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Value = std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; _ExtractKey = std::__detail::_Select1st; _Equal = std::equal_to<std::__cxx11::basic_string<char> >; _Hash = std::hash<std::__cxx11::basic_string<char> >; _RangeHash = std::__detail::_Mod_range_hashing; _Unused = std::__detail::_Default_ranged_hash; _RehashPolicy = std::__detail::_Prime_rehash_policy; _Traits = std::__detail::_Hashtable_traits<true, false, true>; __ireturn_type = std::_Hashtable<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::__ireturn_type]’
 1001 |         { return _M_emplace(__unique_keys{}, std::forward<_Args>(__args)...); }
      |                  ~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/unordered_map.h:396:23:   required from ‘std::pair<typename std::_Hashtable<_Key, std::pair<const _Key, _Val>, _Alloc, std::__detail::_Select1st, _Pred, _Hash, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<std::__not_<std::__and_<std::__is_fast_hash<_Hash>, std::__is_nothrow_invocable<const _Hash&, const _Key&> > >::value, false, true> >::iterator, bool> std::unordered_map<_Key, _Tp, _Hash, _Pred, _Alloc>::emplace(_Args&& ...) [with _Args = {const char* const&, dbservice::metrics::DatabaseMetrics::QueryMetrics}; _Key = std::__cxx11::basic_string<char>; _Tp = dbservice::metrics::DatabaseMetrics::QueryMetrics; _Hash = std::hash<std::__cxx11::basic_string<char> >; _Pred = std::equal_to<std::__cxx11::basic_string<char> >; _Alloc = std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >; typename std::_Hashtable<_Key, std::pair<const _Key, _Val>, _Alloc, std::__detail::_Select1st, _Pred, _Hash, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<std::__not_<std::__and_<std::__is_fast_hash<_Hash>, std::__is_nothrow_invocable<const _Hash&, const _Key&> > >::value, false, true> >::iterator = std::__detail::_Insert_base<std::__cxx11::basic_string<char>, std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics>, std::allocator<std::pair<const std::__cxx11::basic_string<char>, dbservice::metrics::DatabaseMetrics::QueryMetrics> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char> >, std::hash<std::__cxx11::basic_string<char> >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::iterator]’
  396 |         { return _M_h.emplace(std::forward<_Args>(__args)...); }
      |                  ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/metrics/database_metrics.cpp:44:30:   required from here
   44 |         queryMetrics_.emplace(queryType, QueryMetrics{});
      |         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/stl_pair.h:335:9: note: candidate: ‘template<class ... _Args1, long unsigned int ..._Indexes1, class ... _Args2, long unsigned int ..._Indexes2> constexpr std::pair<_T1, _T2>::pair(std::tuple<_Args1 ...>&, std::tuple<_Args2 ...>&, std::_Index_tuple<_Indexes1 ...>, std::_Index_tuple<_Indexes2 ...>) [with _Args1 = {_Args1 ...}; long unsigned int ..._Indexes1 = {_Indexes1 ...}; _Args2 = {_Args2 ...}; long unsigned int ..._Indexes2 = {_Indexes2 ...}; _T1 = const std::__cxx11::basic_string<char>; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’
  335 |         pair(tuple<_Args1...>&, tuple<_Args2...>&,
      |         ^~~~
/usr/include/c++/14/bits/stl_pair.h:335:9: note:   candidate expects 4 arguments, 2 provided
/usr/include/c++/14/bits/stl_pair.h:299:9: note: candidate: ‘template<class ... _Args1, class ... _Args2> constexpr std::pair<_T1, _T2>::pair(std::piecewise_construct_t, std::tuple<_Args1 ...>, std::tuple<_Args2 ...>) [with _Args1 = {_Args1 ...}; _Args2 = {_Args2 ...}; _T1 = const std::__cxx11::basic_string<char>; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’
  299 |         pair(piecewise_construct_t, tuple<_Args1...>, tuple<_Args2...>);
      |         ^~~~
/usr/include/c++/14/bits/stl_pair.h:299:9: note:   candidate expects 3 arguments, 2 provided
/usr/include/c++/14/bits/stl_pair.h:428:7: note: candidate: ‘constexpr std::pair<_T1, _T2>::pair(const _T1&, const _T2&) requires  _S_constructible<const _T1&, const _T2&>() [with _T1 = const std::__cxx11::basic_string<char>; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’
  428 |       pair(const _T1& __x, const _T2& __y)
      |       ^~~~
/usr/include/c++/14/bits/stl_pair.h:428:7: note: constraints not satisfied
/usr/include/c++/14/bits/stl_pair.h:346:7: note: candidate: ‘constexpr std::pair<_T1, _T2>::pair() requires (is_default_constructible_v<_T1>) && (is_default_constructible_v<_T2>) [with _T1 = const std::__cxx11::basic_string<char>; _T2 = dbservice::metrics::DatabaseMetrics::QueryMetrics]’
  346 |       pair()
      |       ^~~~
/usr/include/c++/14/bits/stl_pair.h:346:7: note:   candidate expects 0 arguments, 2 provided
make[2]: *** [CMakeFiles/database-service.dir/build.make:177: CMakeFiles/database-service.dir/src/metrics/database_metrics.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
