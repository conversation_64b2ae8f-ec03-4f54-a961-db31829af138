#include "database-service/database_service.hpp"
#include "database-service/api/api_server.hpp"
#include "database-service/api/route_controller.hpp"
#include "database-service/utils/logger.hpp"
#include "database-service/utils/config_manager.hpp"
#include <iostream>
#include <string>
#include <csignal>
#include <thread>
#include <chrono>
#include <atomic>
#include <expected>
#include <format>
#include <print>

// Global variables
std::shared_ptr<dbservice::DatabaseService> g_databaseService;
std::atomic<bool> g_running(true);

// Signal handler
void signalHandler(int signal) {
    std::cout << "Received signal " << signal << std::endl;
    g_running = false;
    if (g_databaseService) {
        g_databaseService->stop();
    }
}

int main(int argc, char** argv) {
    // Register signal handlers
    std::signal(SIGINT, signalHandler);
    std::signal(SIGTERM, signalHandler);

    // Parse command line arguments
    std::string configFile = "config.json";
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        if (arg == "--config" && i + 1 < argc) {
            configFile = argv[++i];
        } else if (arg == "--help") {
            std::cout << "Usage: " << argv[0] << " [--config <config_file>] [--help]" << std::endl;
            return 0;
        }
    }

    try {
        // Initialize logger with a safe default path
        auto logResult = dbservice::utils::Logger::initialize("/var/log/database-service/database-service.log", "info");
        if (!logResult) {
            std::print(stderr, "Failed to initialize logger: {}\n", logResult.error());
            std::print(stderr, "Continuing without file logging...\n");
        }

        dbservice::utils::Logger::info("Starting database service...");
        dbservice::utils::Logger::info(std::format("Process ID: {}", getpid()));
        dbservice::utils::Logger::info(std::format("Configuration file: {}", configFile));

        // Create database service
        g_databaseService = std::make_shared<dbservice::DatabaseService>();

        // Load configuration
        auto configResult = g_databaseService->loadConfig(configFile);
        if (!configResult) {
            std::print(stderr, "Failed to load configuration: {}\n", configResult.error());
            dbservice::utils::Logger::error(std::format("Failed to load configuration: {}", configResult.error()));
            return 1;
        }

        // Initialize database service
        auto initResult = g_databaseService->initialize();
        if (!initResult) {
            std::print(stderr, "Failed to initialize database service: {}\n", initResult.error());
            dbservice::utils::Logger::error(std::format("Failed to initialize database service: {}", initResult.error()));
            return 1;
        }

        // Start database service
        auto startResult = g_databaseService->start();
        if (!startResult) {
            std::print(stderr, "Failed to start database service: {}\n", startResult.error());
            dbservice::utils::Logger::error(std::format("Failed to start database service: {}", startResult.error()));
            return 1;
        }

        dbservice::utils::Logger::info("Database service started successfully");

        // Wait for signal to stop
        while (g_running) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        // Stop database service
        dbservice::utils::Logger::info("Stopping database service...");
        g_databaseService->stop();
        dbservice::utils::Logger::info("Database service stopped successfully");

        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        dbservice::utils::Logger::critical("Exception: " + std::string(e.what()));
        return 1;
    }
}
