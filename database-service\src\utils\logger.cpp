// C++ Standard Library
#include <iostream>
#include <chrono>
#include <iomanip>
#include <sstream>
#include "database-service/utils/filesystem_wrapper.hpp"
#include <format> // C++20 feature

// Project-specific headers
#include "database-service/utils/logger.hpp"

namespace dbservice::utils {

// Static member initialization
std::string Logger::logFile_;
std::string Logger::logLevel_;
std::ofstream Logger::logStream_;
std::mutex Logger::mutex_;
bool Logger::initialized_ = false;

std::expected<void, std::string> Logger::initialize(std::string_view logFile, std::string_view logLevel) {
    std::lock_guard<std::mutex> lock(mutex_);

    if (initialized_) {
        return {};
    }

    try {
        logFile_ = std::string(logFile);
        logLevel_ = std::string(logLevel);

        // Create directory if it doesn't exist
        std::filesystem::path logPath(logFile_);
        std::filesystem::create_directories(logPath.parent_path());

        // Open log file
        logStream_.open(logFile_, std::ios::out | std::ios::app);
        if (!logStream_.is_open()) {
            std::string errMsg = std::format("Failed to open log file: {}", logFile_);
            std::cerr << errMsg << std::endl;
            return std::unexpected(errMsg);
        }

        initialized_ = true;

        info(std::format("Logger initialized with level: {}", logLevel_));
        return {};
    } catch (const std::exception& e) {
        std::string errMsg = std::format("Exception during logger initialization: {}", e.what());
        std::cerr << errMsg << std::endl;
        return std::unexpected(errMsg);
    }
}

void Logger::debug(std::string_view message, const std::source_location& location) {
    if (logLevel_ == "debug") {
        log("DEBUG", message, location);
    }
}

void Logger::info(std::string_view message, const std::source_location& location) {
    if (logLevel_ == "debug" || logLevel_ == "info") {
        log("INFO", message, location);
    }
}

void Logger::warning(std::string_view message, const std::source_location& location) {
    if (logLevel_ == "debug" || logLevel_ == "info" || logLevel_ == "warning") {
        log("WARNING", message, location);
    }
}

void Logger::error(std::string_view message, const std::source_location& location) {
    if (logLevel_ == "debug" || logLevel_ == "info" || logLevel_ == "warning" || logLevel_ == "error") {
        log("ERROR", message, location);
    }
}

void Logger::critical(std::string_view message, const std::source_location& location) {
    log("CRITICAL", message, location);
}

std::expected<void, std::string> Logger::setLogLevel(std::string_view logLevel) {
    std::lock_guard<std::mutex> lock(mutex_);

    // Validate log level
    if (logLevel != "debug" && logLevel != "info" && logLevel != "warning" && logLevel != "error" && logLevel != "critical") {
        return std::unexpected(std::format("Invalid log level: {}", logLevel));
    }

    logLevel_ = std::string(logLevel);
    info(std::format("Log level changed to: {}", logLevel_));
    return {};
}

std::string_view Logger::getLogLevel() {
    std::lock_guard<std::mutex> lock(mutex_);
    return logLevel_;
}

void Logger::shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return;
    }
    
    info("Logger shutting down");
    
    if (logStream_.is_open()) {
        logStream_.close();
    }
    
    initialized_ = false;
}

void Logger::log(std::string_view level, std::string_view message, const std::source_location& location) {
    std::lock_guard<std::mutex> lock(mutex_);

    std::string timestamp = getTimestamp();
    std::string logMessage = std::format("[{}] [{}] [{}:{}] {}",
        timestamp, level, location.file_name(), location.line(), message);

    // Write to console
    std::cout << logMessage << std::endl;

    // Write to file if initialized
    if (initialized_ && logStream_.is_open()) {
        logStream_ << logMessage << std::endl;
        logStream_.flush();
    }
}

std::string Logger::getTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time), "%Y-%m-%d %H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    
    return ss.str();
}

} // namespace dbservice::utils
