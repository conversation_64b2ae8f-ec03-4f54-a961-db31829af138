// C++ Standard Library
#include <iostream>
#include <chrono>
#include <iomanip>
#include <sstream>
#include "database-service/utils/filesystem_wrapper.hpp"
#include <format> // C++20 feature

// Project-specific headers
#include "database-service/utils/logger.hpp"

namespace dbservice::utils {

// Static member initialization
std::string Logger::logFile_;
std::string Logger::logLevel_;
std::ofstream Logger::logStream_;
std::mutex Logger::mutex_;
bool Logger::initialized_ = false;

bool Logger::initialize(const std::string& logFile, const std::string& logLevel) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (initialized_) {
        return true;
    }
    
    try {
        logFile_ = logFile;
        logLevel_ = logLevel;
        
        // Create directory if it doesn't exist
        std::filesystem::path logPath(logFile_);
        std::filesystem::create_directories(logPath.parent_path());
        
        // Open log file
        logStream_.open(logFile_, std::ios::out | std::ios::app);
        if (!logStream_.is_open()) {
            std::cerr << std::format("Failed to open log file: {}", logFile_) << std::endl;
            return false;
        }
        
        initialized_ = true;
        
        info(std::format("Logger initialized with level: {}", logLevel_));
        return true;
    } catch (const std::exception& e) {
        std::cerr << std::format("Exception during logger initialization: {}", e.what()) << std::endl;
        return false;
    }
}

void Logger::debug(const std::string& message) {
    if (logLevel_ == "debug") {
        log("DEBUG", message);
    }
}

void Logger::info(const std::string& message) {
    if (logLevel_ == "debug" || logLevel_ == "info") {
        log("INFO", message);
    }
}

void Logger::warning(const std::string& message) {
    if (logLevel_ == "debug" || logLevel_ == "info" || logLevel_ == "warning") {
        log("WARNING", message);
    }
}

void Logger::error(const std::string& message) {
    if (logLevel_ == "debug" || logLevel_ == "info" || logLevel_ == "warning" || logLevel_ == "error") {
        log("ERROR", message);
    }
}

void Logger::critical(const std::string& message) {
    log("CRITICAL", message);
}

void Logger::setLogLevel(const std::string& logLevel) {
    std::lock_guard<std::mutex> lock(mutex_);
    logLevel_ = logLevel;
    info(std::format("Log level changed to: {}", logLevel_));
}

std::string Logger::getLogLevel() {
    std::lock_guard<std::mutex> lock(mutex_);
    return logLevel_;
}

void Logger::shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return;
    }
    
    info("Logger shutting down");
    
    if (logStream_.is_open()) {
        logStream_.close();
    }
    
    initialized_ = false;
}

void Logger::log(const std::string& level, const std::string& message) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::string timestamp = getTimestamp();
    std::string logMessage = std::format("[{}] [{}] {}", timestamp, level, message);
    
    // Write to console
    std::cout << logMessage << std::endl;
    
    // Write to file if initialized
    if (initialized_ && logStream_.is_open()) {
        logStream_ << logMessage << std::endl;
        logStream_.flush();
    }
}

std::string Logger::getTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time), "%Y-%m-%d %H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    
    return ss.str();
}

} // namespace dbservice::utils
