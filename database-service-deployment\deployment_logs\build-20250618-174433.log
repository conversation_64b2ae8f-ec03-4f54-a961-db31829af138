Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  5%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:22:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   22 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[ 16%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 22%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
[ 27%] Building CXX object CMakeFiles/database-service.dir/src/core/transaction.cpp.o
[ 33%] Building CXX object CMakeFiles/database-service.dir/src/database_service.cpp.o
[ 38%] Building CXX object CMakeFiles/database-service.dir/src/main.cpp.o
[ 44%] Building CXX object CMakeFiles/database-service.dir/src/metrics/database_metrics.cpp.o
[ 50%] Building CXX object CMakeFiles/database-service.dir/src/metrics/metrics_collector.cpp.o
[ 55%] Building CXX object CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o
[ 61%] Building CXX object CMakeFiles/database-service.dir/src/security/credential_store.cpp.o
/home/<USER>/database-service-build/src/security/credential_store.cpp: In member function ‘bool dbservice::security::CredentialStore::initialize(const std::string&)’:
/home/<USER>/database-service-build/src/security/credential_store.cpp:41:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   41 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/credential_store.cpp:6:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:42:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   42 |     SHA256_Update(&sha256, encryptionKey.c_str(), encryptionKey.length());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:43:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   43 |     SHA256_Final(key, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
[ 66%] Building CXX object CMakeFiles/database-service.dir/src/security/jwt.cpp.o
[ 72%] Building CXX object CMakeFiles/database-service.dir/src/security/security_manager.cpp.o
/home/<USER>/database-service-build/src/security/security_manager.cpp: In function ‘std::string dbservice::security::sha256(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:30:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   30 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/security_manager.cpp:13:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:31:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   31 |     SHA256_Update(&sha256, input.c_str(), input.size());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:32:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   32 |     SHA256_Final(hash, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
[ 77%] Building CXX object CMakeFiles/database-service.dir/src/utils/cache.cpp.o
[ 83%] Building CXX object CMakeFiles/database-service.dir/src/utils/config_manager.cpp.o
[ 88%] Building CXX object CMakeFiles/database-service.dir/src/utils/logger.cpp.o
/home/<USER>/database-service-build/src/utils/logger.cpp:21:6: error: no declaration matches ‘bool dbservice::utils::Logger::initialize(const std::string&, const std::string&)’
   21 | bool Logger::initialize(const std::string& logFile, const std::string& logLevel) {
      |      ^~~~~~
In file included from /home/<USER>/database-service-build/src/utils/logger.cpp:10:
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:24:45: note: candidate is: ‘static std::expected<void, std::__cxx11::basic_string<char> > dbservice::utils::Logger::initialize(std::string_view, std::string_view)’
   24 |     static std::expected<void, std::string> initialize(std::string_view logFile, std::string_view logLevel = "info");
      |                                             ^~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:16:7: note: ‘class dbservice::utils::Logger’ defined here
   16 | class Logger {
      |       ^~~~~~
/home/<USER>/database-service-build/src/utils/logger.cpp:53:6: error: no declaration matches ‘void dbservice::utils::Logger::debug(const std::string&)’
   53 | void Logger::debug(const std::string& message) {
      |      ^~~~~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:31:17: note: candidate is: ‘static void dbservice::utils::Logger::debug(std::string_view, const std::source_location&)’
   31 |     static void debug(std::string_view message, const std::source_location& location = std::source_location::current());
      |                 ^~~~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:16:7: note: ‘class dbservice::utils::Logger’ defined here
   16 | class Logger {
      |       ^~~~~~
/home/<USER>/database-service-build/src/utils/logger.cpp:59:6: error: no declaration matches ‘void dbservice::utils::Logger::info(const std::string&)’
   59 | void Logger::info(const std::string& message) {
      |      ^~~~~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:38:17: note: candidate is: ‘static void dbservice::utils::Logger::info(std::string_view, const std::source_location&)’
   38 |     static void info(std::string_view message, const std::source_location& location = std::source_location::current());
      |                 ^~~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:16:7: note: ‘class dbservice::utils::Logger’ defined here
   16 | class Logger {
      |       ^~~~~~
/home/<USER>/database-service-build/src/utils/logger.cpp:65:6: error: no declaration matches ‘void dbservice::utils::Logger::warning(const std::string&)’
   65 | void Logger::warning(const std::string& message) {
      |      ^~~~~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:45:17: note: candidate is: ‘static void dbservice::utils::Logger::warning(std::string_view, const std::source_location&)’
   45 |     static void warning(std::string_view message, const std::source_location& location = std::source_location::current());
      |                 ^~~~~~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:16:7: note: ‘class dbservice::utils::Logger’ defined here
   16 | class Logger {
      |       ^~~~~~
/home/<USER>/database-service-build/src/utils/logger.cpp:71:6: error: no declaration matches ‘void dbservice::utils::Logger::error(const std::string&)’
   71 | void Logger::error(const std::string& message) {
      |      ^~~~~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:52:17: note: candidate is: ‘static void dbservice::utils::Logger::error(std::string_view, const std::source_location&)’
   52 |     static void error(std::string_view message, const std::source_location& location = std::source_location::current());
      |                 ^~~~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:16:7: note: ‘class dbservice::utils::Logger’ defined here
   16 | class Logger {
      |       ^~~~~~
/home/<USER>/database-service-build/src/utils/logger.cpp:77:6: error: no declaration matches ‘void dbservice::utils::Logger::critical(const std::string&)’
   77 | void Logger::critical(const std::string& message) {
      |      ^~~~~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:59:17: note: candidate is: ‘static void dbservice::utils::Logger::critical(std::string_view, const std::source_location&)’
   59 |     static void critical(std::string_view message, const std::source_location& location = std::source_location::current());
      |                 ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:16:7: note: ‘class dbservice::utils::Logger’ defined here
   16 | class Logger {
      |       ^~~~~~
/home/<USER>/database-service-build/src/utils/logger.cpp:81:6: error: no declaration matches ‘void dbservice::utils::Logger::setLogLevel(const std::string&)’
   81 | void Logger::setLogLevel(const std::string& logLevel) {
      |      ^~~~~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:66:45: note: candidate is: ‘static std::expected<void, std::__cxx11::basic_string<char> > dbservice::utils::Logger::setLogLevel(std::string_view)’
   66 |     static std::expected<void, std::string> setLogLevel(std::string_view logLevel);
      |                                             ^~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:16:7: note: ‘class dbservice::utils::Logger’ defined here
   16 | class Logger {
      |       ^~~~~~
/home/<USER>/database-service-build/src/utils/logger.cpp:87:13: error: no declaration matches ‘std::string dbservice::utils::Logger::getLogLevel()’
   87 | std::string Logger::getLogLevel() {
      |             ^~~~~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:72:29: note: candidate is: ‘static std::string_view dbservice::utils::Logger::getLogLevel()’
   72 |     static std::string_view getLogLevel();
      |                             ^~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:16:7: note: ‘class dbservice::utils::Logger’ defined here
   16 | class Logger {
      |       ^~~~~~
/home/<USER>/database-service-build/src/utils/logger.cpp:108:6: error: no declaration matches ‘void dbservice::utils::Logger::log(const std::string&, const std::string&)’
  108 | void Logger::log(const std::string& level, const std::string& message) {
      |      ^~~~~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:86:17: note: candidate is: ‘static void dbservice::utils::Logger::log(std::string_view, std::string_view, const std::source_location&)’
   86 |     static void log(std::string_view level, std::string_view message, const std::source_location& location);
      |                 ^~~
/home/<USER>/database-service-build/include/database-service/utils/logger.hpp:16:7: note: ‘class dbservice::utils::Logger’ defined here
   16 | class Logger {
      |       ^~~~~~
make[2]: *** [CMakeFiles/database-service.dir/build.make:289: CMakeFiles/database-service.dir/src/utils/logger.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
