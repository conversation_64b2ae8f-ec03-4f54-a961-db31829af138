#include "database-service/database_service.hpp"
#include "database-service/api/api_server.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/schema/schema_manager.hpp"
#include "database-service/security/security_manager.hpp"
#include "database-service/security/credential_store.hpp"
#include "database-service/api/route_controller.hpp"

#include "database-service/utils/config_manager.hpp"
#include "database-service/utils/logger.hpp"
#include <iostream>
#include <thread>
#include <chrono>
#include <condition_variable>
#include <expected>
#include <format>
#include <print>

namespace dbservice {

DatabaseService::DatabaseService()
    : initialized_(false),
      running_(false) {
}

DatabaseService::~DatabaseService() {
    stop();
}

std::expected<void, std::string> DatabaseService::initialize() {
    if (initialized_.load(std::memory_order_relaxed)) {
        return {};
    }

    try {
        utils::Logger::info("Initializing database service...");

        // Load configuration
        auto& configManager = utils::ConfigManager::getInstance();

        // Initialize connection manager
        std::string connectionString =
            std::format("host={} port={} dbname={} user={}",
                configManager.getString("database.host", "localhost"),
                configManager.getInt("database.port", 5432),
                configManager.getString("database.name", "postgres"),
                configManager.getString("database.user", "postgres"));

        std::string password = configManager.getString("database.password", "");
        if (!password.empty()) {
            connectionString += std::format(" password={}", password);
        }

        // Create SSL configuration
        core::SSLConfig sslConfig;
        sslConfig.enabled = configManager.getBool("database.ssl.enabled", false);

        if (sslConfig.enabled) {
            // Get SSL mode
            std::string sslModeStr = configManager.getString("database.ssl.mode", "verify-full");
            if (sslModeStr == "disable") {
                sslConfig.mode = core::SSLMode::Disable;
            } else if (sslModeStr == "allow") {
                sslConfig.mode = core::SSLMode::Allow;
            } else if (sslModeStr == "prefer") {
                sslConfig.mode = core::SSLMode::Prefer;
            } else if (sslModeStr == "require") {
                sslConfig.mode = core::SSLMode::Require;
            } else if (sslModeStr == "verify-ca") {
                sslConfig.mode = core::SSLMode::VerifyCa;
            } else if (sslModeStr == "verify-full") {
                sslConfig.mode = core::SSLMode::VerifyFull;
            } else {
                utils::Logger::warning(std::format("Unknown SSL mode: {}, using verify-full", sslModeStr));
                sslConfig.mode = core::SSLMode::VerifyFull;
            }

            // Get certificate paths
            sslConfig.certPath = configManager.getString("database.ssl.cert_path", "");
            sslConfig.keyPath = configManager.getString("database.ssl.key_path", "");
            sslConfig.caPath = configManager.getString("database.ssl.ca_path", "");
            sslConfig.crlPath = configManager.getString("database.ssl.crl_path", "");
            sslConfig.rejectExpired = configManager.getBool("database.ssl.reject_expired", true);

            utils::Logger::info(std::format("SSL configuration: mode={}, cert={}, key={}, ca={}",
                sslModeStr,
                sslConfig.certPath.empty() ? "not set" : sslConfig.certPath,
                sslConfig.keyPath.empty() ? "not set" : sslConfig.keyPath,
                sslConfig.caPath.empty() ? "not set" : sslConfig.caPath));
        }

        connectionManager_ = std::make_shared<core::ConnectionManager>(
            connectionString,
            configManager.getInt("database.pool.max_connections", 10),
            sslConfig
        );

        // Initialize security manager
        securityManager_ = std::make_shared<security::SecurityManager>(connectionManager_);

        // Configure security manager with JWT settings
        std::string jwtSecret = configManager.getString("security.jwt_secret", "change-this-to-a-secure-secret-in-production");
        int tokenExpirationSeconds = configManager.getInt("security.token_expiration_seconds", 3600);
        int refreshTokenExpirationSeconds = configManager.getInt("security.refresh_token_expiration_seconds", 86400);

        securityManager_->setJwtSecret(jwtSecret);
        securityManager_->setTokenExpirationTimes(tokenExpirationSeconds, refreshTokenExpirationSeconds);

        // Initialize credential store if secure credential storage is enabled
        bool enableSecureCredentialStorage = configManager.getBool("security.secure_credential_storage.enabled", false);
        if (enableSecureCredentialStorage) {
            utils::Logger::info("Initializing secure credential storage");
            std::string encryptionKey = configManager.getString("security.secure_credential_storage.encryption_key", "");

            if (encryptionKey.empty()) {
                utils::Logger::warning("Secure credential storage is enabled but encryption key is not set");
            } else {
                auto& credentialStore = security::CredentialStore::getInstance();
                if (!credentialStore.initialize(encryptionKey)) {
                    return std::unexpected("Failed to initialize secure credential storage");
                }
            }
        }

        // Initialize schema manager
        schemaManager_ = std::make_shared<schema::SchemaManager>(
            connectionManager_,
            configManager.getString("schema.directory", "")
        );

        // Initialize API server
        unsigned short apiPort = configManager.getInt("api.port", 8080);
        apiServer_ = std::make_shared<api::ApiServer>(
            apiPort,
            connectionManager_,
            securityManager_,
            shared_from_this()
        );

        // Configure API server with SSL settings
        api::SSLConfig apiSslConfig;
        apiSslConfig.enabled = configManager.getBool("api.ssl.enabled", false);

        if (apiSslConfig.enabled) {
            utils::Logger::info("Configuring SSL for API server");
            apiSslConfig.certPath = configManager.getString("api.ssl.cert_path", "");
            apiSslConfig.keyPath = configManager.getString("api.ssl.key_path", "");

            if (apiSslConfig.certPath.empty() || apiSslConfig.keyPath.empty()) {
                utils::Logger::warning("SSL is enabled but certificate or key path is not set");
                apiSslConfig.enabled = false;
            } else {
                apiServer_->configureSSL(apiSslConfig);
            }
        }

        // Configure API server with CORS settings
        api::CorsConfig corsConfig;
        corsConfig.enabled = configManager.getBool("api.cors.enabled", false);

        if (corsConfig.enabled) {
            utils::Logger::info("Configuring CORS for API server");

            // Load allowed origins using ranges
            auto originsJson = configManager.getJsonArray("api.cors.allowed_origins");
            if (!originsJson.empty()) {
                for (const auto& origin : originsJson) {
                    if (origin.is_string()) {
                        corsConfig.allowedOrigins.push_back(origin);
                    }
                }
            } else {
                // Default to all origins
                corsConfig.allowedOrigins.push_back("*");
            }

            // Load allowed methods
            auto methodsJson = configManager.getJsonArray("api.cors.allowed_methods");
            if (!methodsJson.empty()) {
                for (const auto& method : methodsJson) {
                    if (method.is_string()) {
                        corsConfig.allowedMethods.push_back(method);
                    }
                }
            } else {
                // Default methods
                corsConfig.allowedMethods = {"GET", "POST", "PUT", "DELETE", "OPTIONS"};
            }

            // Load allowed headers
            auto headersJson = configManager.getJsonArray("api.cors.allowed_headers");
            if (!headersJson.empty()) {
                for (const auto& header : headersJson) {
                    if (header.is_string()) {
                        corsConfig.allowedHeaders.push_back(header);
                    }
                }
            } else {
                // Default headers
                corsConfig.allowedHeaders = {"Content-Type", "Authorization"};
            }

            // Load other settings
            corsConfig.allowCredentials = configManager.getBool("api.cors.allow_credentials", false);
            corsConfig.maxAge = configManager.getInt("api.cors.max_age", 86400);

            apiServer_->configureCors(corsConfig);
        }

        // Register API routes
        utils::Logger::info("Registering API routes...");
        api::RouteController routeController(connectionManager_, securityManager_);
        routeController.registerRoutes(*apiServer_);
        utils::Logger::info("API routes registered successfully");

        initialized_.store(true, std::memory_order_relaxed);
        utils::Logger::info("Database service initialized successfully");
        return {};
    } catch (const std::exception& e) {
        std::string errorMsg = std::format("Failed to initialize database service: {}", e.what());
        utils::Logger::error(errorMsg);
        return std::unexpected(errorMsg);
    }
}

std::expected<void, std::string> DatabaseService::loadConfig(const std::string& configFile) {
    try {
        utils::Logger::info(std::format("Loading configuration from {}", configFile));

        auto& configManager = utils::ConfigManager::getInstance();

        // Load configuration from file
        if (!configManager.loadFromFile(configFile)) {
            std::string errorMsg = std::format("Failed to load configuration from file: {}", configFile);
            utils::Logger::error(errorMsg);
            return std::unexpected(errorMsg);
        }

        return {};
    } catch (const std::exception& e) {
        std::string errorMsg = std::format("Failed to load configuration: {}", e.what());
        utils::Logger::error(errorMsg);
        return std::unexpected(errorMsg);
    }
}

std::expected<void, std::string> DatabaseService::start() {
    if (!initialized_.load(std::memory_order_relaxed)) {
        std::string errorMsg = "Cannot start uninitialized service";
        utils::Logger::error(errorMsg);
        return std::unexpected(errorMsg);
    }

    if (running_.load(std::memory_order_relaxed)) {
        utils::Logger::warning("Service is already running");
        return {};
    }

    try {
        utils::Logger::info("Starting database service...");

        // Start API server
        auto apiStartResult = apiServer_->start();
        if (!apiStartResult) {
            std::string errorMsg = std::format("Failed to start API server: {}", apiStartResult.error().message);
            utils::Logger::error(errorMsg);
            return std::unexpected(errorMsg);
        }

        running_.store(true, std::memory_order_relaxed);
        utils::Logger::info("Database service started successfully");
        return {};
    } catch (const std::exception& e) {
        std::string errorMsg = std::format("Failed to start database service: {}", e.what());
        utils::Logger::error(errorMsg);
        return std::unexpected(errorMsg);
    }
}

void DatabaseService::stop() {
    if (!running_.load(std::memory_order_relaxed)) {
        return;
    }

    try {
        utils::Logger::info("Stopping database service...");

        // Stop API server
        if (apiServer_) {
            auto apiStopResult = apiServer_->stop();
            if (!apiStopResult) {
                // Log error but continue shutdown
                utils::Logger::error(std::format("Error stopping API server: {}", apiStopResult.error().message));
            }
        }

        // Stop connection manager
        if (connectionManager_) {
            connectionManager_->shutdown();
        }



        running_.store(false, std::memory_order_relaxed);

        // Notify waiting threads
        std::unique_lock<std::mutex> lock(waitMutex_);
        waitCondition_.notify_all();

        utils::Logger::info("Database service stopped successfully");
    } catch (const std::exception& e) {
        utils::Logger::error("Error stopping database service: " + std::string(e.what()));
    }
}

void DatabaseService::wait() {
    if (!running_) {
        return;
    }

    std::unique_lock<std::mutex> lock(waitMutex_);
    waitCondition_.wait(lock, [this] { return !running_.load(std::memory_order_relaxed); });
}

bool DatabaseService::isRunning() const {
    return running_.load(std::memory_order_relaxed);
}

std::shared_ptr<api::ApiServer> DatabaseService::getApiServer() const {
    return apiServer_;
}

std::shared_ptr<core::ConnectionManager> DatabaseService::getConnectionManager() const {
    return connectionManager_;
}

std::shared_ptr<schema::SchemaManager> DatabaseService::getSchemaManager() const {
    return schemaManager_;
}

std::shared_ptr<security::SecurityManager> DatabaseService::getSecurityManager() const {
    return securityManager_;
}

std::expected<nlohmann::json, std::string> DatabaseService::getDatabaseMetrics() const {
    try {
        nlohmann::json metrics;

        // Basic database metrics
        metrics["status"] = isRunning() ? "running" : "stopped";
        metrics["initialized"] = initialized_.load(std::memory_order_relaxed);

        // Connection metrics
        if (connectionManager_) {
            auto poolMetrics = connectionManager_->getMetrics();
            if (poolMetrics) {
                metrics["connection_pool"] = *poolMetrics;
            }
        }

        // Add timestamp
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        metrics["timestamp"] = std::format("{:%Y-%m-%d %H:%M:%S}", std::chrono::system_clock::from_time_t(time_t));

        return metrics;
    } catch (const std::exception& e) {
        return std::unexpected(std::format("Failed to get database metrics: {}", e.what()));
    }
}

std::expected<nlohmann::json, std::string> DatabaseService::getConnectionPoolMetrics() const {
    try {
        if (!connectionManager_) {
            return std::unexpected("Connection manager not initialized");
        }

        auto metrics = connectionManager_->getMetrics();
        if (!metrics) {
            return std::unexpected("Failed to get connection pool metrics");
        }

        return *metrics;
    } catch (const std::exception& e) {
        return std::unexpected(std::format("Failed to get connection pool metrics: {}", e.what()));
    }
}

std::expected<nlohmann::json, std::string> DatabaseService::getQueryPerformanceMetrics() const {
    try {
        nlohmann::json metrics;

        // Placeholder implementation - would need actual query performance tracking
        metrics["total_queries"] = 0;
        metrics["average_query_time_ms"] = 0.0;
        metrics["slow_queries"] = 0;
        metrics["failed_queries"] = 0;

        // Add timestamp
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        metrics["timestamp"] = std::format("{:%Y-%m-%d %H:%M:%S}", std::chrono::system_clock::from_time_t(time_t));

        return metrics;
    } catch (const std::exception& e) {
        return std::unexpected(std::format("Failed to get query performance metrics: {}", e.what()));
    }
}

} // namespace dbservice
