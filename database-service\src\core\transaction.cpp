#include "database-service/core/transaction.hpp"
#include "database-service/utils/logger.hpp"
#include "database-service/metrics/database_metrics.hpp"
#include <format> // C++20 feature

namespace dbservice::core {

Transaction::Transaction(std::shared_ptr<Connection> connection)
    : connection_(connection),
      committed_(false),
      rolledBack_(false),
      startTime_(std::chrono::steady_clock::now()) {
}

Transaction::~Transaction() {
    if (!committed_ && !rolledBack_) {
        rollback();
    }
}

std::expected<void, std::string> Transaction::commit() {
    if (committed_ || rolledBack_) {
        return std::unexpected("Transaction already committed or rolled back");
    }

    try {
        int result = connection_->executeNonQuery("COMMIT", {});
        committed_ = (result >= 0);

        if (!committed_) {
            return std::unexpected("Failed to commit transaction");
        }

        // Record transaction metrics
        auto endTime = std::chrono::steady_clock::now();
        auto durationMs = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime_).count();

        auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
        dbMetrics.recordTransactionMetric(true, durationMs);

        return {}; // Success - void value
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during transaction commit: {}", e.what()));
        rollback();
        return std::unexpected(std::format("Exception during commit: {}", e.what()));
    }
}

std::expected<void, std::string> Transaction::rollback() {
    if (committed_ || rolledBack_) {
        return std::unexpected("Transaction already committed or rolled back");
    }

    try {
        int result = connection_->executeNonQuery("ROLLBACK", {});
        rolledBack_ = (result >= 0);

        if (!rolledBack_) {
            return std::unexpected("Failed to rollback transaction");
        }

        // Record transaction metrics
        auto endTime = std::chrono::steady_clock::now();
        auto durationMs = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime_).count();

        auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
        dbMetrics.recordTransactionMetric(false, durationMs);

        return {}; // Success - void value
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during transaction rollback: {}", e.what()));
        return std::unexpected(std::format("Exception during rollback: {}", e.what()));
    }
}

std::shared_ptr<Connection> Transaction::getConnection() const {
    return connection_;
}

} // namespace dbservice::core
