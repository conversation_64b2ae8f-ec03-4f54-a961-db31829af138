Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:22:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   22 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[ 16%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 22%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
[ 27%] Building CXX object CMakeFiles/database-service.dir/src/core/transaction.cpp.o
[ 33%] Building CXX object CMakeFiles/database-service.dir/src/database_service.cpp.o
[ 38%] Building CXX object CMakeFiles/database-service.dir/src/main.cpp.o
[ 44%] Building CXX object CMakeFiles/database-service.dir/src/metrics/database_metrics.cpp.o
[ 50%] Building CXX object CMakeFiles/database-service.dir/src/metrics/metrics_collector.cpp.o
[ 55%] Building CXX object CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o
[ 61%] Building CXX object CMakeFiles/database-service.dir/src/security/credential_store.cpp.o
/home/<USER>/database-service-build/src/security/credential_store.cpp: In member function ‘bool dbservice::security::CredentialStore::initialize(const std::string&)’:
/home/<USER>/database-service-build/src/security/credential_store.cpp:41:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   41 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/credential_store.cpp:6:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:42:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   42 |     SHA256_Update(&sha256, encryptionKey.c_str(), encryptionKey.length());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:43:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   43 |     SHA256_Final(key, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
[ 66%] Building CXX object CMakeFiles/database-service.dir/src/security/jwt.cpp.o
[ 72%] Building CXX object CMakeFiles/database-service.dir/src/security/security_manager.cpp.o
/home/<USER>/database-service-build/src/security/security_manager.cpp: In function ‘std::string dbservice::security::sha256(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:30:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   30 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/security_manager.cpp:13:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:31:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   31 |     SHA256_Update(&sha256, input.c_str(), input.size());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:32:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   32 |     SHA256_Final(hash, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In member function ‘std::expected<void, dbservice::security::SecurityError> dbservice::security::SecurityManager::deleteUser(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:561:51: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘getConnection’
  561 |             auto permissionsResult = transaction->getConnection()->executeNonQuery(permissionsQuery, {username});
      |                                                   ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:566:52: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  566 |                 auto rollbackResult = transaction->rollback();
      |                                                    ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:576:52: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  576 |                 auto rollbackResult = transaction->rollback();
      |                                                    ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:585:46: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘getConnection’
  585 |             auto tokensResult = transaction->getConnection()->executeNonQuery(tokensQuery, {username});
      |                                              ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:590:52: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  590 |                 auto rollbackResult = transaction->rollback();
      |                                                    ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:600:52: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  600 |                 auto rollbackResult = transaction->rollback();
      |                                                    ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:609:44: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘getConnection’
  609 |             auto userResult = transaction->getConnection()->executeNonQuery(userQuery, {username});
      |                                            ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:614:52: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  614 |                 auto rollbackResult = transaction->rollback();
      |                                                    ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:624:52: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  624 |                 auto rollbackResult = transaction->rollback();
      |                                                    ^~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:632:46: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘commit’
  632 |             auto commitResult = transaction->commit();
      |                                              ^~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:644:48: error: ‘class std::shared_ptr<dbservice::core::Transaction>’ has no member named ‘rollback’
  644 |             auto rollbackResult = transaction->rollback();
      |                                                ^~~~~~~~
[ 77%] Building CXX object CMakeFiles/database-service.dir/src/utils/cache.cpp.o
make[2]: *** [CMakeFiles/database-service.dir/build.make:247: CMakeFiles/database-service.dir/src/security/security_manager.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
