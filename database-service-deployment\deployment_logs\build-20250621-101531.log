Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  4%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
[  9%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:92:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   92 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[ 13%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 18%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<std::shared_ptr<dbservice::core::Connection>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::getConnection(std::chrono::milliseconds)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:187:31: warning: unused parameter ‘timeout’ [-Wunused-parameter]
  187 |     std::chrono::milliseconds timeout) {
      |     ~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<nlohmann::json_abi_v3_11_3::basic_json<>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::executeCachedQuery(const std::string&, const std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > >&, std::chrono::milliseconds)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:328:31: warning: unused parameter ‘ttl’ [-Wunused-parameter]
  328 |     std::chrono::milliseconds ttl) {
      |     ~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<std::shared_ptr<dbservice::core::Transaction>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::beginTransaction(dbservice::core::TransactionIsolationLevel, bool, bool)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:370:31: warning: unused parameter ‘isolationLevel’ [-Wunused-parameter]
  370 |     TransactionIsolationLevel isolationLevel,
      |     ~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:371:10: warning: unused parameter ‘readOnly’ [-Wunused-parameter]
  371 |     bool readOnly,
      |     ~~~~~^~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:372:10: warning: unused parameter ‘deferrable’ [-Wunused-parameter]
  372 |     bool deferrable) {
      |     ~~~~~^~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In static member function ‘static std::string dbservice::core::ConnectionManager::sslModeToString(dbservice::core::SSLMode)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:430:23: error: ‘VerifyCA’ is not a member of ‘dbservice::core::SSLMode’; did you mean ‘VerifyCa’?
  430 |         case SSLMode::VerifyCA:
      |                       ^~~~~~~~
      |                       VerifyCa
make[2]: *** [CMakeFiles/database-service.dir/build.make:121: CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
