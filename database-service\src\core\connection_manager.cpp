// C++ Standard Library
#include <iostream>
#include <string>
#include <vector>
#include <deque>
#include <memory>
#include <mutex>
#include <condition_variable>
#include <chrono>
#include <functional>
#include <expected>
#include <stdexcept>
#include "database-service/utils/filesystem_wrapper.hpp"
#include <format>

// Project-specific headers
#include "database-service/core/connection_manager.hpp"
#include "database-service/core/connection.hpp"
#include "database-service/core/transaction.hpp"
#include "database-service/utils/logger.hpp"
#include "database-service/metrics/database_metrics.hpp"

namespace dbservice::core {

ConnectionManager::ConnectionManager(const std::string& connectionString, size_t maxConnections, const SSLConfig& sslConfig)
    : connectionString_(connectionString),
      maxConnections_(maxConnections),
      sslConfig_(sslConfig),
      shutdown_(false),
      activeConnections_(0),
      waitingConnections_(0),
      lastMetricsUpdate_(std::chrono::steady_clock::now()) {

    utils::Logger::info(std::format("Initializing connection manager with {} max connections, SSL {}",
        maxConnections, sslConfig_.enabled ? "enabled" : "disabled"));

    // Initialize query cache
    queryCache_ = std::make_unique<utils::QueryCache>(1000, std::chrono::minutes(5)); // 1000 entries, 5 min TTL

    if (sslConfig_.enabled) {
        if (sslConfig_.mode != SSLMode::Disable) {
            if (sslConfig_.mode == SSLMode::VerifyCa || sslConfig_.mode == SSLMode::VerifyFull) {
                if (sslConfig_.caPath.empty()) {
                    utils::Logger::warning("SSL CA certificate path is empty but verification is enabled");
                                } else if (!std::filesystem::exists(std::filesystem::path(sslConfig_.caPath))) {
                    utils::Logger::warning(std::format("SSL CA certificate file not found: {}", sslConfig_.caPath));
                }
            }
                        if (!sslConfig_.certPath.empty() && !std::filesystem::exists(std::filesystem::path(sslConfig_.certPath))) {
                utils::Logger::warning(std::format("SSL client certificate file not found: {}", sslConfig_.certPath));
            }
                        if (!sslConfig_.keyPath.empty() && !std::filesystem::exists(std::filesystem::path(sslConfig_.keyPath))) {
                utils::Logger::warning(std::format("SSL client key file not found: {}", sslConfig_.keyPath));
            }
        }
    }

    size_t initialConnections = std::min(maxConnections, static_cast<size_t>(2));
    for (size_t i = 0; i < initialConnections; ++i) {
        auto expected_conn = createConnection();
        if (expected_conn) {
            connections_.push_back(std::move(*expected_conn));
        } else {
            utils::Logger::error(std::format("Initial connection creation failed: {}", expected_conn.error()));
        }
    }
    utils::Logger::info(std::format("Connection manager initialized with {} initial connections", connections_.size()));
}

ConnectionManager::ConnectionManager(const std::string& connectionString, size_t maxConnections, bool useSSL)
    : ConnectionManager(connectionString, maxConnections, SSLConfig{useSSL, "/etc/letsencrypt/live/chcit.org/fullchain.pem", "/etc/letsencrypt/live/chcit.org/privkey.pem", "/etc/letsencrypt/live/chcit.org/chain.pem", "", SSLMode::VerifyFull, false}) {}

ConnectionManager::~ConnectionManager() {
    shutdown();
}

std::expected<std::shared_ptr<Connection>, std::string> ConnectionManager::getConnection() {
    std::unique_lock<std::mutex> lock(mutex_);
    waitingConnections_++;

    if (!cv_.wait_for(lock, connectionTimeout_, [this] { return !connections_.empty() || activeConnections_ < maxConnections_ || shutdown_; })) {
        waitingConnections_--;
        if (shutdown_) {
            return std::unexpected("Connection manager is shutting down.");
        }
        return std::unexpected("Timeout waiting for an available connection.");
    }

    waitingConnections_--;

    if (shutdown_) {
        return std::unexpected("Connection manager is shutting down.");
    }

    if (!connections_.empty()) {
        std::shared_ptr<Connection> conn = std::move(connections_.front());
        connections_.pop_front();
        activeConnections_++;
        updateMetrics();
        return conn;
    }

    if (activeConnections_ < maxConnections_) {
        lock.unlock();
        auto new_conn_expected = createConnection();
        lock.lock();

        if (shutdown_) {
            if (new_conn_expected) {
                // Don't need to return it, its destructor will handle it.
            }
            return std::unexpected("Connection manager is shutting down.");
        }

        if (new_conn_expected) {
            activeConnections_++;
            updateMetrics();
            return std::move(*new_conn_expected);
        }
        return std::unexpected(new_conn_expected.error());
    }

    return std::unexpected("Failed to get a connection: pool is empty and max connections reached.");
}

void ConnectionManager::returnConnection(std::shared_ptr<Connection> connection) {
    if (!connection) return;

    std::unique_lock<std::mutex> lock(mutex_);
    if (shutdown_) {
        connection->close();
        return;
    }

    if (activeConnections_ > 0) {
        activeConnections_--;
    }

    if (connection->isOpen()) {
        connections_.push_back(std::move(connection));
    } else {
        // Connection is not open, don't add it back to the pool.
        // Optionally, you can try to create a new one to maintain pool size.
    }

    updateMetrics();
    lock.unlock();
    cv_.notify_one();
}

std::expected<std::vector<std::vector<std::string>>, std::string> ConnectionManager::executeQuery(const std::string& query, const std::vector<std::string>& params) {
    auto expectedConnection = getConnection();
    if (!expectedConnection) {
        return std::unexpected(expectedConnection.error());
    }
    std::shared_ptr<Connection> connection = std::move(*expectedConnection);

    try {
        auto result = connection->executeQuery(query, params);
        returnConnection(std::move(connection));
        return result;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception in executeQuery: {}", e.what()));
        // The connection might be in a bad state, so we don't return it to the pool.
        return std::unexpected(std::format("Exception during query execution: {}", e.what()));
    }
}

std::expected<void, std::string> ConnectionManager::executeQueryWithCallback(const std::string& query, std::function<void(std::span<const std::string>)> callback, const std::vector<std::string>& params) {
    auto expectedConnection = getConnection();
    if (!expectedConnection) {
        return std::unexpected(expectedConnection.error());
    }
    std::shared_ptr<Connection> connection = std::move(*expectedConnection);

    try {
        connection->executeQueryWithCallback(query, callback, params);
        returnConnection(std::move(connection));
        return {};
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception in executeQueryWithCallback: {}", e.what()));
        return std::unexpected(std::format("Exception during query execution with callback: {}", e.what()));
    }
}

std::expected<int, std::string> ConnectionManager::executeNonQuery(const std::string& statement, const std::vector<std::string>& params) {
    auto expectedConnection = getConnection();
    if (!expectedConnection) {
        return std::unexpected(expectedConnection.error());
    }
    std::shared_ptr<Connection> connection = std::move(*expectedConnection);

    auto startTime = std::chrono::steady_clock::now();
    try {
        auto result = connection->executeNonQuery(statement, params);
        auto endTime = std::chrono::steady_clock::now();
        auto executionTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();
        metrics::DatabaseMetrics::getInstance().recordQueryMetric("NON_QUERY", executionTimeMs, true);
        returnConnection(std::move(connection));
        return result;
    } catch (const std::exception& e) {
        auto endTime = std::chrono::steady_clock::now();
        auto executionTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();
        metrics::DatabaseMetrics::getInstance().recordQueryMetric("NON_QUERY", executionTimeMs, false);
        utils::Logger::error(std::format("Exception during non-query execution: {}", e.what()));
        return std::unexpected(std::format("Exception during non-query execution: {}", e.what()));
    }
}

std::expected<std::shared_ptr<Transaction>, std::string> ConnectionManager::beginTransaction() {
    auto expectedConnection = getConnection();
    if (!expectedConnection) {
        return std::unexpected(expectedConnection.error());
    }
    std::shared_ptr<Connection> connection = std::move(*expectedConnection);

    try {
        auto transaction = connection->beginTransaction();
        if (!transaction) {
            returnConnection(std::move(connection));
            return std::unexpected("Failed to begin transaction.");
        }
        return transaction;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during transaction start: {}", e.what()));
        returnConnection(std::move(connection));
        return std::unexpected(std::format("Exception during transaction start: {}", e.what()));
    }
}

void ConnectionManager::shutdown() {
    std::unique_lock<std::mutex> lock(mutex_);
    if (shutdown_) {
        return;
    }
    shutdown_ = true;
    lock.unlock();
    cv_.notify_all();

    std::unique_lock<std::mutex> final_lock(mutex_);
    for (auto& conn : connections_) {
        conn->close();
    }
    connections_.clear();
}

std::expected<std::shared_ptr<Connection>, std::string> ConnectionManager::createConnection() {
    try {
        auto connection = std::make_shared<Connection>(buildConnectionString(), sslConfig_.enabled);
        if (connection && connection->isOpen()) {
            return connection;
        }
        return std::unexpected("Failed to create or open database connection.");
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception in createConnection: {}", e.what()));
        return std::unexpected(std::format("Exception in createConnection: {}", e.what()));
    }
}

std::string ConnectionManager::buildConnectionString() const {
    std::string connStr = connectionString_;
    if (sslConfig_.enabled) {
        connStr += " sslmode=" + sslModeToString(sslConfig_.mode);
        if (!sslConfig_.certPath.empty()) connStr += " sslcert=" + sslConfig_.certPath;
        if (!sslConfig_.keyPath.empty()) connStr += " sslkey=" + sslConfig_.keyPath;
        if (!sslConfig_.caPath.empty()) connStr += " sslrootcert=" + sslConfig_.caPath;
        if (!sslConfig_.crlPath.empty()) connStr += " sslcrl=" + sslConfig_.crlPath;
        if (sslConfig_.rejectExpired) connStr += " sslrejectexpired=1";
    }
    return connStr;
}

std::string ConnectionManager::sslModeToString(SSLMode mode) {
    switch (mode) {
        case SSLMode::Disable: return "disable";
        case SSLMode::Allow: return "allow";
        case SSLMode::Prefer: return "prefer";
        case SSLMode::Require: return "require";
        case SSLMode::VerifyCa: return "verify-ca";
        case SSLMode::VerifyFull: return "verify-full";
        default: return "prefer";
    }
}

const SSLConfig& ConnectionManager::getSSLConfig() const {
    return sslConfig_;
}

size_t ConnectionManager::getActiveConnectionCount() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return activeConnections_;
}

size_t ConnectionManager::getIdleConnectionCount() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return connections_.size();
}

size_t ConnectionManager::getWaitingConnectionCount() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return waitingConnections_;
}

size_t ConnectionManager::getMaxConnections() const {
    return maxConnections_;
}

void ConnectionManager::updateMetrics() {
    auto now = std::chrono::steady_clock::now();
    if (std::chrono::duration_cast<std::chrono::seconds>(now - lastMetricsUpdate_).count() >= 1) {
        lastMetricsUpdate_ = now;
        metrics::DatabaseMetrics::getInstance().recordConnectionPoolMetrics(
            activeConnections_,
            connections_.size(),
            waitingConnections_,
            maxConnections_
        );
    }
}

std::expected<std::vector<std::string>, std::string> ConnectionManager::listDatabases() {
    // TODO: Implement actual database listing logic
    // This typically requires querying system tables (e.g., pg_database in PostgreSQL)
    // Ensure the connected user has permissions to list databases.
    // For now, returning a 'Not Implemented' error.
    utils::Logger::warn("ConnectionManager::listDatabases() is not fully implemented.");
    return std::unexpected("List databases feature is not implemented yet.");
}

std::expected<void, std::string> ConnectionManager::createDatabase(const std::string& dbName) {
    // TODO: Implement actual database creation logic
    // This involves executing a 'CREATE DATABASE dbName' statement.
    // Ensure proper sanitization of dbName to prevent SQL injection if it's dynamic.
    // The user executing this needs appropriate privileges.
    // For now, returning a 'Not Implemented' error.
    utils::Logger::warn(std::format("ConnectionManager::createDatabase({}) is not fully implemented.", dbName));
    return std::unexpected("Create database feature is not implemented yet.");
}

std::expected<void, std::string> ConnectionManager::dropDatabase(const std::string& dbName) {
    // TODO: Implement actual database dropping logic
    // This involves executing a 'DROP DATABASE dbName' statement.
    // Ensure proper sanitization of dbName.
    // This is a destructive operation and should be handled with care, perhaps with confirmation.
    // The user executing this needs appropriate privileges.
    // For now, returning a 'Not Implemented' error.
    utils::Logger::warn(std::format("ConnectionManager::dropDatabase({}) is not fully implemented.", dbName));
    return std::unexpected("Drop database feature is not implemented yet.");
}

// --- ManagedConnection Implementation ---

ManagedConnection::ManagedConnection(ConnectionManager& manager)
    : manager_(&manager), connection_expected_(manager.getConnection()), moved_from_(false) {}

ManagedConnection::~ManagedConnection() {
    if (!moved_from_ && manager_ && connection_expected_) {
        manager_->returnConnection(std::move(*connection_expected_));
    }
}

ManagedConnection::ManagedConnection(ManagedConnection&& other) noexcept
    : manager_(other.manager_), connection_expected_(std::move(other.connection_expected_)), moved_from_(false) {
    other.moved_from_ = true;
}

ManagedConnection& ManagedConnection::operator=(ManagedConnection&& other) noexcept {
    if (this != &other) {
        if (!moved_from_ && manager_ && connection_expected_) {
            manager_->returnConnection(std::move(*connection_expected_));
        }
        manager_ = other.manager_;
        connection_expected_ = std::move(other.connection_expected_);
        moved_from_ = false;
        other.moved_from_ = true;
    }
    return *this;
}

Connection* ManagedConnection::operator->() const {
    if (connection_expected_ && *connection_expected_) {
        return (*connection_expected_).get();
    }
    return nullptr;
}

std::shared_ptr<Connection> ManagedConnection::get() const {
    if (connection_expected_ && *connection_expected_) {
        return *connection_expected_;
    }
    return nullptr;
}

ManagedConnection::operator bool() const {
    return connection_expected_.has_value() && connection_expected_.value() && connection_expected_.value()->isOpen();
}

const std::string* ManagedConnection::getError() const {
    if (!connection_expected_.has_value()) {
        return &connection_expected_.error();
    }
    return nullptr;
}

std::expected<nlohmann::json, std::string> ConnectionManager::getMetrics() const {
    try {
        std::lock_guard<std::mutex> lock(mutex_);

        nlohmann::json metrics;
        metrics["max_connections"] = maxConnections_;
        metrics["active_connections"] = activeConnections_;
        metrics["idle_connections"] = connections_.size();
        metrics["waiting_connections"] = waitingConnections_;
        metrics["total_connections"] = activeConnections_ + connections_.size();

        // Connection utilization percentage
        double utilization = static_cast<double>(activeConnections_) / static_cast<double>(maxConnections_) * 100.0;
        metrics["utilization_percent"] = utilization;

        // SSL configuration status
        metrics["ssl_enabled"] = (sslConfig_.mode != SSLMode::Disable);
        metrics["ssl_mode"] = sslModeToString(sslConfig_.mode);

        // Add timestamp
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        metrics["timestamp"] = std::format("{:%Y-%m-%d %H:%M:%S}", std::chrono::system_clock::from_time_t(time_t));

        return metrics;
    } catch (const std::exception& e) {
        return std::unexpected(std::format("Failed to get connection pool metrics: {}", e.what()));
    }
}

std::expected<nlohmann::json, std::string> ConnectionManager::executeCachedQuery(
    const std::string& query,
    const std::vector<std::string>& params,
    std::optional<std::chrono::milliseconds> ttl) {

    if (!queryCache_) {
        return std::unexpected("Query cache not initialized");
    }

    // Try to get from cache first
    auto cachedResult = queryCache_->get(query, params);
    if (cachedResult) {
        return *cachedResult;
    }

    // Execute query if not in cache
    auto queryResult = executeQuery(query, params);
    if (!queryResult) {
        return std::unexpected(queryResult.error());
    }

    // Convert result to JSON and cache it
    try {
        nlohmann::json jsonResult = nlohmann::json::array();
        for (const auto& row : *queryResult) {
            nlohmann::json jsonRow = nlohmann::json::array();
            for (const auto& cell : row) {
                jsonRow.push_back(cell);
            }
            jsonResult.push_back(jsonRow);
        }

        // Cache the result
        queryCache_->put(query, params, jsonResult, ttl);

        return jsonResult;
    } catch (const std::exception& e) {
        return std::unexpected(std::format("Failed to convert query result to JSON: {}", e.what()));
    }
}

void ConnectionManager::clearCache() {
    if (queryCache_) {
        queryCache_->clear();
    }
}

nlohmann::json ConnectionManager::getCacheStats() const {
    if (queryCache_) {
        return queryCache_->getStats();
    }
    return nlohmann::json{{"error", "Query cache not initialized"}};
}

} // namespace dbservice::core
