Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  5%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘std::expected<void, dbservice::api::ApiError> dbservice::api::ApiServer::start()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:104:24: error: aggregate ‘dbservice::api::sockaddr_in address’ has incomplete type and cannot be defined
  104 |     struct sockaddr_in address;
      |                        ^~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:22:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   22 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::run()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:409:28: error: aggregate ‘dbservice::api::sockaddr_in clientAddress’ has incomplete type and cannot be defined
  409 |         struct sockaddr_in clientAddress;
      |                            ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:423:54: error: ‘clientAddress’ is not captured
  423 |                 this->handleConnection(clientSocket, clientAddress);
      |                                                      ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:422:68: note: the lambda has no capture-default
  422 |             threadPool_->enqueue([this, clientSocket, clientAddress]() {
      |                                                                    ^
/home/<USER>/database-service-build/src/api/api_server.cpp:409:28: note: ‘<typeprefixerror>clientAddress’ declared here
  409 |         struct sockaddr_in clientAddress;
      |                            ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: At global scope:
/home/<USER>/database-service-build/src/api/api_server.cpp:432:71: error: ‘clientAddress’ has incomplete type
  432 | void ApiServer::handleConnection(int clientSocket, struct sockaddr_in clientAddress) {
      |                                                    ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~
In file included from /home/<USER>/database-service-build/src/api/api_server.cpp:1:
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:162:52: note: forward declaration of ‘struct dbservice::api::sockaddr_in’
  162 |     void handleConnection(int clientSocket, struct sockaddr_in clientAddress);
      |                                                    ^~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:1386:44: error: ‘parseQueryString’ is not a member of ‘dbservice::utils’
 1386 |                 auto query_params = utils::parseQueryString(request.queryString);
      |                                            ^~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1386:69: error: ‘const struct dbservice::api::ParsedRequest’ has no member named ‘queryString’
 1386 |                 auto query_params = utils::parseQueryString(request.queryString);
      |                                                                     ^~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:1505:44: error: ‘parseQueryString’ is not a member of ‘dbservice::utils’
 1505 |                 auto query_params = utils::parseQueryString(request.queryString);
      |                                            ^~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1505:69: error: ‘const struct dbservice::api::ParsedRequest’ has no member named ‘queryString’
 1505 |                 auto query_params = utils::parseQueryString(request.queryString);
      |                                                                     ^~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:1651:44: error: ‘warn’ is not a member of ‘dbservice::utils::Logger’
 1651 |                             utils::Logger::warn("/api/query: a parameter was not a string, attempting dump.");
      |                                            ^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1663:123: error: ‘class std::__cxx11::basic_string<char>’ has no member named ‘message’
 1663 |                     utils::Logger::error(std::format("Database query error in /api/query: {}", db_result_expected.error().message));
      |                                                                                                                           ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1667:64: error: ‘class std::__cxx11::basic_string<char>’ has no member named ‘message’
 1667 |                         {"message", db_result_expected.error().message}
      |                                                                ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1668:22: error: no matching function for call to ‘nlohmann::json_abi_v3_11_3::basic_json<>::basic_json(<brace-enclosed initializer list>)’
 1668 |                     }).dump();
      |                      ^
In file included from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:9:
/usr/include/nlohmann/json.hpp:1141:5: note: candidate: ‘template<class JsonRef, typename std::enable_if<nlohmann::json_abi_v3_11_3::detail::conjunction<nlohmann::json_abi_v3_11_3::detail::is_json_ref<JsonRef>, std::is_same<typename JsonRef::value_type, nlohmann::json_abi_v3_11_3::basic_json<std::map, std::vector, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, bool, long int, long unsigned int, double, std::allocator, nlohmann::json_abi_v3_11_3::adl_serializer, std::vector<unsigned char, std::allocator<unsigned char> >, void> > >::value, int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const JsonRef&) [with typename std::enable_if<nlohmann::json_abi_v3_11_3::detail::conjunction<nlohmann::json_abi_v3_11_3::detail::is_json_ref<JsonRef>, std::is_same<typename JsonRef::value_type, nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value, int>::type <anonymous> = JsonRef; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1141 |     basic_json(const JsonRef& ref) : basic_json(ref.moved_or_copied()) {}
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1141:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:1668:22: note:   couldn’t deduce template parameter ‘JsonRef’
 1668 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1032:5: note: candidate: ‘template<class InputIT, typename std::enable_if<(std::is_same<IterImpl, nlohmann::json_abi_v3_11_3::detail::iter_impl<nlohmann::json_abi_v3_11_3::basic_json<> > >::value || std::is_same<IterImpl, nlohmann::json_abi_v3_11_3::detail::iter_impl<const nlohmann::json_abi_v3_11_3::basic_json<> > >::value), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(InputIT, InputIT) [with typename std::enable_if<(std::is_same<InputIT, nlohmann::json_abi_v3_11_3::detail::iter_impl<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value || std::is_same<InputIT, nlohmann::json_abi_v3_11_3::detail::iter_impl<const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value), int>::type <anonymous> = InputIT; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1032 |     basic_json(InputIT first, InputIT last)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1032:5: note:   candidate expects 2 arguments, 1 provided
/usr/include/nlohmann/json.hpp:849:5: note: candidate: ‘template<class BasicJsonType, typename std::enable_if<(nlohmann::json_abi_v3_11_3::detail::is_basic_json<BasicJsonType>::value && (! std::is_same<nlohmann::json_abi_v3_11_3::basic_json<>, BasicJsonType>::value)), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const BasicJsonType&) [with typename std::enable_if<(nlohmann::json_abi_v3_11_3::detail::is_basic_json<BasicJsonType>::value && (! std::is_same<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>, BasicJsonType>::value)), int>::type <anonymous> = BasicJsonType; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
  849 |     basic_json(const BasicJsonType& val)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:849:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:1668:22: note:   couldn’t deduce template parameter ‘BasicJsonType’
 1668 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:835:5: note: candidate: ‘template<class CompatibleType, class U, typename std::enable_if<((! nlohmann::json_abi_v3_11_3::detail::is_basic_json<T>::value) && nlohmann::json_abi_v3_11_3::detail::is_compatible_type<nlohmann::json_abi_v3_11_3::basic_json<>, U>::value), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(CompatibleType&&) [with U = CompatibleType; typename std::enable_if<((! nlohmann::json_abi_v3_11_3::detail::is_basic_json<U>::value) && nlohmann::json_abi_v3_11_3::detail::is_compatible_type<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>, U>::value), int>::type <anonymous> = U; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
  835 |     basic_json(CompatibleType && val) noexcept(noexcept( // NOLINT(bugprone-forwarding-reference-overload,bugprone-exception-escape)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:835:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:1668:22: note:   couldn’t deduce template parameter ‘CompatibleType’
 1668 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1214:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1214 |     basic_json(basic_json&& other) noexcept
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1214:29: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>&&’
 1214 |     basic_json(basic_json&& other) noexcept
      |                ~~~~~~~~~~~~~^~~~~
/usr/include/nlohmann/json.hpp:1145:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1145 |     basic_json(const basic_json& other)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1145:34: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘const nlohmann::json_abi_v3_11_3::basic_json<>&’
 1145 |     basic_json(const basic_json& other)
      |                ~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/nlohmann/json.hpp:1020:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(size_type, const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; size_type = long unsigned int]’
 1020 |     basic_json(size_type cnt, const basic_json& val):
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1020:5: note:   candidate expects 2 arguments, 1 provided
/usr/include/nlohmann/json.hpp:902:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(initializer_list_t, bool, value_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; initializer_list_t = std::initializer_list<nlohmann::json_abi_v3_11_3::detail::json_ref<nlohmann::json_abi_v3_11_3::basic_json<> > >; value_t = nlohmann::json_abi_v3_11_3::detail::value_t]’
  902 |     basic_json(initializer_list_t init,
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:902:35: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>::initializer_list_t’ {aka ‘std::initializer_list<nlohmann::json_abi_v3_11_3::detail::json_ref<nlohmann::json_abi_v3_11_3::basic_json<> > >’}
  902 |     basic_json(initializer_list_t init,
      |                ~~~~~~~~~~~~~~~~~~~^~~~
/usr/include/nlohmann/json.hpp:823:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(std::nullptr_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; std::nullptr_t = std::nullptr_t]’
  823 |     basic_json(std::nullptr_t = nullptr) noexcept // NOLINT(bugprone-exception-escape)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:823:16: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘std::nullptr_t’
  823 |     basic_json(std::nullptr_t = nullptr) noexcept // NOLINT(bugprone-exception-escape)
      |                ^~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/nlohmann/json.hpp:815:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(value_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; value_t = nlohmann::json_abi_v3_11_3::detail::value_t]’
  815 |     basic_json(const value_t v)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:815:30: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>::value_t’ {aka ‘nlohmann::json_abi_v3_11_3::detail::value_t’}
  815 |     basic_json(const value_t v)
      |                ~~~~~~~~~~~~~~^
/home/<USER>/database-service-build/src/api/api_server.cpp:1670:109: error: ‘class std::__cxx11::basic_string<char>’ has no member named ‘message’
 1670 |                     return std::unexpected("Database query execution failed: " + db_result_expected.error().message);
      |                                                                                                             ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:1799:44: error: ‘warn’ is not a member of ‘dbservice::utils::Logger’
 1799 |                             utils::Logger::warn("/api/execute: a parameter was not a string, attempting dump.");
      |                                            ^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1811:129: error: ‘class std::__cxx11::basic_string<char>’ has no member named ‘message’
 1811 |                     utils::Logger::error(std::format("Database non-query error in /api/execute: {}", db_result_expected.error().message));
      |                                                                                                                                 ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1815:64: error: ‘class std::__cxx11::basic_string<char>’ has no member named ‘message’
 1815 |                         {"message", db_result_expected.error().message}
      |                                                                ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1816:22: error: no matching function for call to ‘nlohmann::json_abi_v3_11_3::basic_json<>::basic_json(<brace-enclosed initializer list>)’
 1816 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1141:5: note: candidate: ‘template<class JsonRef, typename std::enable_if<nlohmann::json_abi_v3_11_3::detail::conjunction<nlohmann::json_abi_v3_11_3::detail::is_json_ref<JsonRef>, std::is_same<typename JsonRef::value_type, nlohmann::json_abi_v3_11_3::basic_json<std::map, std::vector, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, bool, long int, long unsigned int, double, std::allocator, nlohmann::json_abi_v3_11_3::adl_serializer, std::vector<unsigned char, std::allocator<unsigned char> >, void> > >::value, int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const JsonRef&) [with typename std::enable_if<nlohmann::json_abi_v3_11_3::detail::conjunction<nlohmann::json_abi_v3_11_3::detail::is_json_ref<JsonRef>, std::is_same<typename JsonRef::value_type, nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value, int>::type <anonymous> = JsonRef; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1141 |     basic_json(const JsonRef& ref) : basic_json(ref.moved_or_copied()) {}
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1141:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:1816:22: note:   couldn’t deduce template parameter ‘JsonRef’
 1816 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1032:5: note: candidate: ‘template<class InputIT, typename std::enable_if<(std::is_same<IterImpl, nlohmann::json_abi_v3_11_3::detail::iter_impl<nlohmann::json_abi_v3_11_3::basic_json<> > >::value || std::is_same<IterImpl, nlohmann::json_abi_v3_11_3::detail::iter_impl<const nlohmann::json_abi_v3_11_3::basic_json<> > >::value), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(InputIT, InputIT) [with typename std::enable_if<(std::is_same<InputIT, nlohmann::json_abi_v3_11_3::detail::iter_impl<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value || std::is_same<InputIT, nlohmann::json_abi_v3_11_3::detail::iter_impl<const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass> > >::value), int>::type <anonymous> = InputIT; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1032 |     basic_json(InputIT first, InputIT last)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1032:5: note:   candidate expects 2 arguments, 1 provided
/usr/include/nlohmann/json.hpp:849:5: note: candidate: ‘template<class BasicJsonType, typename std::enable_if<(nlohmann::json_abi_v3_11_3::detail::is_basic_json<BasicJsonType>::value && (! std::is_same<nlohmann::json_abi_v3_11_3::basic_json<>, BasicJsonType>::value)), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const BasicJsonType&) [with typename std::enable_if<(nlohmann::json_abi_v3_11_3::detail::is_basic_json<BasicJsonType>::value && (! std::is_same<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>, BasicJsonType>::value)), int>::type <anonymous> = BasicJsonType; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
  849 |     basic_json(const BasicJsonType& val)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:849:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:1816:22: note:   couldn’t deduce template parameter ‘BasicJsonType’
 1816 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:835:5: note: candidate: ‘template<class CompatibleType, class U, typename std::enable_if<((! nlohmann::json_abi_v3_11_3::detail::is_basic_json<T>::value) && nlohmann::json_abi_v3_11_3::detail::is_compatible_type<nlohmann::json_abi_v3_11_3::basic_json<>, U>::value), int>::type <anonymous> > nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(CompatibleType&&) [with U = CompatibleType; typename std::enable_if<((! nlohmann::json_abi_v3_11_3::detail::is_basic_json<U>::value) && nlohmann::json_abi_v3_11_3::detail::is_compatible_type<nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>, U>::value), int>::type <anonymous> = U; ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
  835 |     basic_json(CompatibleType && val) noexcept(noexcept( // NOLINT(bugprone-forwarding-reference-overload,bugprone-exception-escape)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:835:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:1816:22: note:   couldn’t deduce template parameter ‘CompatibleType’
 1816 |                     }).dump();
      |                      ^
/usr/include/nlohmann/json.hpp:1214:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1214 |     basic_json(basic_json&& other) noexcept
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1214:29: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>&&’
 1214 |     basic_json(basic_json&& other) noexcept
      |                ~~~~~~~~~~~~~^~~~~
/usr/include/nlohmann/json.hpp:1145:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void]’
 1145 |     basic_json(const basic_json& other)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1145:34: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘const nlohmann::json_abi_v3_11_3::basic_json<>&’
 1145 |     basic_json(const basic_json& other)
      |                ~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/nlohmann/json.hpp:1020:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(size_type, const nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>&) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; size_type = long unsigned int]’
 1020 |     basic_json(size_type cnt, const basic_json& val):
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:1020:5: note:   candidate expects 2 arguments, 1 provided
/usr/include/nlohmann/json.hpp:902:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(initializer_list_t, bool, value_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; initializer_list_t = std::initializer_list<nlohmann::json_abi_v3_11_3::detail::json_ref<nlohmann::json_abi_v3_11_3::basic_json<> > >; value_t = nlohmann::json_abi_v3_11_3::detail::value_t]’
  902 |     basic_json(initializer_list_t init,
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:902:35: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>::initializer_list_t’ {aka ‘std::initializer_list<nlohmann::json_abi_v3_11_3::detail::json_ref<nlohmann::json_abi_v3_11_3::basic_json<> > >’}
  902 |     basic_json(initializer_list_t init,
      |                ~~~~~~~~~~~~~~~~~~~^~~~
/usr/include/nlohmann/json.hpp:823:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(std::nullptr_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; std::nullptr_t = std::nullptr_t]’
  823 |     basic_json(std::nullptr_t = nullptr) noexcept // NOLINT(bugprone-exception-escape)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:823:16: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘std::nullptr_t’
  823 |     basic_json(std::nullptr_t = nullptr) noexcept // NOLINT(bugprone-exception-escape)
      |                ^~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/nlohmann/json.hpp:815:5: note: candidate: ‘nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::basic_json(value_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; value_t = nlohmann::json_abi_v3_11_3::detail::value_t]’
  815 |     basic_json(const value_t v)
      |     ^~~~~~~~~~
/usr/include/nlohmann/json.hpp:815:30: note:   no known conversion for argument 1 from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::basic_json<>::value_t’ {aka ‘nlohmann::json_abi_v3_11_3::detail::value_t’}
  815 |     basic_json(const value_t v)
      |                ~~~~~~~~~~~~~~^
/home/<USER>/database-service-build/src/api/api_server.cpp:1818:113: error: ‘class std::__cxx11::basic_string<char>’ has no member named ‘message’
 1818 |                     return std::unexpected("Database non-query execution failed: " + db_result_expected.error().message);
      |                                                                                                                 ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: At global scope:
/home/<USER>/database-service-build/src/api/api_server.cpp:2093:26: error: ‘ParsedRequest’ is not a member of ‘dbservice::api::ApiServer’
 2093 | std::expected<ApiServer::ParsedRequest, std::string> ApiServer::parseRequest(const std::string& request) {
      |                          ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:2093:52: error: template argument 1 is invalid
 2093 | std::expected<ApiServer::ParsedRequest, std::string> ApiServer::parseRequest(const std::string& request) {
      |                                                    ^
/home/<USER>/database-service-build/src/api/api_server.cpp:2093:54: error: no declaration matches ‘int dbservice::api::ApiServer::parseRequest(const std::string&)’
 2093 | std::expected<ApiServer::ParsedRequest, std::string> ApiServer::parseRequest(const std::string& request) {
      |                                                      ^~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:181:47: note: candidate is: ‘std::expected<dbservice::api::ParsedRequest, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::parseRequest(const std::string&)’
  181 |     std::expected<ParsedRequest, std::string> parseRequest(const std::string& request);
      |                                               ^~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:62:7: note: ‘class dbservice::api::ApiServer’ defined here
   62 | class ApiServer {
      |       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:2134:31: error: ‘request’ was not declared in this scope
 2134 |     std::istringstream stream(request);
      |                               ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::parseQueryParams(std::string_view)’:
/home/<USER>/database-service-build/src/api/api_server.cpp:2180:39: error: no matching function for call to ‘std::__cxx11::basic_istringstream<char>::basic_istringstream(std::string_view&)’
 2180 |     std::istringstream iss(queryString);
      |                                       ^
In file included from /usr/include/c++/14/bits/quoted_string.h:38,
                 from /usr/include/c++/14/iomanip:50,
                 from /usr/include/c++/14/bits/fs_path.h:38,
                 from /usr/include/c++/14/filesystem:52,
                 from /usr/include/nlohmann/detail/meta/std_fs.hpp:22,
                 from /usr/include/nlohmann/detail/conversions/from_json.hpp:27,
                 from /usr/include/nlohmann/adl_serializer.hpp:14,
                 from /usr/include/nlohmann/json.hpp:34:
/usr/include/c++/14/sstream:663:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  663 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:663:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:2180:39: note:   ‘std::string_view’ {aka ‘std::basic_string_view<char>’} is not derived from ‘const std::__cxx11::basic_string<char, std::char_traits<char>, _Alloc>’
 2180 |     std::istringstream iss(queryString);
      |                                       ^
/usr/include/c++/14/sstream:655:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, std::ios_base::openmode, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  655 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:655:9: note:   candidate expects 3 arguments, 1 provided
/usr/include/c++/14/sstream:649:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  649 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:649:9: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/sstream:643:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(__string_type&&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; __string_type = std::__cxx11::basic_string<char>; std::ios_base::openmode = std::ios_base::openmode]’
  643 |       basic_istringstream(__string_type&& __str,
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:643:43: note:   no known conversion for argument 1 from ‘std::string_view’ {aka ‘std::basic_string_view<char>’} to ‘std::__cxx11::basic_istringstream<char>::__string_type&&’ {aka ‘std::__cxx11::basic_string<char>&&’}
  643 |       basic_istringstream(__string_type&& __str,
      |                           ~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:638:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::ios_base::openmode, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; std::ios_base::openmode = std::ios_base::openmode; allocator_type = std::allocator<char>]’
  638 |       basic_istringstream(ios_base::openmode __mode, const allocator_type& __a)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:638:7: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/sstream:632:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>&&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  632 |       basic_istringstream(basic_istringstream&& __rhs)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:632:49: note:   no known conversion for argument 1 from ‘std::string_view’ {aka ‘std::basic_string_view<char>’} to ‘std::__cxx11::basic_istringstream<char>&&’
  632 |       basic_istringstream(basic_istringstream&& __rhs)
      |                           ~~~~~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:615:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const __string_type&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; __string_type = std::__cxx11::basic_string<char>; std::ios_base::openmode = std::ios_base::openmode]’
  615 |       basic_istringstream(const __string_type& __str,
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:615:48: note:   no known conversion for argument 1 from ‘std::string_view’ {aka ‘std::basic_string_view<char>’} to ‘const std::__cxx11::basic_istringstream<char>::__string_type&’ {aka ‘const std::__cxx11::basic_string<char>&’}
  615 |       basic_istringstream(const __string_type& __str,
      |                           ~~~~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:597:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; std::ios_base::openmode = std::ios_base::openmode]’
  597 |       basic_istringstream(ios_base::openmode __mode)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:597:46: note:   no known conversion for argument 1 from ‘std::string_view’ {aka ‘std::basic_string_view<char>’} to ‘std::ios_base::openmode’
  597 |       basic_istringstream(ios_base::openmode __mode)
      |                           ~~~~~~~~~~~~~~~~~~~^~~~~~
/usr/include/c++/14/sstream:580:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream() [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  580 |       basic_istringstream()
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:580:7: note:   candidate expects 0 arguments, 1 provided
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘std::string dbservice::api::ApiServer::urlDecode(std::string_view)’:
/home/<USER>/database-service-build/src/api/api_server.cpp:2250:58: error: no matching function for call to ‘std::__cxx11::basic_istringstream<char>::basic_istringstream(std::basic_string_view<char>)’
 2250 |             std::istringstream iss(input.substr(i + 1, 2));
      |                                                          ^
/usr/include/c++/14/sstream:663:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  663 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:663:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:2250:58: note:   ‘std::basic_string_view<char>’ is not derived from ‘const std::__cxx11::basic_string<char, std::char_traits<char>, _Alloc>’
 2250 |             std::istringstream iss(input.substr(i + 1, 2));
      |                                                          ^
/usr/include/c++/14/sstream:655:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, std::ios_base::openmode, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  655 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:655:9: note:   candidate expects 3 arguments, 1 provided
/usr/include/c++/14/sstream:649:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  649 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:649:9: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/sstream:643:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(__string_type&&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; __string_type = std::__cxx11::basic_string<char>; std::ios_base::openmode = std::ios_base::openmode]’
  643 |       basic_istringstream(__string_type&& __str,
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:643:43: note:   no known conversion for argument 1 from ‘std::basic_string_view<char>’ to ‘std::__cxx11::basic_istringstream<char>::__string_type&&’ {aka ‘std::__cxx11::basic_string<char>&&’}
  643 |       basic_istringstream(__string_type&& __str,
      |                           ~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:638:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::ios_base::openmode, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; std::ios_base::openmode = std::ios_base::openmode; allocator_type = std::allocator<char>]’
  638 |       basic_istringstream(ios_base::openmode __mode, const allocator_type& __a)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:638:7: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/sstream:632:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>&&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  632 |       basic_istringstream(basic_istringstream&& __rhs)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:632:49: note:   no known conversion for argument 1 from ‘std::basic_string_view<char>’ to ‘std::__cxx11::basic_istringstream<char>&&’
  632 |       basic_istringstream(basic_istringstream&& __rhs)
      |                           ~~~~~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:615:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const __string_type&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; __string_type = std::__cxx11::basic_string<char>; std::ios_base::openmode = std::ios_base::openmode]’
  615 |       basic_istringstream(const __string_type& __str,
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:615:48: note:   no known conversion for argument 1 from ‘std::basic_string_view<char>’ to ‘const std::__cxx11::basic_istringstream<char>::__string_type&’ {aka ‘const std::__cxx11::basic_string<char>&’}
  615 |       basic_istringstream(const __string_type& __str,
      |                           ~~~~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:597:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; std::ios_base::openmode = std::ios_base::openmode]’
  597 |       basic_istringstream(ios_base::openmode __mode)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:597:46: note:   no known conversion for argument 1 from ‘std::basic_string_view<char>’ to ‘std::ios_base::openmode’
  597 |       basic_istringstream(ios_base::openmode __mode)
      |                           ~~~~~~~~~~~~~~~~~~~^~~~~~
/usr/include/c++/14/sstream:580:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream() [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  580 |       basic_istringstream()
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:580:7: note:   candidate expects 0 arguments, 1 provided
[ 16%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
make[2]: *** [CMakeFiles/database-service.dir/build.make:79: CMakeFiles/database-service.dir/src/api/api_server.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
