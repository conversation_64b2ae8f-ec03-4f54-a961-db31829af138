CMake Error: The current CMakeCache.txt directory /home/<USER>/database-service-build/build/CMakeCache.txt is different than the directory d:/Augment/project-tracker/database-service/build where CMakeCache.txt was created. This may result in binaries being created in the wrong place. If you are not sure, reedit the CMakeCache.txt
CMake Error: The source "/home/<USER>/database-service-build/CMakeLists.txt" does not match the source "D:/Augment/project-tracker/database-service/CMakeLists.txt" used to generate cache.  Re-run cmake with a different source directory.
[0/1] Re-running CMake...
CMake Error: The current CMakeCache.txt directory /home/<USER>/database-service-build/build/CMakeCache.txt is different than the directory d:/Augment/project-tracker/database-service/build where CMakeCache.txt was created. This may result in binaries being created in the wrong place. If you are not sure, reedit the CMakeCache.txt
CMake Error: The source "/home/<USER>/database-service-build/CMakeLists.txt" does not match the source "D:/Augment/project-tracker/database-service/CMakeLists.txt" used to generate cache.  Re-run cmake with a different source directory.
FAILED: build.ninja /home/<USER>/database-service-build/build/cmake_install.cmake /home/<USER>/database-service-build/build/tests/cmake_install.cmake /home/<USER>/database-service-build/build/CTestTestfile.cmake /home/<USER>/database-service-build/build/tests/CTestTestfile.cmake 
/usr/local/bin/cmake --regenerate-during-build -S/home/<USER>/database-service-build -B/home/<USER>/database-service-build/build
ninja: error: rebuilding 'build.ninja': subcommand failed
