Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  5%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:22:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   22 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[ 16%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 22%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In constructor ‘dbservice::core::ConnectionManager::ConnectionManager(const std::string&, size_t, const dbservice::core::SSLConfig&)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:62:35: error: no matching function for call to ‘std::deque<std::shared_ptr<dbservice::core::Connection> >::push_back(std::remove_reference<dbservice::core::Connection&>::type)’
   62 |             connections_.push_back(std::move(*expected_conn));
      |             ~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~
In file included from /usr/include/c++/14/deque:66,
                 from /home/<USER>/database-service-build/src/core/connection_manager.cpp:5:
/usr/include/c++/14/bits/stl_deque.h:1538:7: note: candidate: ‘void std::deque<_Tp, _Alloc>::push_back(const value_type&) [with _Tp = std::shared_ptr<dbservice::core::Connection>; _Alloc = std::allocator<std::shared_ptr<dbservice::core::Connection> >; value_type = std::shared_ptr<dbservice::core::Connection>]’
 1538 |       push_back(const value_type& __x)
      |       ^~~~~~~~~
/usr/include/c++/14/bits/stl_deque.h:1538:35: note:   no known conversion for argument 1 from ‘std::remove_reference<dbservice::core::Connection&>::type’ {aka ‘dbservice::core::Connection’} to ‘const std::deque<std::shared_ptr<dbservice::core::Connection> >::value_type&’ {aka ‘const std::shared_ptr<dbservice::core::Connection>&’}
 1538 |       push_back(const value_type& __x)
      |                 ~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/stl_deque.h:1553:7: note: candidate: ‘void std::deque<_Tp, _Alloc>::push_back(value_type&&) [with _Tp = std::shared_ptr<dbservice::core::Connection>; _Alloc = std::allocator<std::shared_ptr<dbservice::core::Connection> >; value_type = std::shared_ptr<dbservice::core::Connection>]’
 1553 |       push_back(value_type&& __x)
      |       ^~~~~~~~~
/usr/include/c++/14/bits/stl_deque.h:1553:30: note:   no known conversion for argument 1 from ‘std::remove_reference<dbservice::core::Connection&>::type’ {aka ‘dbservice::core::Connection’} to ‘std::deque<std::shared_ptr<dbservice::core::Connection> >::value_type&&’ {aka ‘std::shared_ptr<dbservice::core::Connection>&&’}
 1553 |       push_back(value_type&& __x)
      |                 ~~~~~~~~~~~~~^~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:64:102: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘error’
   64 |             utils::Logger::error(std::format("Initial connection creation failed: {}", expected_conn.error()));
      |                                                                                                      ^~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In constructor ‘dbservice::core::ConnectionManager::ConnectionManager(const std::string&, size_t, bool)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:71:77: error: cannot convert ‘const char*’ to ‘dbservice::core::SSLMode’ in initialization
   71 |     : ConnectionManager(connectionString, maxConnections, SSLConfig{useSSL, "/etc/letsencrypt/live/chcit.org/fullchain.pem", "/etc/letsencrypt/live/chcit.org/privkey.pem", "/etc/letsencrypt/live/chcit.org/chain.pem", "", SSLMode::VerifyFull, false}) {}
      |                                                                             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                                                             |
      |                                                                             const char*
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<std::shared_ptr<dbservice::core::Connection>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::getConnection()’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:118:29: error: could not convert ‘std::move<dbservice::core::Connection&>((* &((std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>*)(& new_conn_expected))->std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::operator*()))’ from ‘std::remove_reference<dbservice::core::Connection&>::type’ {aka ‘dbservice::core::Connection’} to ‘std::expected<std::shared_ptr<dbservice::core::Connection>, std::__cxx11::basic_string<char> >’
  118 |             return std::move(*new_conn_expected);
      |                    ~~~~~~~~~^~~~~~~~~~~~~~~~~~~~
      |                             |
      |                             std::remove_reference<dbservice::core::Connection&>::type {aka dbservice::core::Connection}
/home/<USER>/database-service-build/src/core/connection_manager.cpp:120:50: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘error’
  120 |         return std::unexpected(new_conn_expected.error());
      |                                                  ^~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: At global scope:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:247:57: error: no declaration matches ‘std::expected<std::shared_ptr<dbservice::core::Connection>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::createConnection()’
  247 | std::expected<std::shared_ptr<Connection>, std::string> ConnectionManager::createConnection() {
      |                                                         ^~~~~~~~~~~~~~~~~
In file included from /home/<USER>/database-service-build/src/core/connection_manager.cpp:17:
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:213:33: note: candidate is: ‘std::shared_ptr<dbservice::core::Connection> dbservice::core::ConnectionManager::createConnection()’
  213 |     std::shared_ptr<Connection> createConnection();
      |                                 ^~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:52:7: note: ‘class dbservice::core::ConnectionManager’ defined here
   52 | class ConnectionManager {
      |       ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > >, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::listDatabases()’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:326:20: error: ‘warn’ is not a member of ‘dbservice::utils::Logger’
  326 |     utils::Logger::warn("ConnectionManager::listDatabases() is not fully implemented.");
      |                    ^~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<void, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::createDatabase(const std::string&)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:336:20: error: ‘warn’ is not a member of ‘dbservice::utils::Logger’
  336 |     utils::Logger::warn(std::format("ConnectionManager::createDatabase({}) is not fully implemented.", dbName));
      |                    ^~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<void, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::dropDatabase(const std::string&)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:347:20: error: ‘warn’ is not a member of ‘dbservice::utils::Logger’
  347 |     utils::Logger::warn(std::format("ConnectionManager::dropDatabase({}) is not fully implemented.", dbName));
      |                    ^~~~
make[2]: *** [CMakeFiles/database-service.dir/build.make:121: CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
