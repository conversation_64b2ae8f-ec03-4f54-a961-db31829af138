Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  9%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
[  9%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:91:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   91 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[ 13%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 18%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<std::shared_ptr<dbservice::core::Connection>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::getConnection(std::chrono::milliseconds)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:187:31: warning: unused parameter ‘timeout’ [-Wunused-parameter]
  187 |     std::chrono::milliseconds timeout) {
      |     ~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<nlohmann::json_abi_v3_11_3::basic_json<>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::executeCachedQuery(const std::string&, const std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > >&, std::chrono::milliseconds)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:328:31: warning: unused parameter ‘ttl’ [-Wunused-parameter]
  328 |     std::chrono::milliseconds ttl) {
      |     ~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<std::shared_ptr<dbservice::core::Transaction>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::beginTransaction(dbservice::core::TransactionIsolationLevel, bool, bool)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:370:31: warning: unused parameter ‘isolationLevel’ [-Wunused-parameter]
  370 |     TransactionIsolationLevel isolationLevel,
      |     ~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:371:10: warning: unused parameter ‘readOnly’ [-Wunused-parameter]
  371 |     bool readOnly,
      |     ~~~~~^~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:372:10: warning: unused parameter ‘deferrable’ [-Wunused-parameter]
  372 |     bool deferrable) {
      |     ~~~~~^~~~~~~~~~
[ 22%] Building CXX object CMakeFiles/database-service.dir/src/core/transaction.cpp.o
[ 27%] Building CXX object CMakeFiles/database-service.dir/src/database_service.cpp.o
[ 31%] Building CXX object CMakeFiles/database-service.dir/src/main.cpp.o
[ 36%] Building CXX object CMakeFiles/database-service.dir/src/metrics/database_metrics.cpp.o
[ 40%] Building CXX object CMakeFiles/database-service.dir/src/metrics/metrics_collector.cpp.o
[ 45%] Building CXX object CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o
[ 50%] Building CXX object CMakeFiles/database-service.dir/src/security/audit_logger.cpp.o
[ 54%] Building CXX object CMakeFiles/database-service.dir/src/security/credential_store.cpp.o
/home/<USER>/database-service-build/src/security/credential_store.cpp: In member function ‘bool dbservice::security::CredentialStore::initialize(const std::string&)’:
/home/<USER>/database-service-build/src/security/credential_store.cpp:46:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   46 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/credential_store.cpp:6:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:47:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   47 |     SHA256_Update(&sha256, encryptionKey.c_str(), encryptionKey.length());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:48:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   48 |     SHA256_Final(key, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
[ 59%] Building CXX object CMakeFiles/database-service.dir/src/security/jwt.cpp.o
[ 63%] Building CXX object CMakeFiles/database-service.dir/src/security/rate_limiter.cpp.o
[ 68%] Building CXX object CMakeFiles/database-service.dir/src/security/security_manager.cpp.o
/home/<USER>/database-service-build/src/security/rate_limiter.cpp: In member function ‘dbservice::security::RateLimitResult dbservice::security::RateLimiter::checkLimit(const std::string&, const std::string&, const std::string&)’:
/home/<USER>/database-service-build/src/security/rate_limiter.cpp:62:24: warning: unused parameter ‘userAgent’ [-Wunused-parameter]
   62 |     const std::string& userAgent) {
      |     ~~~~~~~~~~~~~~~~~~~^~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In function ‘std::string dbservice::security::sha256(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:30:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   30 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/security_manager.cpp:13:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:31:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   31 |     SHA256_Update(&sha256, input.c_str(), input.size());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:32:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   32 |     SHA256_Final(hash, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
[ 72%] Building CXX object CMakeFiles/database-service.dir/src/service/application_manager.cpp.o
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘bool dbservice::service::ApplicationManager::initialize()’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:43:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
   43 |         auto result = connection->query(checkTableQuery);
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:44:56: error: expected primary-expression before ‘bool’
   44 |         if (result.empty() || !result[0]["exists"].get<bool>()) {
      |                                                        ^~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:44:55: warning: logical not is only applied to the left hand side of comparison [-Wlogical-not-parentheses]
   44 |         if (result.empty() || !result[0]["exists"].get<bool>()) {
      |                                                       ^
/home/<USER>/database-service-build/src/service/application_manager.cpp:44:56: error: expected ‘)’ before ‘bool’
   44 |         if (result.empty() || !result[0]["exists"].get<bool>()) {
      |            ~                                           ^~~~
      |                                                        )
/home/<USER>/database-service-build/src/service/application_manager.cpp:58:30: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
   58 |         result = connection->query(checkTableQuery);
      |                              ^~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:59:56: error: expected primary-expression before ‘bool’
   59 |         if (result.empty() || !result[0]["exists"].get<bool>()) {
      |                                                        ^~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:59:55: warning: logical not is only applied to the left hand side of comparison [-Wlogical-not-parentheses]
   59 |         if (result.empty() || !result[0]["exists"].get<bool>()) {
      |                                                       ^
/home/<USER>/database-service-build/src/service/application_manager.cpp:59:56: error: expected ‘)’ before ‘bool’
   59 |         if (result.empty() || !result[0]["exists"].get<bool>()) {
      |            ~                                           ^~~~
      |                                                        )
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<int, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::registerApplication(const dbservice::service::ApplicationRequest&)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:121:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
  121 |         auto result = connection->query(insertQuery, params);
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:127:49: error: expected primary-expression before ‘int’
  127 |         int applicationId = result[0]["id"].get<int>();
      |                                                 ^~~
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<std::__cxx11::basic_string<char>, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::generateApiKey(int)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:151:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
  151 |         auto result = connection->query(checkQuery, {std::to_string(applicationId)});
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:167:21: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘execute’
  167 |         connection->execute(updateQuery, {newApiKey, std::to_string(applicationId)});
      |                     ^~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<int, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::validateApiKey(const std::string&)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:194:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
  194 |         auto result = connection->query(query, {apiKey});
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:200:36: error: expected primary-expression before ‘int’
  200 |         return result[0]["id"].get<int>();
      |                                    ^~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:200:36: error: expected ‘;’ before ‘int’
  200 |         return result[0]["id"].get<int>();
      |                                    ^~~
      |                                    ;
/home/<USER>/database-service-build/src/service/application_manager.cpp:200:39: error: expected unqualified-id before ‘>’ token
  200 |         return result[0]["id"].get<int>();
      |                                       ^
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘bool dbservice::service::ApplicationManager::applicationNameExists(const std::string&)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:248:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
  248 |         auto result = connection->query(query, {name});
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:250:58: error: expected primary-expression before ‘int’
  250 |         return !result.empty() && result[0]["count"].get<int>() > 0;
      |                                                          ^~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:250:58: error: expected ‘;’ before ‘int’
  250 |         return !result.empty() && result[0]["count"].get<int>() > 0;
      |                                                          ^~~
      |                                                          ;
/home/<USER>/database-service-build/src/service/application_manager.cpp:250:61: error: expected unqualified-id before ‘>’ token
  250 |         return !result.empty() && result[0]["count"].get<int>() > 0;
      |                                                             ^
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<dbservice::service::Application, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::getApplication(int)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:275:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
  275 |         auto result = connection->query(query, {std::to_string(applicationId)});
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<dbservice::service::Application, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::getApplicationByName(const std::string&)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:305:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
  305 |         auto result = connection->query(query, {name});
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<std::vector<dbservice::service::Application>, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::listApplications()’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:335:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
  335 |         auto result = connection->query(query);
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<void, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::updateApplicationMetadata(int, const nlohmann::json_abi_v3_11_3::json&)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:365:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
  365 |         auto result = connection->query(checkQuery, {std::to_string(applicationId)});
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:378:21: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘execute’
  378 |         connection->execute(updateQuery, {metadata.dump(), std::to_string(applicationId)});
      |                     ^~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<void, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::deactivateApplication(int)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:428:41: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘execute’
  428 |         auto rowsAffected = connection->execute(updateQuery, {std::to_string(applicationId)});
      |                                         ^~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<void, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::activateApplication(int)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:459:41: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘execute’
  459 |         auto rowsAffected = connection->execute(updateQuery, {std::to_string(applicationId)});
      |                                         ^~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<void, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::linkToDatabase(int, int, const std::string&)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:490:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
  490 |         auto result = connection->query(checkAppQuery, {std::to_string(applicationId)});
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:502:30: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
  502 |         result = connection->query(checkMappingQuery, {
      |                              ^~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:515:25: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘execute’
  515 |             connection->execute(updateQuery, {
      |                         ^~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:527:25: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘execute’
  527 |             connection->execute(insertQuery, {
      |                         ^~~~~~~
[ 77%] Building CXX object CMakeFiles/database-service.dir/src/service/database_instance_manager.cpp.o
make[2]: *** [CMakeFiles/database-service.dir/build.make:289: CMakeFiles/database-service.dir/src/service/application_manager.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp: In member function ‘bool dbservice::service::DatabaseInstanceManager::initialize()’:
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:51:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
   51 |         auto result = connection->query(checkTableQuery);
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:52:56: error: expected primary-expression before ‘bool’
   52 |         if (result.empty() || !result[0]["exists"].get<bool>()) {
      |                                                        ^~~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:52:55: warning: logical not is only applied to the left hand side of comparison [-Wlogical-not-parentheses]
   52 |         if (result.empty() || !result[0]["exists"].get<bool>()) {
      |                                                       ^
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:52:56: error: expected ‘)’ before ‘bool’
   52 |         if (result.empty() || !result[0]["exists"].get<bool>()) {
      |            ~                                           ^~~~
      |                                                        )
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp: In member function ‘std::expected<int, dbservice::service::DatabaseError> dbservice::service::DatabaseInstanceManager::registerInstance(const dbservice::service::DatabaseConfig&)’:
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:110:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
  110 |         auto result = connection->query(insertQuery, params);
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:116:46: error: expected primary-expression before ‘int’
  116 |         int instanceId = result[0]["id"].get<int>();
      |                                              ^~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:121:25: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘execute’
  121 |             connection->execute("DELETE FROM database_instances WHERE id = $1", {std::to_string(instanceId)});
      |                         ^~~~~~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp: In member function ‘std::expected<dbservice::service::DatabaseInstance, dbservice::service::DatabaseError> dbservice::service::DatabaseInstanceManager::getInstance(int)’:
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:152:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
  152 |         auto result = connection->query(query, {std::to_string(instanceId)});
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp: In member function ‘std::expected<dbservice::service::DatabaseInstance, dbservice::service::DatabaseError> dbservice::service::DatabaseInstanceManager::getInstanceByName(const std::string&)’:
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:182:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
  182 |         auto result = connection->query(query, {name});
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp: In member function ‘std::expected<std::vector<dbservice::service::DatabaseInstance>, dbservice::service::DatabaseError> dbservice::service::DatabaseInstanceManager::listInstances()’:
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:212:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
  212 |         auto result = connection->query(query);
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp: In member function ‘bool dbservice::service::DatabaseInstanceManager::instanceNameExists(const std::string&)’:
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:281:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
  281 |         auto result = connection->query(query, {name});
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:283:58: error: expected primary-expression before ‘int’
  283 |         return !result.empty() && result[0]["count"].get<int>() > 0;
      |                                                          ^~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:283:58: error: expected ‘;’ before ‘int’
  283 |         return !result.empty() && result[0]["count"].get<int>() > 0;
      |                                                          ^~~
      |                                                          ;
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:283:61: error: expected unqualified-id before ‘>’ token
  283 |         return !result.empty() && result[0]["count"].get<int>() > 0;
      |                                                             ^
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp: In member function ‘std::expected<std::shared_ptr<dbservice::core::Connection>, dbservice::service::DatabaseError> dbservice::service::DatabaseInstanceManager::getConnection(int, int)’:
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:365:26: error: ‘using std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Connection’ {aka ‘class dbservice::core::Connection’} has no member named ‘connect’; did you mean ‘Connection’?
  365 |         if (!connection->connect(connectionString)) {
      |                          ^~~~~~~
      |                          Connection
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp: In member function ‘std::expected<void, dbservice::service::DatabaseError> dbservice::service::DatabaseInstanceManager::updateCredentials(int, const std::string&, const std::string&)’:
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:396:35: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘query’
  396 |         auto result = connection->query(checkQuery, {std::to_string(instanceId)});
      |                                   ^~~~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:409:21: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘execute’
  409 |         connection->execute(updateQuery, {username, std::to_string(instanceId)});
      |                     ^~~~~~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp: In member function ‘std::expected<bool, dbservice::service::DatabaseError> dbservice::service::DatabaseInstanceManager::testConnection(int)’:
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:451:42: error: ‘using std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Connection’ {aka ‘class dbservice::core::Connection’} has no member named ‘connect’; did you mean ‘Connection’?
  451 |         bool connected = testConnection->connect(connectionString);
      |                                          ^~~~~~~
      |                                          Connection
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:456:47: error: ‘using std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Connection’ {aka ‘class dbservice::core::Connection’} has no member named ‘query’
  456 |                 auto result = testConnection->query("SELECT 1 as test");
      |                                               ^~~~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:457:33: error: ‘using std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Connection’ {aka ‘class dbservice::core::Connection’} has no member named ‘disconnect’
  457 |                 testConnection->disconnect();
      |                                 ^~~~~~~~~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:460:33: error: ‘using std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Connection’ {aka ‘class dbservice::core::Connection’} has no member named ‘disconnect’
  460 |                 testConnection->disconnect();
      |                                 ^~~~~~~~~~
In file included from /usr/include/c++/14/bits/char_traits.h:57,
                 from /usr/include/c++/14/string:42,
                 from /home/<USER>/database-service-build/include/database-service/service/database_instance_manager.hpp:3,
                 from /home/<USER>/database-service-build/src/service/database_instance_manager.cpp:1:
/usr/include/c++/14/bits/stl_construct.h: In instantiation of ‘constexpr void std::_Construct(_Tp*, _Args&& ...) [with _Tp = dbservice::core::Connection; _Args = {}]’:
/usr/include/c++/14/bits/alloc_traits.h:657:19:   required from ‘static constexpr void std::allocator_traits<std::allocator<void> >::construct(allocator_type&, _Up*, _Args&& ...) [with _Up = dbservice::core::Connection; _Args = {}; allocator_type = std::allocator<void>]’
  657 |         { std::_Construct(__p, std::forward<_Args>(__args)...); }
      |           ~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr_base.h:607:39:   required from ‘std::_Sp_counted_ptr_inplace<_Tp, _Alloc, _Lp>::_Sp_counted_ptr_inplace(_Alloc, _Args&& ...) [with _Args = {}; _Tp = dbservice::core::Connection; _Alloc = std::allocator<void>; __gnu_cxx::_Lock_policy _Lp = __gnu_cxx::_S_atomic]’
  607 |           allocator_traits<_Alloc>::construct(__a, _M_ptr(),
      |           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~
  608 |               std::forward<_Args>(__args)...); // might throw
      |               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ 
/usr/include/c++/14/bits/shared_ptr_base.h:969:16:   required from ‘std::__shared_count<_Lp>::__shared_count(_Tp*&, std::_Sp_alloc_shared_tag<_Alloc>, _Args&& ...) [with _Tp = dbservice::core::Connection; _Alloc = std::allocator<void>; _Args = {}; __gnu_cxx::_Lock_policy _Lp = __gnu_cxx::_S_atomic]’
  969 |           auto __pi = ::new (__mem)
      |                       ^~~~~~~~~~~~~
  970 |             _Sp_cp_type(__a._M_a, std::forward<_Args>(__args)...);
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr_base.h:1713:14:   required from ‘std::__shared_ptr<_Tp, _Lp>::__shared_ptr(std::_Sp_alloc_shared_tag<_Tp>, _Args&& ...) [with _Alloc = std::allocator<void>; _Args = {}; _Tp = dbservice::core::Connection; __gnu_cxx::_Lock_policy _Lp = __gnu_cxx::_S_atomic]’
 1713 |         : _M_ptr(), _M_refcount(_M_ptr, __tag, std::forward<_Args>(__args)...)
      |                     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:463:59:   required from ‘std::shared_ptr<_Tp>::shared_ptr(std::_Sp_alloc_shared_tag<_Tp>, _Args&& ...) [with _Alloc = std::allocator<void>; _Args = {}; _Tp = dbservice::core::Connection]’
  463 |         : __shared_ptr<_Tp>(__tag, std::forward<_Args>(__args)...)
      |                                                                  ^
/usr/include/c++/14/bits/shared_ptr.h:1007:14:   required from ‘std::shared_ptr<std::_NonArray<_Tp> > std::make_shared(_Args&& ...) [with _Tp = dbservice::core::Connection; _Args = {}; _NonArray<_Tp> = dbservice::core::Connection]’
 1007 |       return shared_ptr<_Tp>(_Sp_alloc_shared_tag<_Alloc>{__a},
      |              ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1008 |                              std::forward<_Args>(__args)...);
      |                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:364:72:   required from here
  364 |         auto connection = std::make_shared<dbservice::core::Connection>();
      |                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~
/usr/include/c++/14/bits/stl_construct.h:115:28: error: no matching function for call to ‘construct_at(dbservice::core::Connection*&)’
  115 |           std::construct_at(__p, std::forward<_Args>(__args)...);
      |           ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/stl_construct.h:94:5: note: candidate: ‘template<class _Tp, class ... _Args> constexpr decltype (::new(void*(0)) _Tp) std::construct_at(_Tp*, _Args&& ...)’
   94 |     construct_at(_Tp* __location, _Args&&... __args)
      |     ^~~~~~~~~~~~
/usr/include/c++/14/bits/stl_construct.h:94:5: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/stl_construct.h: In substitution of ‘template<class _Tp, class ... _Args> constexpr decltype (::new(void*(0)) _Tp) std::construct_at(_Tp*, _Args&& ...) [with _Tp = dbservice::core::Connection; _Args = {}]’:
/usr/include/c++/14/bits/stl_construct.h:115:21:   required from ‘constexpr void std::_Construct(_Tp*, _Args&& ...) [with _Tp = dbservice::core::Connection; _Args = {}]’
  115 |           std::construct_at(__p, std::forward<_Args>(__args)...);
      |           ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/alloc_traits.h:657:19:   required from ‘static constexpr void std::allocator_traits<std::allocator<void> >::construct(allocator_type&, _Up*, _Args&& ...) [with _Up = dbservice::core::Connection; _Args = {}; allocator_type = std::allocator<void>]’
  657 |         { std::_Construct(__p, std::forward<_Args>(__args)...); }
      |           ~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr_base.h:607:39:   required from ‘std::_Sp_counted_ptr_inplace<_Tp, _Alloc, _Lp>::_Sp_counted_ptr_inplace(_Alloc, _Args&& ...) [with _Args = {}; _Tp = dbservice::core::Connection; _Alloc = std::allocator<void>; __gnu_cxx::_Lock_policy _Lp = __gnu_cxx::_S_atomic]’
  607 |           allocator_traits<_Alloc>::construct(__a, _M_ptr(),
      |           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~
  608 |               std::forward<_Args>(__args)...); // might throw
      |               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ 
/usr/include/c++/14/bits/shared_ptr_base.h:969:16:   required from ‘std::__shared_count<_Lp>::__shared_count(_Tp*&, std::_Sp_alloc_shared_tag<_Alloc>, _Args&& ...) [with _Tp = dbservice::core::Connection; _Alloc = std::allocator<void>; _Args = {}; __gnu_cxx::_Lock_policy _Lp = __gnu_cxx::_S_atomic]’
  969 |           auto __pi = ::new (__mem)
      |                       ^~~~~~~~~~~~~
  970 |             _Sp_cp_type(__a._M_a, std::forward<_Args>(__args)...);
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr_base.h:1713:14:   required from ‘std::__shared_ptr<_Tp, _Lp>::__shared_ptr(std::_Sp_alloc_shared_tag<_Tp>, _Args&& ...) [with _Alloc = std::allocator<void>; _Args = {}; _Tp = dbservice::core::Connection; __gnu_cxx::_Lock_policy _Lp = __gnu_cxx::_S_atomic]’
 1713 |         : _M_ptr(), _M_refcount(_M_ptr, __tag, std::forward<_Args>(__args)...)
      |                     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:463:59:   required from ‘std::shared_ptr<_Tp>::shared_ptr(std::_Sp_alloc_shared_tag<_Tp>, _Args&& ...) [with _Alloc = std::allocator<void>; _Args = {}; _Tp = dbservice::core::Connection]’
  463 |         : __shared_ptr<_Tp>(__tag, std::forward<_Args>(__args)...)
      |                                                                  ^
/usr/include/c++/14/bits/shared_ptr.h:1007:14:   required from ‘std::shared_ptr<std::_NonArray<_Tp> > std::make_shared(_Args&& ...) [with _Tp = dbservice::core::Connection; _Args = {}; _NonArray<_Tp> = dbservice::core::Connection]’
 1007 |       return shared_ptr<_Tp>(_Sp_alloc_shared_tag<_Alloc>{__a},
      |              ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1008 |                              std::forward<_Args>(__args)...);
      |                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:364:72:   required from here
  364 |         auto connection = std::make_shared<dbservice::core::Connection>();
      |                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~
/usr/include/c++/14/bits/stl_construct.h:96:17: error: no matching function for call to ‘dbservice::core::Connection::Connection()’
   96 |     -> decltype(::new((void*)0) _Tp(std::declval<_Args>()...))
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
In file included from /home/<USER>/database-service-build/src/service/database_instance_manager.cpp:3:
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:83:5: note: candidate: ‘dbservice::core::Connection::Connection(dbservice::core::Connection&&)’
   83 |     Connection(Connection&& other) noexcept;
      |     ^~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:83:5: note:   candidate expects 1 argument, 0 provided
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:68:14: note: candidate: ‘dbservice::core::Connection::Connection(std::string_view, bool)’
   68 |     explicit Connection(std::string_view connectionString, bool useSSL = true);
      |              ^~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:68:14: note:   candidate expects 2 arguments, 0 provided
/usr/include/c++/14/bits/stl_construct.h: In instantiation of ‘constexpr void std::_Construct(_Tp*, _Args&& ...) [with _Tp = dbservice::core::Connection; _Args = {}]’:
/usr/include/c++/14/bits/alloc_traits.h:657:19:   required from ‘static constexpr void std::allocator_traits<std::allocator<void> >::construct(allocator_type&, _Up*, _Args&& ...) [with _Up = dbservice::core::Connection; _Args = {}; allocator_type = std::allocator<void>]’
  657 |         { std::_Construct(__p, std::forward<_Args>(__args)...); }
      |           ~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr_base.h:607:39:   required from ‘std::_Sp_counted_ptr_inplace<_Tp, _Alloc, _Lp>::_Sp_counted_ptr_inplace(_Alloc, _Args&& ...) [with _Args = {}; _Tp = dbservice::core::Connection; _Alloc = std::allocator<void>; __gnu_cxx::_Lock_policy _Lp = __gnu_cxx::_S_atomic]’
  607 |           allocator_traits<_Alloc>::construct(__a, _M_ptr(),
      |           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~
  608 |               std::forward<_Args>(__args)...); // might throw
      |               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ 
/usr/include/c++/14/bits/shared_ptr_base.h:969:16:   required from ‘std::__shared_count<_Lp>::__shared_count(_Tp*&, std::_Sp_alloc_shared_tag<_Alloc>, _Args&& ...) [with _Tp = dbservice::core::Connection; _Alloc = std::allocator<void>; _Args = {}; __gnu_cxx::_Lock_policy _Lp = __gnu_cxx::_S_atomic]’
  969 |           auto __pi = ::new (__mem)
      |                       ^~~~~~~~~~~~~
  970 |             _Sp_cp_type(__a._M_a, std::forward<_Args>(__args)...);
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr_base.h:1713:14:   required from ‘std::__shared_ptr<_Tp, _Lp>::__shared_ptr(std::_Sp_alloc_shared_tag<_Tp>, _Args&& ...) [with _Alloc = std::allocator<void>; _Args = {}; _Tp = dbservice::core::Connection; __gnu_cxx::_Lock_policy _Lp = __gnu_cxx::_S_atomic]’
 1713 |         : _M_ptr(), _M_refcount(_M_ptr, __tag, std::forward<_Args>(__args)...)
      |                     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:463:59:   required from ‘std::shared_ptr<_Tp>::shared_ptr(std::_Sp_alloc_shared_tag<_Tp>, _Args&& ...) [with _Alloc = std::allocator<void>; _Args = {}; _Tp = dbservice::core::Connection]’
  463 |         : __shared_ptr<_Tp>(__tag, std::forward<_Args>(__args)...)
      |                                                                  ^
/usr/include/c++/14/bits/shared_ptr.h:1007:14:   required from ‘std::shared_ptr<std::_NonArray<_Tp> > std::make_shared(_Args&& ...) [with _Tp = dbservice::core::Connection; _Args = {}; _NonArray<_Tp> = dbservice::core::Connection]’
 1007 |       return shared_ptr<_Tp>(_Sp_alloc_shared_tag<_Alloc>{__a},
      |              ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 1008 |                              std::forward<_Args>(__args)...);
      |                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/service/database_instance_manager.cpp:364:72:   required from here
  364 |         auto connection = std::make_shared<dbservice::core::Connection>();
      |                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~
/usr/include/c++/14/bits/stl_construct.h:119:7: error: no matching function for call to ‘dbservice::core::Connection::Connection()’
  119 |       ::new((void*)__p) _Tp(std::forward<_Args>(__args)...);
      |       ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:83:5: note: candidate: ‘dbservice::core::Connection::Connection(dbservice::core::Connection&&)’
   83 |     Connection(Connection&& other) noexcept;
      |     ^~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:83:5: note:   candidate expects 1 argument, 0 provided
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:68:14: note: candidate: ‘dbservice::core::Connection::Connection(std::string_view, bool)’
   68 |     explicit Connection(std::string_view connectionString, bool useSSL = true);
      |              ^~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:68:14: note:   candidate expects 2 arguments, 0 provided
make[2]: *** [CMakeFiles/database-service.dir/build.make:303: CMakeFiles/database-service.dir/src/service/database_instance_manager.cpp.o] Error 1
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
