Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.5s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:22:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   22 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[ 16%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 22%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
[ 27%] Building CXX object CMakeFiles/database-service.dir/src/core/transaction.cpp.o
/home/<USER>/database-service-build/src/core/transaction.cpp:22:6: error: no declaration matches ‘bool dbservice::core::Transaction::commit()’
   22 | bool Transaction::commit() {
      |      ^~~~~~~~~~~
In file included from /home/<USER>/database-service-build/src/core/transaction.cpp:1:
/home/<USER>/database-service-build/include/database-service/core/transaction.hpp:34:38: note: candidate is: ‘std::expected<void, std::__cxx11::basic_string<char> > dbservice::core::Transaction::commit()’
   34 |     std::expected<void, std::string> commit();
      |                                      ^~~~~~
/home/<USER>/database-service-build/include/database-service/core/transaction.hpp:17:7: note: ‘class dbservice::core::Transaction’ defined here
   17 | class Transaction {
      |       ^~~~~~~~~~~
/home/<USER>/database-service-build/src/core/transaction.cpp:46:6: error: no declaration matches ‘bool dbservice::core::Transaction::rollback()’
   46 | bool Transaction::rollback() {
      |      ^~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/transaction.hpp:40:38: note: candidate is: ‘std::expected<void, std::__cxx11::basic_string<char> > dbservice::core::Transaction::rollback()’
   40 |     std::expected<void, std::string> rollback();
      |                                      ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/transaction.hpp:17:7: note: ‘class dbservice::core::Transaction’ defined here
   17 | class Transaction {
      |       ^~~~~~~~~~~
make[2]: *** [CMakeFiles/database-service.dir/build.make:135: CMakeFiles/database-service.dir/src/core/transaction.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
