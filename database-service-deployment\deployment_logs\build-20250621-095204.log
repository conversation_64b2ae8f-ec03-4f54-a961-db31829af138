Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  9%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
[  9%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
In file included from /home/<USER>/database-service-build/src/api/route_controller.cpp:1:
/home/<USER>/database-service-build/include/database-service/api/route_controller.hpp:50:31: error: ‘CredentialStore’ is not a member of ‘dbservice::security’
   50 |     std::shared_ptr<security::CredentialStore> credentialStore_;
      |                               ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/route_controller.hpp:50:46: error: template argument 1 is invalid
   50 |     std::shared_ptr<security::CredentialStore> credentialStore_;
      |                                              ^
/home/<USER>/database-service-build/include/database-service/api/route_controller.hpp:50:10: error: ‘<expression error>’ in namespace ‘std’ does not name a type
   50 |     std::shared_ptr<security::CredentialStore> credentialStore_;
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In constructor ‘dbservice::api::RouteController::RouteController(std::shared_ptr<dbservice::core::ConnectionManager>, std::shared_ptr<dbservice::security::SecurityManager>)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:23:5: error: ‘credentialStore_’ was not declared in this scope
   23 |     credentialStore_ = security::CredentialStore::getSharedInstance();
      |     ^~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:92:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   92 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleStoreCredentials(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:595:24: error: ‘credentialStore_’ was not declared in this scope
  595 |         bool success = credentialStore_->storeCredential(key, value);
      |                        ^~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleGetCredentials(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:638:23: error: ‘credentialStore_’ was not declared in this scope
  638 |         auto result = credentialStore_->getCredential(key);
      |                       ^~~~~~~~~~~~~~~~
make[2]: *** [CMakeFiles/database-service.dir/build.make:93: CMakeFiles/database-service.dir/src/api/route_controller.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
