Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  5%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:22:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   22 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[ 16%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 22%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
[ 27%] Building CXX object CMakeFiles/database-service.dir/src/core/transaction.cpp.o
[ 33%] Building CXX object CMakeFiles/database-service.dir/src/database_service.cpp.o
[ 38%] Building CXX object CMakeFiles/database-service.dir/src/main.cpp.o
[ 44%] Building CXX object CMakeFiles/database-service.dir/src/metrics/database_metrics.cpp.o
[ 50%] Building CXX object CMakeFiles/database-service.dir/src/metrics/metrics_collector.cpp.o
[ 55%] Building CXX object CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o
[ 61%] Building CXX object CMakeFiles/database-service.dir/src/security/credential_store.cpp.o
/home/<USER>/database-service-build/src/security/credential_store.cpp: In member function ‘bool dbservice::security::CredentialStore::initialize(const std::string&)’:
/home/<USER>/database-service-build/src/security/credential_store.cpp:41:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   41 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/credential_store.cpp:6:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:42:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   42 |     SHA256_Update(&sha256, encryptionKey.c_str(), encryptionKey.length());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:43:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   43 |     SHA256_Final(key, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
[ 66%] Building CXX object CMakeFiles/database-service.dir/src/security/jwt.cpp.o
[ 72%] Building CXX object CMakeFiles/database-service.dir/src/security/security_manager.cpp.o
/home/<USER>/database-service-build/src/security/security_manager.cpp: In function ‘std::string dbservice::security::sha256(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:30:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   30 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/security_manager.cpp:13:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:31:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   31 |     SHA256_Update(&sha256, input.c_str(), input.size());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:32:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   32 |     SHA256_Final(hash, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
[ 77%] Building CXX object CMakeFiles/database-service.dir/src/utils/cache.cpp.o
[ 83%] Building CXX object CMakeFiles/database-service.dir/src/utils/config_manager.cpp.o
[ 88%] Building CXX object CMakeFiles/database-service.dir/src/utils/logger.cpp.o
[ 94%] Building CXX object CMakeFiles/database-service.dir/src/utils/thread_pool.cpp.o
/home/<USER>/database-service-build/src/utils/thread_pool.cpp: In member function ‘size_t dbservice::utils::ThreadPool::getPendingTaskCount() const’:
/home/<USER>/database-service-build/src/utils/thread_pool.cpp:75:50: error: no matching function for call to ‘std::unique_lock<std::mutex>::unique_lock(const std::mutex&)’
   75 |     std::unique_lock<std::mutex> lock(queueMutex_);
      |                                                  ^
In file included from /usr/include/c++/14/mutex:48,
                 from /home/<USER>/database-service-build/include/database-service/utils/thread_pool.hpp:6,
                 from /home/<USER>/database-service-build/src/utils/thread_pool.cpp:1:
/usr/include/c++/14/bits/unique_lock.h:70:16: note: candidate: ‘std::unique_lock<_Mutex>::unique_lock(mutex_type&) [with _Mutex = std::mutex; mutex_type = std::mutex]’ (near match)
   70 |       explicit unique_lock(mutex_type& __m)
      |                ^~~~~~~~~~~
/usr/include/c++/14/bits/unique_lock.h:70:16: note:   conversion of argument 1 would be ill-formed:
/home/<USER>/database-service-build/src/utils/thread_pool.cpp:75:39: error: binding reference of type ‘std::unique_lock<std::mutex>::mutex_type&’ {aka ‘std::mutex&’} to ‘const std::mutex’ discards qualifiers
   75 |     std::unique_lock<std::mutex> lock(queueMutex_);
      |                                       ^~~~~~~~~~~
/usr/include/c++/14/bits/unique_lock.h:103:9: note: candidate: ‘template<class _Rep, class _Period> std::unique_lock<_Mutex>::unique_lock(mutex_type&, const std::chrono::duration<_Rep, _Period>&) [with _Period = _Rep; _Mutex = std::mutex]’
  103 |         unique_lock(mutex_type& __m,
      |         ^~~~~~~~~~~
/usr/include/c++/14/bits/unique_lock.h:103:9: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/bits/unique_lock.h:95:9: note: candidate: ‘template<class _Clock, class _Duration> std::unique_lock<_Mutex>::unique_lock(mutex_type&, const std::chrono::time_point<_Clock, _Dur>&) [with _Duration = _Clock; _Mutex = std::mutex]’
   95 |         unique_lock(mutex_type& __m,
      |         ^~~~~~~~~~~
/usr/include/c++/14/bits/unique_lock.h:95:9: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/bits/unique_lock.h:118:7: note: candidate: ‘std::unique_lock<_Mutex>::unique_lock(std::unique_lock<_Mutex>&&) [with _Mutex = std::mutex]’
  118 |       unique_lock(unique_lock&& __u) noexcept
      |       ^~~~~~~~~~~
/usr/include/c++/14/bits/unique_lock.h:118:33: note:   no known conversion for argument 1 from ‘const std::mutex’ to ‘std::unique_lock<std::mutex>&&’
  118 |       unique_lock(unique_lock&& __u) noexcept
      |                   ~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/unique_lock.h:87:7: note: candidate: ‘std::unique_lock<_Mutex>::unique_lock(mutex_type&, std::adopt_lock_t) [with _Mutex = std::mutex; mutex_type = std::mutex]’
   87 |       unique_lock(mutex_type& __m, adopt_lock_t) noexcept
      |       ^~~~~~~~~~~
/usr/include/c++/14/bits/unique_lock.h:87:7: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/bits/unique_lock.h:82:7: note: candidate: ‘std::unique_lock<_Mutex>::unique_lock(mutex_type&, std::try_to_lock_t) [with _Mutex = std::mutex; mutex_type = std::mutex]’
   82 |       unique_lock(mutex_type& __m, try_to_lock_t)
      |       ^~~~~~~~~~~
/usr/include/c++/14/bits/unique_lock.h:82:7: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/bits/unique_lock.h:77:7: note: candidate: ‘std::unique_lock<_Mutex>::unique_lock(mutex_type&, std::defer_lock_t) [with _Mutex = std::mutex; mutex_type = std::mutex]’
   77 |       unique_lock(mutex_type& __m, defer_lock_t) noexcept
      |       ^~~~~~~~~~~
/usr/include/c++/14/bits/unique_lock.h:77:7: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/bits/unique_lock.h:65:7: note: candidate: ‘std::unique_lock<_Mutex>::unique_lock() [with _Mutex = std::mutex]’
   65 |       unique_lock() noexcept
      |       ^~~~~~~~~~~
/usr/include/c++/14/bits/unique_lock.h:65:7: note:   candidate expects 0 arguments, 1 provided
make[2]: *** [CMakeFiles/database-service.dir/build.make:303: CMakeFiles/database-service.dir/src/utils/thread_pool.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
