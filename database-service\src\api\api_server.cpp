#include "database-service/api/api_server.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/security/security_manager.hpp"
#include "database-service/security/credential_store.hpp"
#include "database-service/metrics/database_metrics.hpp"
#include "database-service/utils/logger.hpp"
#include "database-service/utils/config_manager.hpp"
#include "database-service/database_service.hpp"
#include <format> // C++20 feature
#include <nlohmann/json.hpp>
#include <regex>
#include <sstream>
#include <thread>
#include <chrono>
#include <algorithm>

// Platform-specific socket includes
#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
    #define SOCKET_ERROR_CODE WSAGetLastError()
    #define CLOSE_SOCKET closesocket
    typedef int socklen_t;
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <unistd.h>
    #include <arpa/inet.h>
    #include <errno.h>
    #define SOCKET int
    #define INVALID_SOCKET -1
    #define SOCKET_ERROR -1
    #define SOCKET_ERROR_CODE errno
    #define CLOSE_SOCKET close
#endif

namespace dbservice::api {

ApiServer::ApiServer(unsigned short port,
                   std::shared_ptr<core::ConnectionManager> connectionManager,
                   std::shared_ptr<security::SecurityManager> securityManager,
                   std::shared_ptr<DatabaseService> databaseService)
    : port_(port),
      connectionManager_(connectionManager),
      securityManager_(securityManager),
      databaseService_(databaseService),
      running_(false),
      serverSocket_(INVALID_SOCKET) {

    // Initialize thread pool with optimal number of threads
    size_t threadCount = std::thread::hardware_concurrency();
    if (threadCount == 0) threadCount = 8; // Fallback
    threadPool_ = std::make_unique<utils::ThreadPool>(threadCount);

    // Load CORS configuration
    loadCorsConfig();
}

ApiServer::~ApiServer() {
    stop();
}

std::expected<void, dbservice::api::ApiError> ApiServer::start() {
    if (running_.load(std::memory_order_relaxed)) {
        utils::Logger::warning("API server is already running");
        return {}; // Already running, considered success for idempotency
    }

    // Initialize socket library on Windows
    #ifdef _WIN32
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        std::string errorMsg = std::format("Failed to initialize Winsock: {}", SOCKET_ERROR_CODE);
        utils::Logger::error(errorMsg);
        return std::unexpected(ApiError{ApiErrorType::WinsockInit, errorMsg, SOCKET_ERROR_CODE});
    }
    #endif

    // Create socket
    serverSocket_ = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (serverSocket_ == INVALID_SOCKET) {
        std::string errorMsg = std::format("Failed to create socket: {}", SOCKET_ERROR_CODE);
        utils::Logger::error(errorMsg);
        #ifdef _WIN32
        WSACleanup();
        #endif
        return std::unexpected(ApiError{ApiErrorType::SocketCreation, errorMsg, SOCKET_ERROR_CODE});
    }

    // Set socket options
    int opt = 1;
    if (setsockopt(serverSocket_, SOL_SOCKET, SO_REUSEADDR, (const char*)&opt, sizeof(opt)) < 0) {
        std::string errorMsg = std::format("Failed to set socket options: {}", SOCKET_ERROR_CODE);
        utils::Logger::error(errorMsg);
        CLOSE_SOCKET(serverSocket_);
        #ifdef _WIN32
        WSACleanup();
        #endif
        return std::unexpected(ApiError{ApiErrorType::SocketOption, errorMsg, SOCKET_ERROR_CODE});
    }

    // Bind socket
    struct sockaddr_in address;
    address.sin_family = AF_INET;
    address.sin_addr.s_addr = INADDR_ANY;
    address.sin_port = htons(port_);

    if (bind(serverSocket_, (struct sockaddr*)&address, sizeof(address)) < 0) {
        std::string errorMsg = std::format("Failed to bind socket: {}", SOCKET_ERROR_CODE);
        utils::Logger::error(errorMsg);
        CLOSE_SOCKET(serverSocket_);
        #ifdef _WIN32
        WSACleanup();
        #endif
        return std::unexpected(ApiError{ApiErrorType::SocketBind, errorMsg, SOCKET_ERROR_CODE});
    }

    // Listen for connections
    if (listen(serverSocket_, 10) < 0) {
        std::string errorMsg = std::format("Failed to listen on socket: {}", SOCKET_ERROR_CODE);
        utils::Logger::error(errorMsg);
        CLOSE_SOCKET(serverSocket_);
        #ifdef _WIN32
        WSACleanup();
        #endif
        return std::unexpected(ApiError{ApiErrorType::SocketListen, errorMsg, SOCKET_ERROR_CODE});
    }

    // Start server thread
    running_.store(true, std::memory_order_relaxed);
    serverThread_ = std::thread(&ApiServer::run, this);

    utils::Logger::info(std::format("API server started on port {}", port_));
    return {};
}

std::expected<void, dbservice::api::ApiError> ApiServer::stop() {
    if (!running_.load(std::memory_order_relaxed)) {
        return {}; // Already stopped or never started
    }

    running_.store(false, std::memory_order_relaxed);

    // Close server socket
    if (serverSocket_ != INVALID_SOCKET) {
        CLOSE_SOCKET(serverSocket_);
        serverSocket_ = INVALID_SOCKET;
    }

    // Wait for server thread to finish
    if (serverThread_.joinable()) {
        serverThread_.join();
    }

    // Cleanup socket library on Windows
    #ifdef _WIN32
    WSACleanup();
    #endif
    utils::Logger::info("API server stopped");
    return {};
}

bool ApiServer::isRunning() const {
    return running_.load(std::memory_order_relaxed);
}

unsigned short ApiServer::getPort() const {
    return port_;
}

void ApiServer::configureCors(const CorsConfig& config) {
    corsConfig_ = config;
    utils::Logger::info(std::format("CORS configuration updated: enabled={}", corsConfig_.enabled ? "true" : "false"));
}

void ApiServer::configureSSL(const SSLConfig& config) {
    sslConfig_ = config;
    utils::Logger::info(std::format("SSL configuration updated: enabled={}", sslConfig_.enabled ? "true" : "false"));

    if (sslConfig_.enabled) {
        utils::Logger::info(std::format("SSL certificate: {}", sslConfig_.certPath));
        utils::Logger::info(std::format("SSL key: {}", sslConfig_.keyPath));
    }
}

void ApiServer::addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler) {
    std::string regex_path = path;
    std::vector<std::string> param_names;
    std::regex param_regex("\\{([^}]+)\\}");

    auto words_begin = std::sregex_iterator(path.begin(), path.end(), param_regex);
    auto words_end = std::sregex_iterator();
    for (std::sregex_iterator i = words_begin; i != words_end; ++i) {
        param_names.push_back((*i)[1].str());
    }

    regex_path = std::regex_replace(path, param_regex, "([^/]+)");

    routes_[method].push_back({
        std::regex("^" + regex_path + "$"),
        param_names,
        std::move(handler)
    });

    utils::Logger::info(std::format("Route added: {} {}", method, path));
}

nlohmann::json ApiServer::getThreadPoolStats() const {
    nlohmann::json stats;
    if (threadPool_) {
        stats["thread_count"] = threadPool_->getThreadCount();
        stats["pending_tasks"] = threadPool_->getPendingTaskCount();
        stats["active_tasks"] = threadPool_->getActiveTaskCount();
        stats["is_shutting_down"] = threadPool_->isShuttingDown();
    } else {
        stats["error"] = "Thread pool not initialized";
    }
    return stats;
}

void ApiServer::loadCorsConfig() {
    auto& configManager = utils::ConfigManager::getInstance();

    corsConfig_.enabled = configManager.getBool("api.cors.enabled", false);

    if (corsConfig_.enabled) {
        // Load allowed origins
        auto originsJson = configManager.getJsonArray("api.cors.allowed_origins");
        if (!originsJson.empty()) {
            for (const auto& origin : originsJson) {
                if (origin.is_string()) {
                    corsConfig_.allowedOrigins.push_back(origin);
                }
            }
        } else {
            // Default to all origins
            corsConfig_.allowedOrigins.push_back("*");
        }

        // Load allowed methods
        auto methodsJson = configManager.getJsonArray("api.cors.allowed_methods");
        if (!methodsJson.empty()) {
            for (const auto& method : methodsJson) {
                if (method.is_string()) {
                    corsConfig_.allowedMethods.push_back(method);
                }
            }
        } else {
            // Default methods
            corsConfig_.allowedMethods = {"GET", "POST", "PUT", "DELETE", "OPTIONS"};
        }

        // Load allowed headers
        auto headersJson = configManager.getJsonArray("api.cors.allowed_headers");
        if (!headersJson.empty()) {
            for (const auto& header : headersJson) {
                if (header.is_string()) {
                    corsConfig_.allowedHeaders.push_back(header);
                }
            }
        } else {
            // Default headers
            corsConfig_.allowedHeaders = {"Content-Type", "Authorization"};
        }

        // Load other settings
        corsConfig_.allowCredentials = configManager.getBool("api.cors.allow_credentials", false);
        corsConfig_.maxAge = configManager.getInt("api.cors.max_age", 86400);

        utils::Logger::info(std::format("CORS enabled with {} allowed origins", corsConfig_.allowedOrigins.size()));
    } else {
        utils::Logger::info("CORS is disabled");
    }
}

std::expected<Response, std::string> ApiServer::handleRequest(ParsedRequest& request) {
    try {
        if (corsConfig_.enabled && request.method == "OPTIONS") {
            Response response;
            applyCorsHeaders(request.method, request.headers, response.headers);
            return response;
        }

        auto it = routes_.find(request.method);
        if (it != routes_.end()) {
            for (auto& route : it->second) {
                std::smatch match;
                if (std::regex_match(request.path, match, route.pattern)) {
                    for (size_t i = 0; i < route.param_names.size(); ++i) {
                        request.path_params[route.param_names[i]] = match[i + 1].str();
                    }

                    // TODO: Re-implement authentication check based on route properties
                    // For now, we'll keep the simplified check
                    bool requiresAuth = true;
                    if (request.path == "/api/auth/login" || request.path == "/api/health") {
                        requiresAuth = false;
                    }

                    if (requiresAuth) {
                        auto auth_it = request.headers.find("Authorization");
                        if (auth_it == request.headers.end()) {
                            return std::unexpected("Authentication required");
                        }

                        std::string_view authHeader = auth_it->second;
                        if (!authHeader.starts_with("Bearer ")) {
                            return std::unexpected("Invalid authentication format");
                        }

                        std::string token(authHeader.substr(7));
                        if (!securityManager_->validateToken(token)) {
                            return std::unexpected("Invalid or expired token");
                        }
                    }

                    auto result = route.handler(request);
                    if (result && corsConfig_.enabled) {
                        applyCorsHeaders(request.method, request.headers, result->headers);
                    }
                    return result;
                }
            }
        }

        return std::unexpected("Endpoint not found");
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during request handling: {}", e.what()));
        return std::unexpected(std::string("Internal Server Error: ") + e.what());
    }
}


void ApiServer::applyCorsHeaders(
    std::string_view method,
    const std::unordered_map<std::string, std::string>& headers,
    std::unordered_map<std::string, std::string>& responseHeaders) {

    if (!corsConfig_.enabled) {
        return;
    }

    // Handle Origin header
    auto originIt = headers.find("Origin");
    if (originIt != headers.end()) {
        const std::string& origin = originIt->second;

        // Check if origin is allowed
        bool originAllowed = false;

        // Check for wildcard
        if (std::find(corsConfig_.allowedOrigins.begin(), corsConfig_.allowedOrigins.end(), "*") != corsConfig_.allowedOrigins.end()) {
            originAllowed = true;
            responseHeaders["Access-Control-Allow-Origin"] = "*";
        } else {
            // Check specific origins
            for (const auto& allowedOrigin : corsConfig_.allowedOrigins) {
                if (origin == allowedOrigin) {
                    originAllowed = true;
                    responseHeaders["Access-Control-Allow-Origin"] = origin;
                    break;
                }
            }
        }

        if (originAllowed) {
            // Set other CORS headers
            if (corsConfig_.allowCredentials) {
                responseHeaders["Access-Control-Allow-Credentials"] = "true";
            }

            // For preflight requests
            if (method == "OPTIONS") {
                // Allowed methods
                if (!corsConfig_.allowedMethods.empty()) {
                    std::string methodsStr;
                    for (size_t i = 0; i < corsConfig_.allowedMethods.size(); ++i) {
                        if (i > 0) {
                            methodsStr += ", ";
                        }
                        methodsStr += corsConfig_.allowedMethods[i];
                    }
                    responseHeaders["Access-Control-Allow-Methods"] = methodsStr;
                }

                // Allowed headers
                if (!corsConfig_.allowedHeaders.empty()) {
                    std::string headersStr;
                    for (size_t i = 0; i < corsConfig_.allowedHeaders.size(); ++i) {
                        if (i > 0) {
                            headersStr += ", ";
                        }
                        headersStr += corsConfig_.allowedHeaders[i];
                    }
                    responseHeaders["Access-Control-Allow-Headers"] = headersStr;
                }

                // Max age
                responseHeaders["Access-Control-Max-Age"] = std::to_string(corsConfig_.maxAge);
            }
        }
    }
}

void ApiServer::run() {
    while (running_.load(std::memory_order_relaxed)) {
        // Accept connection
        struct sockaddr_in clientAddress;
        socklen_t clientAddressLength = sizeof(clientAddress);
        SOCKET clientSocket = accept(serverSocket_, (struct sockaddr*)&clientAddress, &clientAddressLength);

        if (clientSocket == INVALID_SOCKET) {
            if (running_) {
                utils::Logger::error(std::format("Failed to accept connection: {}", SOCKET_ERROR_CODE));
            }
            continue;
        }

        // Handle connection using thread pool
        try {
            threadPool_->enqueue([this, clientSocket, clientAddress]() {
                this->handleConnection(clientSocket, clientAddress);
            });
        } catch (const std::exception& e) {
            utils::Logger::error(std::format("Failed to enqueue connection handler: {}", e.what()));
            CLOSE_SOCKET(clientSocket);
        }
    }
}

void ApiServer::handleConnection(int clientSocket, struct sockaddr_in clientAddress) {
    try {
        char clientIp[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &clientAddress.sin_addr, clientIp, INET_ADDRSTRLEN);
        utils::Logger::debug(std::format("Connection accepted from {}", clientIp));

        // Set timeout
        #ifdef _WIN32
        DWORD timeout = 30000; // 30 seconds
        setsockopt(clientSocket, SOL_SOCKET, SO_RCVTIMEO, (const char*)&timeout, sizeof(timeout));
        #else
        struct timeval timeout;
        timeout.tv_sec = 30;
        timeout.tv_usec = 0;
        setsockopt(clientSocket, SOL_SOCKET, SO_RCVTIMEO, (const char*)&timeout, sizeof(timeout));
        #endif

        // Receive request
        std::string request;
        char buffer[4096];
        int bytesRead;

        while ((bytesRead = recv(clientSocket, buffer, sizeof(buffer) - 1, 0)) > 0) {
            buffer[bytesRead] = '\0';
            request += buffer;

            // Check if request is complete
            if (request.find("\r\n\r\n") != std::string::npos) {
                break;
            }
        }

        if (bytesRead < 0) {
            utils::Logger::error(std::format("Failed to receive request: {}", SOCKET_ERROR_CODE));
            CLOSE_SOCKET(clientSocket);
            return;
        }

        // Parse request
        auto parsed_request_expected = parseRequest(request);
        if (!parsed_request_expected) {
            utils::Logger::error(std::format("Failed to parse request: {}", parsed_request_expected.error()));
            Response error_response = createErrorResponse(400, "Bad Request", parsed_request_expected.error());
            std::string error_response_str = formatHttpResponse(error_response);
            send(clientSocket, error_response_str.c_str(), error_response_str.length(), 0);
            CLOSE_SOCKET(clientSocket);
            return;
        }
        ParsedRequest parsed_request = *parsed_request_expected;

        // Handle request
        auto response_expected = handleRequest(parsed_request);
        if (!response_expected) {
            utils::Logger::error(std::format("Failed to handle request: {}", response_expected.error()));
            // Use the error message from handleRequest if available, otherwise a generic one
            Response error_response = createErrorResponse(500, "Internal Server Error", response_expected.error());
            std::string error_response_str = formatHttpResponse(error_response);
            send(clientSocket, error_response_str.c_str(), error_response_str.length(), 0);
            CLOSE_SOCKET(clientSocket);
            return;
        }
        Response api_response = *response_expected;
        // From here, use api_response.headers and api_response.body
        // For example, to maintain similar variable names for the subsequent response building code:
        const auto& responseHeaders = api_response.headers;
        const auto& responseBody = api_response.body;

        // Build response
        std::ostringstream response;

        // Status line (default to 200 OK)
        response << "HTTP/1.1 200 OK\r\n";

        // Headers
        for (const auto& [name, value] : responseHeaders) {
            response << name << ": " << value << "\r\n";
        }

        // Add content length if not present
        if (responseHeaders.find("Content-Length") == responseHeaders.end()) {
            response << "Content-Length: " << responseBody.length() << "\r\n";
        }

        // Add content type if not present
        if (responseHeaders.find("Content-Type") == responseHeaders.end()) {
            response << "Content-Type: application/json\r\n";
        }

        // Add connection close if not present
        if (responseHeaders.find("Connection") == responseHeaders.end()) {
            response << "Connection: close\r\n";
        }

        // End of headers
        response << "\r\n";

        // Body
        response << responseBody;

        // Send response
        std::string responseStr = response.str();
        send(clientSocket, responseStr.c_str(), responseStr.length(), 0);

        // Close connection
        CLOSE_SOCKET(clientSocket);
        utils::Logger::debug(std::format("Connection closed from {}", clientIp));
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception in connection handler: {}", e.what()));
        CLOSE_SOCKET(clientSocket);
    } catch (...) {
        utils::Logger::error("Unknown exception in connection handler");
        CLOSE_SOCKET(clientSocket);
    }
}

void ApiServer::initializeRoutes() {
    // CORS preflight handler for /api endpoint
    this->addRoute("OPTIONS", "/api",
        [this](const ParsedRequest& /*request*/) -> std::expected<Response, std::string> {
            Response response_struct;
            response_struct.statusCode = 204; // No Content
            if (this->corsConfig_.enabled) {
                // These headers should ideally be configured based on corsConfig_ and the specific route/request origin.
                // For simplicity in this step, using common permissive values.
                response_struct.headers["Access-Control-Allow-Origin"] = "*"; 
                response_struct.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, PATCH, OPTIONS";
                response_struct.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With";
                response_struct.headers["Access-Control-Max-Age"] = std::to_string(this->corsConfig_.maxAge);
                if (this->corsConfig_.allowCredentials) {
                    response_struct.headers["Access-Control-Allow-Credentials"] = "true";
                }
            }
            response_struct.body = ""; // Body is empty for 204
            return response_struct;
        }
    );

    // Health check
    this->addRoute("GET", "/api/health",
        [this](const ParsedRequest& /*request*/) -> std::expected<Response, std::string> {
            nlohmann::json json_body;
            json_body["status"] = "ok";
            json_body["version"] = "1.0.0"; // Consider making version dynamic
            json_body["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::system_clock::now().time_since_epoch()
            ).count();

            Response response_struct;
            response_struct.statusCode = 200;
            response_struct.headers["Content-Type"] = "application/json";
            response_struct.body = json_body.dump();
            
            // Add CORS headers if enabled
            if (this->corsConfig_.enabled) {
                response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowedOrigins.empty() ? "*" : this->corsConfig_.allowedOrigins[0];
                 if (this->corsConfig_.allowCredentials) {
                    response_struct.headers["Access-Control-Allow-Credentials"] = "true";
                }
            }
            return response_struct;
        }
    );

    // Database metrics
    this->addRoute("GET", "/api/database/metrics",
        [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
            Response response_struct;
            try {
                auto auth_header_it = request.headers.find("Authorization");
                if (auth_header_it == request.headers.end()) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Missing Authorization header"}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing Authorization header"); // Error for logging, actual response in struct
                }

                std::string token = auth_header_it->second;
                if (token.rfind("Bearer ", 0) == 0) {
                    token = token.substr(7);
                }

                auto validationResult = securityManager_->validateToken(token);
                if (!validationResult) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", validationResult.error().message}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(validationResult.error().message);
                }

                // Assuming validateToken now returns std::expected<UserClaims, SecurityError>
                // And UserClaims struct/map contains an 'is_admin' field or similar.
                // This part needs to be adjusted based on the actual return type of validateToken.
                // For now, let's assume it returns a structure or map from which we can get admin status.
                // Placeholder: directly get user info to check admin status
                auto userInfo = securityManager_->getUserInfo(*validationResult); // Assuming validateToken returns username
                bool isAdmin = false;
                if (userInfo && userInfo->count("is_admin") && ((*userInfo)["is_admin"] == "true" || (*userInfo)["is_admin"] == "t")) {
                    isAdmin = true;
                }

                if (!isAdmin) {
                    response_struct.statusCode = 403;
                    response_struct.body = nlohmann::json({
                        {"error", "Forbidden"},
                        {"message", "User does not have admin privileges"}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("User is not admin");
                }

                auto metrics_expected = databaseService_->getDatabaseMetrics();
                if (!metrics_expected) {
                    response_struct.statusCode = 500;
                    response_struct.body = nlohmann::json({
                        {"error", "Internal Server Error"},
                        {"message", metrics_expected.error()}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(metrics_expected.error());
                }

                response_struct.statusCode = 200;
                response_struct.headers["Content-Type"] = "application/json";
                response_struct.body = (*metrics_expected).dump(); // Assuming metrics_expected.value() is nlohmann::json

                // Add CORS headers if enabled
                if (this->corsConfig_.enabled) {
                    response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowedOrigins.empty() ? "*" : this->corsConfig_.allowedOrigins[0];
                    if (this->corsConfig_.allowCredentials) {
                        response_struct.headers["Access-Control-Allow-Credentials"] = "true";
                    }
                }
                return response_struct;

            } catch (const std::exception& e) {
                utils::Logger::error(std::format("Exception in /api/database/metrics: {}", e.what()));
                response_struct.statusCode = 500;
                response_struct.body = nlohmann::json({
                    {"error", "Internal Server Error"},
                    {"message", e.what()}
                }).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Exception: ") + e.what());
            }
        }
    );

    this->addRoute("GET", "/api/database/metrics/connection-pool",
        [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
            Response response_struct;
            try {
                auto auth_header_it = request.headers.find("Authorization");
                if (auth_header_it == request.headers.end()) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Missing Authorization header"}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing Authorization header");
                }

                std::string token = auth_header_it->second;
                if (token.rfind("Bearer ", 0) == 0) {
                    token = token.substr(7);
                }

                auto validationResult = securityManager_->validateToken(token);
                if (!validationResult) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", validationResult.error().message}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(validationResult.error().message);
                }

                auto userInfo = securityManager_->getUserInfo(*validationResult);
                bool isAdmin = false;
                if (userInfo && userInfo->count("is_admin") && ((*userInfo)["is_admin"] == "true" || (*userInfo)["is_admin"] == "t")) {
                    isAdmin = true;
                }

                if (!isAdmin) {
                    response_struct.statusCode = 403;
                    response_struct.body = nlohmann::json({
                        {"error", "Forbidden"},
                        {"message", "User does not have admin privileges"}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("User is not admin");
                }

                // Assuming databaseService_ has a method to get connection pool metrics
                auto metrics_expected = databaseService_->getConnectionPoolMetrics();
                if (!metrics_expected) {
                    response_struct.statusCode = 500;
                    response_struct.body = nlohmann::json({
                        {"error", "Internal Server Error"},
                        {"message", metrics_expected.error()}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(metrics_expected.error());
                }

                response_struct.statusCode = 200;
                response_struct.headers["Content-Type"] = "application/json";
                response_struct.body = (*metrics_expected).dump();

                if (this->corsConfig_.enabled) {
                    response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowedOrigins.empty() ? "*" : this->corsConfig_.allowedOrigins[0];
                    if (this->corsConfig_.allowCredentials) {
                        response_struct.headers["Access-Control-Allow-Credentials"] = "true";
                    }
                }
                return response_struct;

            } catch (const std::exception& e) {
                utils::Logger::error(std::format("Exception in /api/database/metrics/connection-pool: {}", e.what()));
                response_struct.statusCode = 500;
                response_struct.body = nlohmann::json({
                    {"error", "Internal Server Error"},
                    {"message", e.what()}
                }).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Exception: ") + e.what());
            }
        }
    );

    this->addRoute("GET", "/api/database/metrics/query-performance",
        [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
            Response response_struct;
            try {
                auto auth_header_it = request.headers.find("Authorization");
                if (auth_header_it == request.headers.end()) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Missing Authorization header"}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing Authorization header");
                }

                std::string token = auth_header_it->second;
                if (token.rfind("Bearer ", 0) == 0) {
                    token = token.substr(7);
                }

                auto validationResult = securityManager_->validateToken(token);
                if (!validationResult) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", validationResult.error().message}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(validationResult.error().message);
                }

                auto userInfo = securityManager_->getUserInfo(*validationResult);
                bool isAdmin = false;
                if (userInfo && userInfo->count("is_admin") && ((*userInfo)["is_admin"] == "true" || (*userInfo)["is_admin"] == "t")) {
                    isAdmin = true;
                }

                if (!isAdmin) {
                    response_struct.statusCode = 403;
                    response_struct.body = nlohmann::json({
                        {"error", "Forbidden"},
                        {"message", "User does not have admin privileges"}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("User is not admin");
                }

                // Assuming databaseService_ has a method to get query performance metrics
                auto metrics_expected = databaseService_->getQueryPerformanceMetrics();
                if (!metrics_expected) {
                    response_struct.statusCode = 500;
                    response_struct.body = nlohmann::json({
                        {"error", "Internal Server Error"},
                        {"message", metrics_expected.error()}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(metrics_expected.error());
                }

                response_struct.statusCode = 200;
                response_struct.headers["Content-Type"] = "application/json";
                response_struct.body = (*metrics_expected).dump();

                if (this->corsConfig_.enabled) {
                    response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowedOrigins.empty() ? "*" : this->corsConfig_.allowedOrigins[0];
                    if (this->corsConfig_.allowCredentials) {
                        response_struct.headers["Access-Control-Allow-Credentials"] = "true";
                    }
                }
                return response_struct;

            } catch (const std::exception& e) {
                utils::Logger::error(std::format("Exception in /api/database/metrics/query-performance: {}", e.what()));
                response_struct.statusCode = 500;
                response_struct.body = nlohmann::json({
                    {"error", "Internal Server Error"},
                    {"message", e.what()}
                }).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Exception: ") + e.what());
            }
        }
    );



    // Authentication
    this->addRoute("POST", "/api/auth/login",
        [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
            Response response_struct;
            try {
                if (request.body.empty()) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({
                        {"error", "Bad Request"},
                        {"message", "Request body is empty"}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Request body is empty");
                }

                nlohmann::json request_json = nlohmann::json::parse(request.body);

                if (!request_json.contains("username") || !request_json.contains("password")) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({
                        {"error", "Bad Request"},
                        {"message", "Username and password are required"}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing username or password in request body");
                }

                std::string username = request_json["username"].get<std::string>();
                std::string password = request_json["password"].get<std::string>();

                auto tokens_expected = securityManager_->authenticate(username, password);

                if (!tokens_expected) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", tokens_expected.error().message} // Use error message from SecurityError
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(tokens_expected.error().message);
                }

                const auto& tokens = *tokens_expected;
                nlohmann::json response_body_json = {
                    {"access_token", tokens.accessToken},
                    {"refresh_token", tokens.refreshToken},
                    {"user", username}, // Consider getting username from tokens_expected if it contains verified user identity
                    {"token_type", "Bearer"}
                };

                response_struct.statusCode = 200;
                response_struct.headers["Content-Type"] = "application/json";
                response_struct.body = response_body_json.dump();

                if (this->corsConfig_.enabled) {
                    response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowedOrigins.empty() ? "*" : this->corsConfig_.allowedOrigins[0];
                     if (this->corsConfig_.allowCredentials) {
                        response_struct.headers["Access-Control-Allow-Credentials"] = "true";
                    }
                }
                return response_struct;

            } catch (const nlohmann::json::parse_error& e) {
                utils::Logger::error(std::format("JSON parse error in /api/auth/login: {}", e.what()));
                response_struct.statusCode = 400;
                response_struct.body = nlohmann::json({
                    {"error", "Bad Request"},
                    {"message", std::string("Invalid JSON format: ") + e.what()}
                }).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Invalid JSON: ") + e.what());
            } catch (const std::exception& e) {
                utils::Logger::error(std::format("Exception in /api/auth/login: {}", e.what()));
                response_struct.statusCode = 500;
                response_struct.body = nlohmann::json({
                    {"error", "Internal Server Error"},
                    {"message", e.what()}
                }).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Exception: ") + e.what());
            }
        }
    );

    // Token refresh
    this->addRoute("POST", "/api/auth/refresh",
        [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
            Response response_struct;
            try {
                if (request.body.empty()) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({
                        {"error", "Bad Request"},
                        {"message", "Request body is empty"}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Request body is empty");
                }

                nlohmann::json request_json = nlohmann::json::parse(request.body);

                if (!request_json.contains("refresh_token")) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({
                        {"error", "Bad Request"},
                        {"message", "refresh_token is required"}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing refresh_token in request body");
                }

                std::string refresh_token = request_json["refresh_token"].get<std::string>();
                auto tokens_expected = securityManager_->refreshAccessToken(refresh_token);

                if (!tokens_expected) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", tokens_expected.error().message} // Use error message from SecurityError
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(tokens_expected.error().message);
                }

                const auto& tokens = *tokens_expected;
                nlohmann::json response_body_json = {
                    {"access_token", tokens.accessToken},
                    {"refresh_token", tokens.refreshToken},
                    {"token_type", "Bearer"}
                };

                response_struct.statusCode = 200;
                response_struct.headers["Content-Type"] = "application/json";
                response_struct.body = response_body_json.dump();

                if (this->corsConfig_.enabled) {
                    response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowedOrigins.empty() ? "*" : this->corsConfig_.allowedOrigins[0];
                     if (this->corsConfig_.allowCredentials) {
                        response_struct.headers["Access-Control-Allow-Credentials"] = "true";
                    }
                }
                return response_struct;

            } catch (const nlohmann::json::parse_error& e) {
                utils::Logger::error(std::format("JSON parse error in /api/auth/refresh: {}", e.what()));
                response_struct.statusCode = 400;
                response_struct.body = nlohmann::json({
                    {"error", "Bad Request"},
                    {"message", std::string("Invalid JSON format: ") + e.what()}
                }).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Invalid JSON: ") + e.what());
            } catch (const std::exception& e) {
                utils::Logger::error(std::format("Exception in /api/auth/refresh: {}", e.what()));
                response_struct.statusCode = 500;
                response_struct.body = nlohmann::json({
                    {"error", "Internal Server Error"},
                    {"message", e.what()}
                }).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Exception: ") + e.what());
            }
        }
    );

    // User info
    this->addRoute("GET", "/api/auth/user",
        [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
            Response response_struct;
            try {
                auto auth_header_it = request.headers.find("Authorization");
                if (auth_header_it == request.headers.end()) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Authentication required. Missing Authorization header."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing Authorization header");
                }

                const std::string& auth_header = auth_header_it->second;
                if (auth_header.rfind("Bearer ", 0) != 0) { // Check if starts with "Bearer "
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Invalid authentication format. Expected Bearer token."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Invalid token format, not Bearer");
                }

                std::string token = auth_header.substr(7); // Extract token part
                auto validation_result = securityManager_->validateToken(token);
                if (!validation_result) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", validation_result.error().message}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(validation_result.error().message);
                }

                // Token is valid, username is in validation_result.value()
                auto user_info_expected = securityManager_->getUserInfo(*validation_result);
                if (!user_info_expected) {
                    // This case might indicate an internal issue if a validated token doesn't have user info
                    response_struct.statusCode = 500;
                    response_struct.body = nlohmann::json({
                        {"error", "Internal Server Error"},
                        {"message", user_info_expected.error().message}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Failed to retrieve user info: " + user_info_expected.error().message);
                }

                const auto& user_info_map = *user_info_expected;
                nlohmann::json response_body_json = {
                    {"username", user_info_map.count("username") ? user_info_map.at("username") : ""},
                    {"email", user_info_map.count("email") ? user_info_map.at("email") : ""},
                    {"is_admin", user_info_map.count("is_admin") ? (user_info_map.at("is_admin") == "t" || user_info_map.at("is_admin") == "true") : false},
                    {"created_at", user_info_map.count("created_at") ? user_info_map.at("created_at") : ""}
                };

                response_struct.statusCode = 200;
                response_struct.headers["Content-Type"] = "application/json";
                response_struct.body = response_body_json.dump();

                if (this->corsConfig_.enabled) {
                    response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowedOrigins.empty() ? "*" : this->corsConfig_.allowedOrigins[0];
                     if (this->corsConfig_.allowCredentials) {
                        response_struct.headers["Access-Control-Allow-Credentials"] = "true";
                    }
                }
                return response_struct;

            } catch (const std::exception& e) {
                utils::Logger::error(std::format("Exception in /api/auth/user: {}", e.what()));
                response_struct.statusCode = 500;
                response_struct.body = nlohmann::json({
                    {"error", "Internal Server Error"},
                    {"message", e.what()}
                }).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Exception: ") + e.what());
            }
        }
    );

    // Logout
    this->addRoute("POST", "/api/auth/logout",
        [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
            Response response_struct;
            try {
                auto auth_header_it = request.headers.find("Authorization");
                if (auth_header_it == request.headers.end()) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Authentication required. Missing Authorization header."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing Authorization header");
                }

                const std::string& auth_header = auth_header_it->second;
                if (auth_header.rfind("Bearer ", 0) != 0) { // Check if starts with "Bearer "
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Invalid authentication format. Expected Bearer token."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Invalid token format, not Bearer");
                }

                std::string token = auth_header.substr(7); // Extract token part
                auto validation_result = securityManager_->validateToken(token);
                if (!validation_result) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", validation_result.error().message}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Token validation failed: " + validation_result.error().message);
                }

                // Token is valid, username is in validation_result.value()
                std::string username = *validation_result;
                auto invalidation_result = securityManager_->invalidateTokens(username);

                if (!invalidation_result) {
                    response_struct.statusCode = 500;
                    response_struct.body = nlohmann::json({
                        {"error", "Internal Server Error"},
                        {"message", invalidation_result.error().message}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Failed to invalidate tokens: " + invalidation_result.error().message);
                }

                response_struct.statusCode = 200;
                response_struct.headers["Content-Type"] = "application/json";
                response_struct.body = nlohmann::json({
                    {"message", "Logout successful"}
                }).dump();

                if (this->corsConfig_.enabled) {
                    response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowedOrigins.empty() ? "*" : this->corsConfig_.allowedOrigins[0];
                     if (this->corsConfig_.allowCredentials) {
                        response_struct.headers["Access-Control-Allow-Credentials"] = "true";
                    }
                }
                return response_struct;

            } catch (const std::exception& e) {
                utils::Logger::error(std::format("Exception in /api/auth/logout: {}", e.what()));
                response_struct.statusCode = 500;
                response_struct.body = nlohmann::json({
                    {"error", "Internal Server Error"},
                    {"message", e.what()}
                }).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Exception: ") + e.what());
            }
        }
    );

    // Credential management
    this->addRoute("POST", "/api/credentials/store",
        [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
            Response response_struct;
            try {
                // Validate token and check admin permission
                auto auth_header_it = request.headers.find("Authorization");
                if (auth_header_it == request.headers.end()) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Authentication required. Missing Authorization header."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing Authorization header");
                }

                const std::string& auth_header = auth_header_it->second;
                if (auth_header.rfind("Bearer ", 0) != 0) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Invalid authentication format. Expected Bearer token."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Invalid token format, not Bearer");
                }

                std::string token = auth_header.substr(7);
                auto validation_result = securityManager_->validateToken(token);
                if (!validation_result) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", validation_result.error().message}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Token validation failed: " + validation_result.error().message);
                }

                auto user_info_expected = securityManager_->getUserInfo(*validation_result);
                if (!user_info_expected) {
                    response_struct.statusCode = 500; // Or 401 if user info is critical for auth context
                    response_struct.body = nlohmann::json({
                        {"error", "Internal Server Error"},
                        {"message", "Failed to retrieve user information after token validation."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Failed to retrieve user info: " + user_info_expected.error().message);
                }
                const auto& user_info_map = *user_info_expected;
                bool is_admin = user_info_map.count("is_admin") && (user_info_map.at("is_admin") == "t" || user_info_map.at("is_admin") == "true");

                if (!is_admin) {
                    response_struct.statusCode = 403;
                    response_struct.body = nlohmann::json({
                        {"error", "Forbidden"},
                        {"message", "User does not have admin privileges to store credentials."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("User is not admin");
                }

                // Parse request body
                if (request.body.empty()) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({
                        {"error", "Bad Request"},
                        {"message", "Request body is empty"}
                        }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Request body is empty");
                }
                nlohmann::json request_json = nlohmann::json::parse(request.body);
                if (!request_json.contains("key") || !request_json.contains("value")) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({
                        {"error", "Bad Request"},
                        {"message", "'key' and 'value' are required in the request body."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing 'key' or 'value' in request body");
                }

                std::string key = request_json["key"].get<std::string>();
                std::string value = request_json["value"].get<std::string>();

                // Store credential using CredentialStore singleton
                auto& credential_store = security::CredentialStore::getInstance();
                // Assuming storeCredential might throw or return a more detailed error in future.
                // For now, it returns bool.
                if (!credential_store.storeCredential(key, value)) {
                    response_struct.statusCode = 500;
                    response_struct.body = nlohmann::json({
                        {"error", "Internal Server Error"},
                        {"message", "Failed to store credential."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("CredentialStore failed to store credential.");
                }

                response_struct.statusCode = 200;
                response_struct.headers["Content-Type"] = "application/json";
                response_struct.body = nlohmann::json({
                    {"message", "Credential stored successfully"}
                }).dump();

                if (this->corsConfig_.enabled) {
                    response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowedOrigins.empty() ? "*" : this->corsConfig_.allowedOrigins[0];
                    if (this->corsConfig_.allowCredentials) {
                        response_struct.headers["Access-Control-Allow-Credentials"] = "true";
                    }
                }
                return response_struct;

            } catch (const nlohmann::json::parse_error& e) {
                utils::Logger::error(std::format("JSON parse error in /api/credentials/store: {}", e.what()));
                response_struct.statusCode = 400;
                response_struct.body = nlohmann::json({
                    {"error", "Bad Request"},
                    {"message", std::string("Invalid JSON format: ") + e.what()}
                }).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Invalid JSON: ") + e.what());
            } catch (const std::exception& e) {
                utils::Logger::error(std::format("Exception in /api/credentials/store: {}", e.what()));
                response_struct.statusCode = 500;
                response_struct.body = nlohmann::json({
                    {"error", "Internal Server Error"},
                    {"message", e.what()}
                }).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Exception: ") + e.what());
            }
        }
    );

    this->addRoute("GET", "/api/credentials/get",
        [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
            Response response_struct;
            try {
                // Validate token and check admin permission
                auto auth_header_it = request.headers.find("Authorization");
                if (auth_header_it == request.headers.end()) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Authentication required. Missing Authorization header."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing Authorization header");
                }

                const std::string& auth_header = auth_header_it->second;
                if (auth_header.rfind("Bearer ", 0) != 0) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Invalid authentication format. Expected Bearer token."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Invalid token format, not Bearer");
                }

                std::string token = auth_header.substr(7);
                auto validation_result = securityManager_->validateToken(token);
                if (!validation_result) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", validation_result.error().message}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Token validation failed: " + validation_result.error().message);
                }

                auto user_info_expected = securityManager_->getUserInfo(*validation_result);
                if (!user_info_expected) {
                    response_struct.statusCode = 500;
                    response_struct.body = nlohmann::json({
                        {"error", "Internal Server Error"},
                        {"message", "Failed to retrieve user information after token validation."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Failed to retrieve user info: " + user_info_expected.error().message);
                }
                const auto& user_info_map = *user_info_expected;
                bool is_admin = user_info_map.count("is_admin") && (user_info_map.at("is_admin") == "t" || user_info_map.at("is_admin") == "true");

                if (!is_admin) {
                    response_struct.statusCode = 403;
                    response_struct.body = nlohmann::json({
                        {"error", "Forbidden"},
                        {"message", "User does not have admin privileges to get credentials."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("User is not admin");
                }

                // Parse query parameters from request path
                std::string key_to_find;
                std::string queryString;
                size_t queryPos = request.path.find('?');
                if (queryPos != std::string::npos) {
                    queryString = request.path.substr(queryPos + 1);
                }
                auto query_params = parseQueryParams(queryString);
                auto key_it = query_params.find("key");
                if (key_it == query_params.end() || key_it->second.empty()) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({
                        {"error", "Bad Request"},
                        {"message", "'key' query parameter is required."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing 'key' query parameter");
                }
                key_to_find = key_it->second;

                // Get credential
                auto& credential_store = security::CredentialStore::getInstance();
                std::string value = credential_store.getCredential(key_to_find);

                if (value.empty()) { // Assuming empty string means not found
                    response_struct.statusCode = 404;
                    response_struct.body = nlohmann::json({
                        {"error", "Not Found"},
                        {"message", "Credential not found for the given key."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Credential not found");
                }

                response_struct.statusCode = 200;
                response_struct.headers["Content-Type"] = "application/json";
                response_struct.body = nlohmann::json({
                    {"key", key_to_find},
                    {"value", value}
                }).dump();

                if (this->corsConfig_.enabled) {
                    response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowedOrigins.empty() ? "*" : this->corsConfig_.allowedOrigins[0];
                    if (this->corsConfig_.allowCredentials) {
                        response_struct.headers["Access-Control-Allow-Credentials"] = "true";
                    }
                }
                return response_struct;

            } catch (const std::exception& e) {
                utils::Logger::error(std::format("Exception in /api/credentials/get: {}", e.what()));
                response_struct.statusCode = 500;
                response_struct.body = nlohmann::json({
                    {"error", "Internal Server Error"},
                    {"message", e.what()}
                }).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Exception: ") + e.what());
            }
        }
    );

    this->addRoute("DELETE", "/api/credentials/remove",
        [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
            Response response_struct;
            try {
                // Validate token and check admin permission
                auto auth_header_it = request.headers.find("Authorization");
                if (auth_header_it == request.headers.end()) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Authentication required. Missing Authorization header."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing Authorization header");
                }

                const std::string& auth_header = auth_header_it->second;
                if (auth_header.rfind("Bearer ", 0) != 0) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Invalid authentication format. Expected Bearer token."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Invalid token format, not Bearer");
                }

                std::string token = auth_header.substr(7);
                auto validation_result = securityManager_->validateToken(token);
                if (!validation_result) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", validation_result.error().message}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Token validation failed: " + validation_result.error().message);
                }

                auto user_info_expected = securityManager_->getUserInfo(*validation_result);
                 if (!user_info_expected) {
                    response_struct.statusCode = 500;
                    response_struct.body = nlohmann::json({
                        {"error", "Internal Server Error"},
                        {"message", "Failed to retrieve user information after token validation."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Failed to retrieve user info: " + user_info_expected.error().message);
                }
                const auto& user_info_map = *user_info_expected;
                bool is_admin = user_info_map.count("is_admin") && (user_info_map.at("is_admin") == "t" || user_info_map.at("is_admin") == "true");

                if (!is_admin) {
                    response_struct.statusCode = 403;
                    response_struct.body = nlohmann::json({
                        {"error", "Forbidden"},
                        {"message", "User does not have admin privileges to remove credentials."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("User is not admin");
                }

                // Parse query parameters from request path
                std::string key_to_remove;
                std::string queryString;
                size_t queryPos = request.path.find('?');
                if (queryPos != std::string::npos) {
                    queryString = request.path.substr(queryPos + 1);
                }
                auto query_params = parseQueryParams(queryString);
                auto key_it = query_params.find("key");
                if (key_it == query_params.end() || key_it->second.empty()) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({
                        {"error", "Bad Request"},
                        {"message", "'key' query parameter is required."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing 'key' query parameter");
                }
                key_to_remove = key_it->second;

                // Remove credential
                auto& credential_store = security::CredentialStore::getInstance();
                bool success = credential_store.removeCredential(key_to_remove);

                if (!success) {
                    response_struct.statusCode = 404; // Or 500 if failure is unexpected
                    response_struct.body = nlohmann::json({
                        {"error", "Not Found"},
                        {"message", "Credential not found or failed to remove."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Credential not found or failed to remove");
                }

                response_struct.statusCode = 200;
                response_struct.headers["Content-Type"] = "application/json";
                response_struct.body = nlohmann::json({
                    {"message", "Credential removed successfully"}
                }).dump();

                if (this->corsConfig_.enabled) {
                    response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowedOrigins.empty() ? "*" : this->corsConfig_.allowedOrigins[0];
                    if (this->corsConfig_.allowCredentials) {
                        response_struct.headers["Access-Control-Allow-Credentials"] = "true";
                    }
                }
                return response_struct;

            } catch (const std::exception& e) {
                utils::Logger::error(std::format("Exception in /api/credentials/remove: {}", e.what()));
                response_struct.statusCode = 500;
                response_struct.body = nlohmann::json({
                    {"error", "Internal Server Error"},
                    {"message", e.what()}
                }).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Exception: ") + e.what());
            }
        }
    );

    // Query execution
    this->addRoute("POST", "/api/query",
        [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
            Response response_struct;
            try {
                // 1. Token Validation
                auto auth_header_it = request.headers.find("Authorization");
                if (auth_header_it == request.headers.end()) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Authentication required. Missing Authorization header."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing Authorization header");
                }

                const std::string& auth_header = auth_header_it->second;
                if (auth_header.rfind("Bearer ", 0) != 0) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Invalid authentication format. Expected Bearer token."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Invalid token format, not Bearer");
                }

                std::string token = auth_header.substr(7);
                auto validation_result = securityManager_->validateToken(token);
                if (!validation_result) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", validation_result.error().message}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Token validation failed: " + validation_result.error().message);
                }
                // Token is valid, proceed. Username from *validation_result can be used for logging/auditing if needed.

                // 2. Parse request body
                if (request.body.empty()) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({
                        {"error", "Bad Request"},
                        {"message", "Request body is empty."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Request body is empty");
                }

                nlohmann::json request_json;
                try {
                    request_json = nlohmann::json::parse(request.body);
                } catch (const nlohmann::json::parse_error& e) {
                    utils::Logger::error(std::format("JSON parse error in /api/query: {}", e.what()));
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({
                        {"error", "Bad Request"},
                        {"message", std::string("Invalid JSON format: ") + e.what()}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(std::string("Invalid JSON: ") + e.what());
                }

                // 3. Extract query and parameters
                if (!request_json.contains("query") || !request_json["query"].is_string()) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({
                        {"error", "Bad Request"},
                        {"message", "'query' field (string) is required in the request body."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing or invalid 'query' field");
                }
                std::string query_str = request_json["query"].get<std::string>();

                std::vector<std::string> params_vec;
                if (request_json.contains("params")) {
                    if (!request_json["params"].is_array()) {
                        response_struct.statusCode = 400;
                        response_struct.body = nlohmann::json({
                            {"error", "Bad Request"},
                            {"message", "'params' field must be an array if provided."}
                        }).dump();
                        response_struct.headers["Content-Type"] = "application/json";
                        return std::unexpected("'params' field is not an array");
                    }
                    for (const auto& param_item : request_json["params"]) {
                        if (!param_item.is_string()) {
                             // Attempt to dump non-string params, or enforce string type strictly
                            utils::Logger::warning("/api/query: a parameter was not a string, attempting dump.");
                            params_vec.push_back(param_item.dump()); 
                        } else {
                            params_vec.push_back(param_item.get<std::string>());
                        }
                    }
                }

                // 4. Execute query using ConnectionManager
                auto db_result_expected = connectionManager_->executeQuery(query_str, params_vec);

                if (!db_result_expected) {
                    utils::Logger::error(std::format("Database query error in /api/query: {}", db_result_expected.error()));
                    response_struct.statusCode = 500; // Or map DatabaseError type to HTTP status
                    nlohmann::json error_json;
                    error_json["error"] = "Database Query Failed";
                    error_json["message"] = db_result_expected.error();
                    response_struct.body = error_json.dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Database query execution failed: " + db_result_expected.error());
                }

                // 5. Format successful response
                const auto& result_data = *db_result_expected;
                nlohmann::json response_body_json = {
                    {"rows_affected", result_data.size()}, // Changed from "rows" to "rows_affected" for clarity with SELECT vs other DML
                    {"data", nlohmann::json::array()}
                };
                for (const auto& row_map : result_data) {
                    response_body_json["data"].push_back(row_map);
                }

                response_struct.statusCode = 200;
                response_struct.headers["Content-Type"] = "application/json";
                response_struct.body = response_body_json.dump();

                if (this->corsConfig_.enabled) {
                    response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowedOrigins.empty() ? "*" : this->corsConfig_.allowedOrigins[0];
                    if (this->corsConfig_.allowCredentials) {
                        response_struct.headers["Access-Control-Allow-Credentials"] = "true";
                    }
                }
                return response_struct;

            } catch (const std::exception& e) {
                utils::Logger::error(std::format("Generic exception in /api/query: {}", e.what()));
                response_struct.statusCode = 500;
                response_struct.body = nlohmann::json({
                    {"error", "Internal Server Error"},
                    {"message", e.what()}
                }).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Exception: ") + e.what());
            }
        }
    );

    // Non-query execution
    this->addRoute("POST", "/api/execute",
        [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
            Response response_struct;
            try {
                // 1. Token Validation
                auto auth_header_it = request.headers.find("Authorization");
                if (auth_header_it == request.headers.end()) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Authentication required. Missing Authorization header."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing Authorization header");
                }

                const std::string& auth_header = auth_header_it->second;
                if (auth_header.rfind("Bearer ", 0) != 0) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", "Invalid authentication format. Expected Bearer token."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Invalid token format, not Bearer");
                }

                std::string token = auth_header.substr(7);
                auto validation_result = securityManager_->validateToken(token);
                if (!validation_result) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({
                        {"error", "Unauthorized"},
                        {"message", validation_result.error().message}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Token validation failed: " + validation_result.error().message);
                }
                // Token is valid. Username from *validation_result can be used for logging/auditing.

                // 2. Parse request body
                if (request.body.empty()) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({
                        {"error", "Bad Request"},
                        {"message", "Request body is empty."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Request body is empty");
                }

                nlohmann::json request_json;
                try {
                    request_json = nlohmann::json::parse(request.body);
                } catch (const nlohmann::json::parse_error& e) {
                    utils::Logger::error(std::format("JSON parse error in /api/execute: {}", e.what()));
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({
                        {"error", "Bad Request"},
                        {"message", std::string("Invalid JSON format: ") + e.what()}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(std::string("Invalid JSON: ") + e.what());
                }

                // 3. Extract statement and parameters
                if (!request_json.contains("statement") || !request_json["statement"].is_string()) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({
                        {"error", "Bad Request"},
                        {"message", "'statement' field (string) is required in the request body."}
                    }).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing or invalid 'statement' field");
                }
                std::string statement_str = request_json["statement"].get<std::string>();

                std::vector<std::string> params_vec;
                if (request_json.contains("params")) {
                    if (!request_json["params"].is_array()) {
                        response_struct.statusCode = 400;
                        response_struct.body = nlohmann::json({
                            {"error", "Bad Request"},
                            {"message", "'params' field must be an array if provided."}
                        }).dump();
                        response_struct.headers["Content-Type"] = "application/json";
                        return std::unexpected("'params' field is not an array");
                    }
                    for (const auto& param_item : request_json["params"]) {
                         if (!param_item.is_string()) {
                            utils::Logger::warning("/api/execute: a parameter was not a string, attempting dump.");
                            params_vec.push_back(param_item.dump()); 
                        } else {
                            params_vec.push_back(param_item.get<std::string>());
                        }
                    }
                }

                // 4. Execute non-query using ConnectionManager
                auto db_result_expected = connectionManager_->executeNonQuery(statement_str, params_vec);

                if (!db_result_expected) {
                    utils::Logger::error(std::format("Database non-query error in /api/execute: {}", db_result_expected.error()));
                    response_struct.statusCode = 500; // Or map DatabaseError type to HTTP status
                    nlohmann::json error_json;
                    error_json["error"] = "Database Execution Failed";
                    error_json["message"] = db_result_expected.error();
                    response_struct.body = error_json.dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Database non-query execution failed: " + db_result_expected.error());
                }

                // 5. Format successful response
                int affected_rows = *db_result_expected;
                response_struct.statusCode = 200;
                response_struct.headers["Content-Type"] = "application/json";
                response_struct.body = nlohmann::json({
                    {"affected_rows", affected_rows}
                }).dump();

                if (this->corsConfig_.enabled) {
                    response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowedOrigins.empty() ? "*" : this->corsConfig_.allowedOrigins[0];
                    if (this->corsConfig_.allowCredentials) {
                        response_struct.headers["Access-Control-Allow-Credentials"] = "true";
                    }
                }
                return response_struct;

            } catch (const std::exception& e) {
                utils::Logger::error(std::format("Generic exception in /api/execute: {}", e.what()));
                response_struct.statusCode = 500;
                response_struct.body = nlohmann::json({
                    {"error", "Internal Server Error"},
                    {"message", e.what()}
                }).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Exception: ") + e.what());
            }
        }
    );

    // Database Management: List Databases
    this->addRoute("GET", "/api/databases",
        [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
            Response response_struct;
            try {
                auto auth_header_it = request.headers.find("Authorization");
                if (auth_header_it == request.headers.end()) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", "Authentication required. Missing Authorization header."}}).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing Authorization header");
                }

                const std::string& auth_header = auth_header_it->second;
                if (auth_header.rfind("Bearer ", 0) != 0) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", "Invalid authentication format. Expected Bearer token."}}).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Invalid token format, not Bearer");
                }

                std::string token = auth_header.substr(7);
                auto validation_result = securityManager_->validateToken(token);
                if (!validation_result) {
                    response_struct.statusCode = 401;
                    response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", validation_result.error().message}}).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Token validation failed: " + validation_result.error().message);
                }

                auto user_info_expected = securityManager_->getUserInfo(*validation_result);
                if (!user_info_expected) {
                    response_struct.statusCode = 500;
                    response_struct.body = nlohmann::json({{"error", "Internal Server Error"}, {"message", "Failed to retrieve user information after token validation."}}).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Failed to retrieve user info: " + user_info_expected.error().message);
                }
                const auto& user_info_map = *user_info_expected;
                bool is_admin = user_info_map.count("is_admin") && (user_info_map.at("is_admin") == "t" || user_info_map.at("is_admin") == "true");

                if (!is_admin) {
                    response_struct.statusCode = 403;
                    response_struct.body = nlohmann::json({{"error", "Forbidden"}, {"message", "User does not have admin privileges to list databases."}}).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("User is not admin");
                }

                // TODO: Call connectionManager_->listDatabases()
                // auto dbs_expected = connectionManager_->listDatabases();
                // if (!dbs_expected) { /* handle error */ }
                // response_struct.body = nlohmann::json({{"databases", *dbs_expected}}).dump();

                response_struct.statusCode = 501; // Not Implemented
                response_struct.headers["Content-Type"] = "application/json";
                response_struct.body = nlohmann::json({{"message", "List databases endpoint not fully implemented yet."}}).dump();

                if (this->corsConfig_.enabled) {
                    response_struct.headers["Access-Control-Allow-Origin"] = this->corsConfig_.allowedOrigins.empty() ? "*" : this->corsConfig_.allowedOrigins[0];
                    if (this->corsConfig_.allowCredentials) {
                        response_struct.headers["Access-Control-Allow-Credentials"] = "true";
                    }
                }
                return response_struct;

            } catch (const std::exception& e) {
                utils::Logger::error(std::format("Exception in GET /api/databases: {}", e.what()));
                response_struct.statusCode = 500;
                response_struct.body = nlohmann::json({{"error", "Internal Server Error"}, {"message", e.what()}}).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Exception: ") + e.what());
            }
        }
    );

    // Database Management: Create Database
    this->addRoute("POST", "/api/databases",
        [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
            Response response_struct;
            try {
                auto auth_header_it = request.headers.find("Authorization");
                if (auth_header_it == request.headers.end()) { /* ... 401 ... */ 
                    response_struct.statusCode = 401; 
                    response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", "Missing Authorization header"}}).dump();
                    response_struct.headers["Content-Type"] = "application/json"; 
                    return std::unexpected("Missing Authorization header"); }
                const std::string& auth_header = auth_header_it->second;
                if (auth_header.rfind("Bearer ", 0) != 0) { /* ... 401 ... */ 
                    response_struct.statusCode = 401; 
                    response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", "Invalid token format"}}).dump(); 
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Invalid token format"); }
                std::string token = auth_header.substr(7);
                auto validation_result = securityManager_->validateToken(token);
                if (!validation_result) { /* ... 401 ... */ 
                    response_struct.statusCode = 401; 
                    response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", validation_result.error().message}}).dump(); 
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(validation_result.error().message); }
                auto user_info_expected = securityManager_->getUserInfo(*validation_result);
                if (!user_info_expected) { /* ... 500 ... */ 
                    response_struct.statusCode = 500; 
                    response_struct.body = nlohmann::json({{"error", "Server Error"}, {"message", user_info_expected.error().message}}).dump(); 
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(user_info_expected.error().message); }
                const auto& user_info_map = *user_info_expected;
                bool is_admin = user_info_map.count("is_admin") && (user_info_map.at("is_admin") == "t" || user_info_map.at("is_admin") == "true");
                if (!is_admin) { /* ... 403 ... */ 
                    response_struct.statusCode = 403; 
                    response_struct.body = nlohmann::json({{"error", "Forbidden"}, {"message", "Admin privileges required"}}).dump(); 
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Admin privileges required"); }

                if (request.body.empty()) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({{"error", "Bad Request"}, {"message", "Request body is empty."}}).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Request body is empty");
                }
                nlohmann::json request_json;
                try {
                    request_json = nlohmann::json::parse(request.body);
                } catch (const nlohmann::json::parse_error& e) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({{"error", "Bad Request"}, {"message", std::string("Invalid JSON format: ") + e.what()}}).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(std::string("Invalid JSON: ") + e.what());
                }

                if (!request_json.contains("database_name") || !request_json["database_name"].is_string()) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({{"error", "Bad Request"}, {"message", "'database_name' (string) is required in request body."}}).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing or invalid 'database_name' field");
                }
                std::string db_name = request_json["database_name"].get<std::string>();

                // TODO: Call connectionManager_->createDatabase(db_name)
                // auto create_expected = connectionManager_->createDatabase(db_name);
                // if (!create_expected) { /* handle error */ }
                // response_struct.body = nlohmann::json({{"message", "Database '" + db_name + "' created successfully."}}).dump();

                response_struct.statusCode = 501; // Not Implemented
                response_struct.headers["Content-Type"] = "application/json";
                response_struct.body = nlohmann::json({{"message", "Create database endpoint not fully implemented yet."}}).dump();

                if (this->corsConfig_.enabled) { /* ... CORS ... */ }
                return response_struct;

            } catch (const std::exception& e) { /* ... 500 ... */ 
                 utils::Logger::error(std::format("Exception in POST /api/databases: {}", e.what()));
                response_struct.statusCode = 500;
                response_struct.body = nlohmann::json({{"error", "Internal Server Error"}, {"message", e.what()}}).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Exception: ") + e.what());
            }
        }
    );

    // Database Management: Drop Database
    this->addRoute("DELETE", "/api/databases",
        [this](const ParsedRequest& request) -> std::expected<Response, std::string> {
            Response response_struct;
            try {
                auto auth_header_it = request.headers.find("Authorization");
                if (auth_header_it == request.headers.end()) { /* ... 401 ... */ 
                    response_struct.statusCode = 401; 
                    response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", "Missing Authorization header"}}).dump();
                    response_struct.headers["Content-Type"] = "application/json"; 
                    return std::unexpected("Missing Authorization header"); }
                const std::string& auth_header = auth_header_it->second;
                if (auth_header.rfind("Bearer ", 0) != 0) { /* ... 401 ... */ 
                    response_struct.statusCode = 401; 
                    response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", "Invalid token format"}}).dump(); 
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Invalid token format"); }
                std::string token = auth_header.substr(7);
                auto validation_result = securityManager_->validateToken(token);
                if (!validation_result) { /* ... 401 ... */ 
                    response_struct.statusCode = 401; 
                    response_struct.body = nlohmann::json({{"error", "Unauthorized"}, {"message", validation_result.error().message}}).dump(); 
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(validation_result.error().message); }
                auto user_info_expected = securityManager_->getUserInfo(*validation_result);
                if (!user_info_expected) { /* ... 500 ... */ 
                    response_struct.statusCode = 500; 
                    response_struct.body = nlohmann::json({{"error", "Server Error"}, {"message", user_info_expected.error().message}}).dump(); 
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(user_info_expected.error().message); }
                const auto& user_info_map = *user_info_expected;
                bool is_admin = user_info_map.count("is_admin") && (user_info_map.at("is_admin") == "t" || user_info_map.at("is_admin") == "true");
                if (!is_admin) { /* ... 403 ... */ 
                    response_struct.statusCode = 403; 
                    response_struct.body = nlohmann::json({{"error", "Forbidden"}, {"message", "Admin privileges required"}}).dump(); 
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Admin privileges required"); }

                if (request.body.empty()) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({{"error", "Bad Request"}, {"message", "Request body is empty."}}).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Request body is empty");
                }
                nlohmann::json request_json;
                try {
                    request_json = nlohmann::json::parse(request.body);
                } catch (const nlohmann::json::parse_error& e) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({{"error", "Bad Request"}, {"message", std::string("Invalid JSON format: ") + e.what()}}).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected(std::string("Invalid JSON: ") + e.what());
                }

                if (!request_json.contains("database_name") || !request_json["database_name"].is_string()) {
                    response_struct.statusCode = 400;
                    response_struct.body = nlohmann::json({{"error", "Bad Request"}, {"message", "'database_name' (string) is required in request body for deletion."}}).dump();
                    response_struct.headers["Content-Type"] = "application/json";
                    return std::unexpected("Missing or invalid 'database_name' field for deletion");
                }
                std::string db_name = request_json["database_name"].get<std::string>();

                // TODO: Call connectionManager_->dropDatabase(db_name)
                // auto drop_expected = connectionManager_->dropDatabase(db_name);
                // if (!drop_expected) { /* handle error */ }
                // response_struct.body = nlohmann::json({{"message", "Database '" + db_name + "' dropped successfully."}}).dump();

                response_struct.statusCode = 501; // Not Implemented
                response_struct.headers["Content-Type"] = "application/json";
                response_struct.body = nlohmann::json({{"message", "Drop database endpoint not fully implemented yet."}}).dump();

                if (this->corsConfig_.enabled) { /* ... CORS ... */ }
                return response_struct;

            } catch (const std::exception& e) { /* ... 500 ... */ 
                utils::Logger::error(std::format("Exception in DELETE /api/databases: {}", e.what()));
                response_struct.statusCode = 500;
                response_struct.body = nlohmann::json({{"error", "Internal Server Error"}, {"message", e.what()}}).dump();
                response_struct.headers["Content-Type"] = "application/json";
                return std::unexpected(std::string("Exception: ") + e.what());
            }
        }
    );
}

std::expected<ParsedRequest, std::string> ApiServer::parseRequest(const std::string& request) {
    ParsedRequest parsedRequest;
    std::istringstream stream(request);
    std::string line;

    // Parse request line
    if (!std::getline(stream, line) || line.empty()) {
        return std::unexpected("Empty request line");
    }

    std::istringstream requestLineStream(line);
    requestLineStream >> parsedRequest.method >> parsedRequest.path;

    // Parse headers
    while (std::getline(stream, line) && line != "\r") {
        size_t colonPos = line.find(':');
        if (colonPos != std::string::npos) {
            std::string name = line.substr(0, colonPos);
            std::string value = line.substr(colonPos + 2); // Skip ": "
            value.erase(value.find_last_not_of("\r\n") + 1);
            parsedRequest.headers[name] = value;
        }
    }

    // Parse body
    auto it = parsedRequest.headers.find("Content-Length");
    if (it != parsedRequest.headers.end()) {
        try {
            size_t contentLength = std::stoul(it->second);
            if (contentLength > 0) {
                std::string bodyContent(contentLength, '\0');
                stream.read(&bodyContent[0], contentLength);
                parsedRequest.body = bodyContent;
            }
        } catch (const std::exception& e) {
            return std::unexpected(std::string("Invalid Content-Length: ") + e.what());
        }
    }

    return parsedRequest;
}


Response ApiServer::createSuccessResponse(const nlohmann::json& data) {
    Response response;
    response.headers["Content-Type"] = "application/json";
    response.body = nlohmann::json{
        {"success", true},
        {"data", data}
    }.dump();
    return response;
}

Response ApiServer::createSuccessResponseWithHeaders(const nlohmann::json& data) {
    Response response;
    response.headers["Content-Type"] = "application/json";
    response.body = nlohmann::json{
        {"success", true},
        {"data", data}
    }.dump();
    return response;
}

Response ApiServer::createErrorResponse(
    int statusCode, const std::string& statusText, const std::string& message) {
    Response response;
    response.headers["Content-Type"] = "application/json";
    response.headers["Status"] = std::to_string(statusCode) + " " + statusText;
    response.body = nlohmann::json{
        {"success", false},
        {"error", {
            {"code", statusCode},
            {"message", message}
        }}
    }.dump();
    return response;
}

std::unordered_map<std::string, std::string> ApiServer::parseQueryParams(std::string_view queryString) {
    std::unordered_map<std::string, std::string> params;

    if (queryString.empty()) {
        return params;
    }

    std::istringstream iss{std::string(queryString)};
    std::string pair;

    while (std::getline(iss, pair, '&')) {
        size_t pos = pair.find('=');
        if (pos != std::string::npos) {
            std::string key = pair.substr(0, pos);
            std::string value = pair.substr(pos + 1);

            // URL decode
            key = urlDecode(key);
            value = urlDecode(value);

            params[key] = value;
        }
    }

    return params;
}

std::string ApiServer::formatHttpResponse(const Response& response) {
    std::ostringstream httpResponse;

    // Status line
    int statusCode = response.statusCode > 0 ? response.statusCode : 200;
    std::string statusText = "OK";
    if (statusCode == 400) statusText = "Bad Request";
    else if (statusCode == 401) statusText = "Unauthorized";
    else if (statusCode == 403) statusText = "Forbidden";
    else if (statusCode == 404) statusText = "Not Found";
    else if (statusCode == 500) statusText = "Internal Server Error";

    httpResponse << "HTTP/1.1 " << statusCode << " " << statusText << "\r\n";

    // Headers
    for (const auto& [name, value] : response.headers) {
        httpResponse << name << ": " << value << "\r\n";
    }

    // Add content length if not present
    if (response.headers.find("Content-Length") == response.headers.end()) {
        httpResponse << "Content-Length: " << response.body.length() << "\r\n";
    }

    // Add content type if not present
    if (response.headers.find("Content-Type") == response.headers.end()) {
        httpResponse << "Content-Type: application/json\r\n";
    }

    // Add connection close if not present
    if (response.headers.find("Connection") == response.headers.end()) {
        httpResponse << "Connection: close\r\n";
    }

    // End of headers
    httpResponse << "\r\n";

    // Body
    httpResponse << response.body;

    return httpResponse.str();
}

std::string ApiServer::urlDecode(std::string_view input) {
    std::string result;
    result.reserve(input.size());

    for (size_t i = 0; i < input.size(); ++i) {
        if (input[i] == '%' && i + 2 < input.size()) {
            int value;
            std::istringstream iss(std::string(input.substr(i + 1, 2)));
            if (iss >> std::hex >> value) {
                result += static_cast<char>(value);
                i += 2;
            } else {
                result += input[i];
            }
        } else if (input[i] == '+') {
            result += ' ';
        } else {
            result += input[i];
        }
    }

    return result;
}

} // namespace dbservice::api
