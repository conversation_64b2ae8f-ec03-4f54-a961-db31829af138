#pragma once

#include "database-service/api/api_server.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/security/security_manager.hpp"
#include "database-service/security/credential_store.hpp"
#include <memory>
#include <expected>
#include <string>

// Force file size change to ensure transfer

// Forward declarations
namespace dbservice::service {
    class ApplicationManager;
    class DatabaseInstanceManager;
}

namespace dbservice::security {
    class AuditLogger;
    class RateLimiter;
}

namespace dbservice::api {

/**
 * @class RouteController
 * @brief Manages the registration of API routes
 */
class RouteController {
public:
    /**
     * @brief Constructor
     * @param connectionManager Shared pointer to the ConnectionManager
     * @param securityManager Shared pointer to the SecurityManager
     */
    RouteController(std::shared_ptr<core::ConnectionManager> connectionManager,
                    std::shared_ptr<security::SecurityManager> securityManager);

    /**
     * @brief Registers all API routes with the server
     * @param server The ApiServer instance
     */
    void registerRoutes(ApiServer& server);

private:
    std::shared_ptr<core::ConnectionManager> connectionManager_;
    std::shared_ptr<security::SecurityManager> securityManager_;
    std::shared_ptr<service::ApplicationManager> applicationManager_;
    std::shared_ptr<service::DatabaseInstanceManager> databaseInstanceManager_;
    std::shared_ptr<security::AuditLogger> auditLogger_;
    std::shared_ptr<security::RateLimiter> rateLimiter_;
    std::shared_ptr<security::CredentialStore> credentialStore_;

    // Route handlers
    std::expected<Response, std::string> handleHealthCheck(const ParsedRequest& request);
    std::expected<Response, std::string> handleLogin(const ParsedRequest& request);
    std::expected<Response, std::string> handleLogout(const ParsedRequest& request);
    std::expected<Response, std::string> handleQuery(const ParsedRequest& request);
    std::expected<Response, std::string> handleExecute(const ParsedRequest& request);
    std::expected<Response, std::string> handleTransaction(const ParsedRequest& request);
    std::expected<Response, std::string> handleDatabaseMetrics(const ParsedRequest& request);
    std::expected<Response, std::string> handleStoreCredentials(const ParsedRequest& request);
    std::expected<Response, std::string> handleGetCredentials(const ParsedRequest& request);
    std::expected<Response, std::string> handleRegisterApplication(const ParsedRequest& request);
    std::expected<Response, std::string> handleListApplications(const ParsedRequest& request);

    // Helper methods
    std::expected<int, std::string> validateAuthentication(const ParsedRequest& request);
    std::string getClientId(const ParsedRequest& request);
    std::string getClientIp(const ParsedRequest& request);
    Response createErrorResponse(int statusCode, const std::string& message, const std::string& details = "");
};

} // namespace dbservice::api
