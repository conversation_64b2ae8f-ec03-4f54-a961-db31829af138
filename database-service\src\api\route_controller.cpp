#include "database-service/api/route_controller.hpp"
#include "database-service/utils/logger.hpp"
#include <nlohmann/json.hpp>

namespace dbservice::api {

RouteController::RouteController(
    std::shared_ptr<core::ConnectionManager> connectionManager,
    std::shared_ptr<security::SecurityManager> securityManager)
    : connectionManager_(std::move(connectionManager)),
      securityManager_(std::move(securityManager)) {}

void RouteController::registerRoutes(ApiServer& server) {
    // Health check endpoint
    server.addRoute("GET", "/api/health", [this](const ParsedRequest& request) {
        return handleHealthCheck(request);
    });

    utils::Logger::info("Registered health check route");
}

std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
    utils::Logger::info("Handling health check request.");
    nlohmann::json json_body;
    json_body["status"] = "OK";
    json_body["message"] = "Database service is healthy.";

    Response response_struct; // Create an instance of the Response struct
    response_struct.statusCode = 200;
    response_struct.headers["Content-Type"] = "application/json";
    response_struct.body = json_body.dump();
    // TODO: Add actual health check logic, e.g., pinging the database
    return response_struct; // Return the struct, which will be wrapped by std::expected by the RouteHandler
}

} // namespace dbservice::api
