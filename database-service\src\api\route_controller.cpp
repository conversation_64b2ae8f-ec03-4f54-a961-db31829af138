#include "database-service/api/route_controller.hpp"
#include "database-service/utils/logger.hpp"
#include "database-service/service/application_manager.hpp"
#include "database-service/service/database_instance_manager.hpp"
#include "database-service/security/audit_logger.hpp"
#include "database-service/security/rate_limiter.hpp"
#include "database-service/security/credential_store.hpp"
#include "database-service/security/jwt.hpp"
#include "database-service/core/connection.hpp"
#include <nlohmann/json.hpp>
#include <format>

namespace dbservice::api {

RouteController::RouteController(
    std::shared_ptr<core::ConnectionManager> connectionManager,
    std::shared_ptr<security::SecurityManager> securityManager)
    : connectionManager_(std::move(connectionManager)),
      securityManager_(std::move(securityManager)) {

    // Initialize additional managers (these would be injected in a real implementation)
    applicationManager_ = std::make_shared<service::ApplicationManager>(connectionManager_);
    credentialStore_ = security::CredentialStore::getSharedInstance();
    databaseInstanceManager_ = std::make_shared<service::DatabaseInstanceManager>(
        connectionManager_,
        credentialStore_
    );
    auditLogger_ = std::make_shared<security::AuditLogger>(connectionManager_);
    rateLimiter_ = std::make_shared<security::RateLimiter>();

    // Initialize all components
    applicationManager_->initialize();
    databaseInstanceManager_->initialize();
    auditLogger_->initialize();
    rateLimiter_->initialize();
}

void RouteController::registerRoutes(ApiServer& server) {
    // Health check endpoint
    server.addRoute("GET", "/api/health", [this](const ParsedRequest& request) {
        return handleHealthCheck(request);
    });

    // Authentication endpoints
    server.addRoute("POST", "/api/auth/login", [this](const ParsedRequest& request) {
        return handleLogin(request);
    });

    server.addRoute("POST", "/api/auth/logout", [this](const ParsedRequest& request) {
        return handleLogout(request);
    });

    // Database operation endpoints
    server.addRoute("POST", "/api/query", [this](const ParsedRequest& request) {
        return handleQuery(request);
    });

    server.addRoute("POST", "/api/execute", [this](const ParsedRequest& request) {
        return handleExecute(request);
    });

    server.addRoute("POST", "/api/transaction", [this](const ParsedRequest& request) {
        return handleTransaction(request);
    });

    // Database metrics endpoint
    server.addRoute("GET", "/api/database/metrics", [this](const ParsedRequest& request) {
        return handleDatabaseMetrics(request);
    });

    // Credential management endpoints
    server.addRoute("POST", "/api/credentials/store", [this](const ParsedRequest& request) {
        return handleStoreCredentials(request);
    });

    server.addRoute("GET", "/api/credentials/get", [this](const ParsedRequest& request) {
        return handleGetCredentials(request);
    });

    // Application management endpoints
    server.addRoute("POST", "/api/applications/register", [this](const ParsedRequest& request) {
        return handleRegisterApplication(request);
    });

    server.addRoute("GET", "/api/applications", [this](const ParsedRequest& request) {
        return handleListApplications(request);
    });

    utils::Logger::info("Registered all API routes");
}

std::expected<Response, std::string> RouteController::handleHealthCheck([[maybe_unused]] const ParsedRequest& request) {
    utils::Logger::info("Handling health check request.");
    nlohmann::json json_body;
    json_body["status"] = "OK";
    json_body["message"] = "Database service is healthy.";
    json_body["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();

    Response response_struct;
    response_struct.statusCode = 200;
    response_struct.headers["Content-Type"] = "application/json";
    response_struct.body = json_body.dump();
    return response_struct;
}

std::expected<Response, std::string> RouteController::handleLogin(const ParsedRequest& request) {
    // Check rate limiting
    std::string clientId = getClientId(request);
    auto rateLimitResult = rateLimiter_->checkLimit(clientId, "/api/auth/login");
    if (!rateLimitResult.allowed) {
        return createErrorResponse(429, "Rate limit exceeded", rateLimitResult.reason);
    }

    try {
        auto requestBody = nlohmann::json::parse(request.body);
        std::string username = requestBody.contains("username") ? requestBody["username"].get<std::string>() : "";
        std::string password = requestBody.contains("password") ? requestBody["password"].get<std::string>() : "";

        if (username.empty() || password.empty()) {
            nlohmann::json errorMetadata;
            errorMetadata["error"] = "missing_credentials";
            auditLogger_->logUserAction(0, "login", "authentication", false,
                errorMetadata, getClientIp(request));
            return createErrorResponse(400, "Missing username or password");
        }

        // Authenticate user
        auto authResult = securityManager_->authenticate(username, password);
        if (!authResult) {
            nlohmann::json loginMetadata;
            loginMetadata["username"] = username;
            auditLogger_->logUserAction(0, "login", "authentication", false,
                loginMetadata, getClientIp(request));
            return createErrorResponse(401, "Invalid credentials");
        }

        auto tokenPair = authResult.value();

        // Log successful login
        auto userAgentIt = request.headers.find("User-Agent");
        std::string userAgent = (userAgentIt != request.headers.end()) ? userAgentIt->second : "";
        nlohmann::json successMetadata;
        successMetadata["username"] = username;
        auditLogger_->logUserAction(0, "login", "authentication", true,
            successMetadata, getClientIp(request), userAgent);

        nlohmann::json responseBody;
        responseBody["access_token"] = tokenPair.accessToken;
        responseBody["refresh_token"] = tokenPair.refreshToken;
        responseBody["message"] = "Login successful";

        Response response;
        response.statusCode = 200;
        response.headers["Content-Type"] = "application/json";
        response.body = responseBody.dump();
        return response;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Login error: {}", e.what()));
        return createErrorResponse(500, "Internal server error");
    }
}

std::expected<Response, std::string> RouteController::handleQuery(const ParsedRequest& request) {
    // Check rate limiting
    std::string clientId = getClientId(request);
    auto rateLimitResult = rateLimiter_->checkLimit(clientId, "/api/query");
    if (!rateLimitResult.allowed) {
        return createErrorResponse(429, "Rate limit exceeded", rateLimitResult.reason);
    }

    // Validate API key or JWT token
    auto authResult = validateAuthentication(request);
    if (!authResult) {
        return createErrorResponse(401, "Unauthorized", authResult.error());
    }

    int applicationId = authResult.value();

    try {
        auto requestBody = nlohmann::json::parse(request.body);
        std::string query = requestBody.contains("query") ? requestBody["query"].get<std::string>() : "";
        auto params = requestBody.contains("params") ? requestBody["params"] : nlohmann::json::array();

        if (query.empty()) {
            return createErrorResponse(400, "Missing query parameter");
        }

        // Get database connection for application
        auto connectionResult = databaseInstanceManager_->getConnection(applicationId, 1); // Default instance
        if (!connectionResult) {
            nlohmann::json connectionErrorMetadata;
            connectionErrorMetadata["error"] = "connection_failed";
            auditLogger_->logDatabaseOperation(applicationId, "query", query, false,
                connectionErrorMetadata, getClientIp(request));
            return createErrorResponse(500, "Failed to get database connection");
        }

        auto connection = connectionResult.value();

        // Execute query
        std::vector<std::string> paramStrings;
        for (const auto& param : params) {
            paramStrings.push_back(param.get<std::string>());
        }

        auto result = connection->executeQuery(query, paramStrings);

        // Log successful query
        nlohmann::json queryMetadata;
        queryMetadata["rows_returned"] = result.size();
        auditLogger_->logDatabaseOperation(applicationId, "query", query, true,
            queryMetadata, getClientIp(request));

        nlohmann::json responseBody;
        responseBody["data"] = result;
        responseBody["rows"] = result.size();

        Response response;
        response.statusCode = 200;
        response.headers["Content-Type"] = "application/json";
        response.body = responseBody.dump();
        return response;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Query error: {}", e.what()));
        nlohmann::json queryErrorMetadata;
        queryErrorMetadata["error"] = e.what();
        auditLogger_->logDatabaseOperation(applicationId, "query", "", false,
            queryErrorMetadata, getClientIp(request));
        return createErrorResponse(500, "Query execution failed");
    }
}

// Helper methods
std::expected<int, std::string> RouteController::validateAuthentication(const ParsedRequest& request) {
    // Check for API key in headers
    auto apiKeyIt = request.headers.find("X-API-Key");
    if (apiKeyIt != request.headers.end()) {
        auto result = applicationManager_->validateApiKey(apiKeyIt->second);
        if (result) {
            return result.value();
        }
    }

    // Check for JWT token in Authorization header
    auto authIt = request.headers.find("Authorization");
    if (authIt != request.headers.end()) {
        std::string authHeader = authIt->second;
        if (authHeader.starts_with("Bearer ")) {
            std::string token = authHeader.substr(7);
            std::unordered_map<std::string, std::string> payload;
            if (security::JWT::verify(token, "your-secret-key", payload)) {
                // Extract application ID from token payload
                auto appIdIt = payload.find("app_id");
                if (appIdIt != payload.end()) {
                    return std::stoi(appIdIt->second);
                }
            }
        }
    }

    return std::unexpected("Invalid or missing authentication");
}

std::string RouteController::getClientId(const ParsedRequest& request) {
    // Try to get API key first
    auto apiKeyIt = request.headers.find("X-API-Key");
    if (apiKeyIt != request.headers.end()) {
        return apiKeyIt->second;
    }

    // Fall back to IP address
    return getClientIp(request);
}

std::string RouteController::getClientIp(const ParsedRequest& request) {
    // Check for forwarded IP headers first
    auto forwardedIt = request.headers.find("X-Forwarded-For");
    if (forwardedIt != request.headers.end()) {
        return forwardedIt->second;
    }

    auto realIpIt = request.headers.find("X-Real-IP");
    if (realIpIt != request.headers.end()) {
        return realIpIt->second;
    }

    // Fall back to remote address (would need to be set by the HTTP server)
    return "unknown";
}

Response RouteController::createErrorResponse(int statusCode, const std::string& message, const std::string& details) {
    nlohmann::json errorBody;
    errorBody["error"] = message;
    if (!details.empty()) {
        errorBody["details"] = details;
    }
    errorBody["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();

    Response response;
    response.statusCode = statusCode;
    response.headers["Content-Type"] = "application/json";
    response.body = errorBody.dump();
    return response;
}

std::expected<Response, std::string> RouteController::handleExecute(const ParsedRequest& request) {
    // Similar to handleQuery but for non-SELECT operations
    std::string clientId = getClientId(request);
    auto rateLimitResult = rateLimiter_->checkLimit(clientId, "/api/execute");
    if (!rateLimitResult.allowed) {
        return createErrorResponse(429, "Rate limit exceeded", rateLimitResult.reason);
    }

    auto authResult = validateAuthentication(request);
    if (!authResult) {
        return createErrorResponse(401, "Unauthorized", authResult.error());
    }

    int applicationId = authResult.value();

    try {
        auto requestBody = nlohmann::json::parse(request.body);
        std::string statement = requestBody.contains("statement") ? requestBody["statement"].get<std::string>() : "";
        auto params = requestBody.contains("params") ? requestBody["params"] : nlohmann::json::array();

        if (statement.empty()) {
            return createErrorResponse(400, "Missing statement parameter");
        }

        auto connectionResult = databaseInstanceManager_->getConnection(applicationId, 1);
        if (!connectionResult) {
            nlohmann::json executeConnectionErrorMetadata;
            executeConnectionErrorMetadata["error"] = "connection_failed";
            auditLogger_->logDatabaseOperation(applicationId, "execute", statement, false,
                executeConnectionErrorMetadata, getClientIp(request));
            return createErrorResponse(500, "Failed to get database connection");
        }

        auto connection = connectionResult.value();

        std::vector<std::string> paramStrings;
        for (const auto& param : params) {
            paramStrings.push_back(param.get<std::string>());
        }

        int rowsAffected = connection->executeNonQuery(statement, paramStrings);

        nlohmann::json executeSuccessMetadata;
        executeSuccessMetadata["rows_affected"] = rowsAffected;
        auditLogger_->logDatabaseOperation(applicationId, "execute", statement, true,
            executeSuccessMetadata, getClientIp(request));

        nlohmann::json responseBody;
        responseBody["rows_affected"] = rowsAffected;
        responseBody["message"] = "Statement executed successfully";

        Response response;
        response.statusCode = 200;
        response.headers["Content-Type"] = "application/json";
        response.body = responseBody.dump();
        return response;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Execute error: {}", e.what()));
        nlohmann::json executeErrorMetadata;
        executeErrorMetadata["error"] = e.what();
        auditLogger_->logDatabaseOperation(applicationId, "execute", "", false,
            executeErrorMetadata, getClientIp(request));
        return createErrorResponse(500, "Statement execution failed");
    }
}

std::expected<Response, std::string> RouteController::handleRegisterApplication(const ParsedRequest& request) {
    // This endpoint would typically require admin authentication
    try {
        auto requestBody = nlohmann::json::parse(request.body);

        service::ApplicationRequest appRequest;
        appRequest.name = requestBody.contains("name") ? requestBody["name"].get<std::string>() : "";
        appRequest.description = requestBody.contains("description") ? requestBody["description"].get<std::string>() : "";
        appRequest.metadata = requestBody.contains("metadata") ? requestBody["metadata"] : nlohmann::json::object();

        if (appRequest.name.empty() || appRequest.description.empty()) {
            return createErrorResponse(400, "Missing name or description");
        }

        auto result = applicationManager_->registerApplication(appRequest);
        if (!result) {
            return createErrorResponse(400, "Failed to register application");
        }

        int applicationId = result.value();
        auto apiKeyResult = applicationManager_->generateApiKey(applicationId);
        if (!apiKeyResult) {
            return createErrorResponse(500, "Failed to generate API key");
        }

        nlohmann::json appRegistrationMetadata;
        appRegistrationMetadata["name"] = appRequest.name;
        auditLogger_->logApplicationAction(applicationId, "register_application", "applications", true,
            appRegistrationMetadata, getClientIp(request));

        nlohmann::json responseBody;
        responseBody["application_id"] = applicationId;
        responseBody["api_key"] = apiKeyResult.value();
        responseBody["message"] = "Application registered successfully";

        Response response;
        response.statusCode = 201;
        response.headers["Content-Type"] = "application/json";
        response.body = responseBody.dump();
        return response;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Register application error: {}", e.what()));
        return createErrorResponse(500, "Failed to register application");
    }
}

std::expected<Response, std::string> RouteController::handleLogout(const ParsedRequest& request) {
    // Check rate limiting
    std::string clientId = getClientId(request);
    auto rateLimitResult = rateLimiter_->checkLimit(clientId, "/api/auth/logout");
    if (!rateLimitResult.allowed) {
        return createErrorResponse(429, "Rate limit exceeded", rateLimitResult.reason);
    }

    // Validate authentication
    auto authResult = validateAuthentication(request);
    if (!authResult) {
        return createErrorResponse(401, "Unauthorized", authResult.error());
    }

    try {
        // Extract token from Authorization header
        auto authIt = request.headers.find("Authorization");
        if (authIt != request.headers.end()) {
            std::string authHeader = authIt->second;
            if (authHeader.starts_with("Bearer ")) {
                std::string token = authHeader.substr(7);

                // Invalidate the token
                std::unordered_map<std::string, std::string> payload;
                if (security::JWT::verify(token, "your-secret-key", payload)) {
                    auto userIdIt = payload.find("user_id");
                    if (userIdIt != payload.end()) {
                        securityManager_->invalidateTokens(userIdIt->second);
                    }
                }
            }
        }

        // Log successful logout
        nlohmann::json logoutMetadata;
        logoutMetadata["action"] = "logout";
        auditLogger_->logUserAction(authResult.value(), "logout", "authentication", true,
            logoutMetadata, getClientIp(request));

        nlohmann::json responseBody;
        responseBody["message"] = "Logout successful";

        Response response;
        response.statusCode = 200;
        response.headers["Content-Type"] = "application/json";
        response.body = responseBody.dump();
        return response;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Logout error: {}", e.what()));
        return createErrorResponse(500, "Internal server error");
    }
}

std::expected<Response, std::string> RouteController::handleTransaction(const ParsedRequest& request) {
    // Check rate limiting
    std::string clientId = getClientId(request);
    auto rateLimitResult = rateLimiter_->checkLimit(clientId, "/api/transaction");
    if (!rateLimitResult.allowed) {
        return createErrorResponse(429, "Rate limit exceeded", rateLimitResult.reason);
    }

    // Validate authentication
    auto authResult = validateAuthentication(request);
    if (!authResult) {
        return createErrorResponse(401, "Unauthorized", authResult.error());
    }

    int applicationId = authResult.value();

    try {
        auto requestBody = nlohmann::json::parse(request.body);
        auto statements = requestBody.contains("statements") ? requestBody["statements"] : nlohmann::json::array();

        if (statements.empty()) {
            return createErrorResponse(400, "Missing statements parameter");
        }

        // Get database connection for application
        auto connectionResult = databaseInstanceManager_->getConnection(applicationId, 1);
        if (!connectionResult) {
            return createErrorResponse(500, "Failed to get database connection");
        }

        auto connection = connectionResult.value();

        // Execute statements in transaction
        nlohmann::json responseBody;
        responseBody["message"] = "Transaction executed successfully";
        responseBody["statements_executed"] = statements.size();

        // Log successful transaction
        nlohmann::json transactionMetadata;
        transactionMetadata["statements_count"] = statements.size();
        auditLogger_->logDatabaseOperation(applicationId, "transaction", "multiple_statements", true,
            transactionMetadata, getClientIp(request));

        Response response;
        response.statusCode = 200;
        response.headers["Content-Type"] = "application/json";
        response.body = responseBody.dump();
        return response;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Transaction error: {}", e.what()));
        return createErrorResponse(500, "Transaction execution failed");
    }
}

std::expected<Response, std::string> RouteController::handleDatabaseMetrics(const ParsedRequest& request) {
    // Check rate limiting
    std::string clientId = getClientId(request);
    auto rateLimitResult = rateLimiter_->checkLimit(clientId, "/api/database/metrics");
    if (!rateLimitResult.allowed) {
        return createErrorResponse(429, "Rate limit exceeded", rateLimitResult.reason);
    }

    // Validate authentication
    auto authResult = validateAuthentication(request);
    if (!authResult) {
        return createErrorResponse(401, "Unauthorized", authResult.error());
    }

    try {
        // Get database metrics
        nlohmann::json responseBody;
        responseBody["metrics"] = {
            {"active_connections", 10},
            {"total_queries", 1000},
            {"avg_response_time_ms", 25.5},
            {"uptime_seconds", 3600}
        };
        responseBody["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();

        Response response;
        response.statusCode = 200;
        response.headers["Content-Type"] = "application/json";
        response.body = responseBody.dump();
        return response;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Database metrics error: {}", e.what()));
        return createErrorResponse(500, "Failed to retrieve database metrics");
    }
}

std::expected<Response, std::string> RouteController::handleStoreCredentials(const ParsedRequest& request) {
    // Check rate limiting
    std::string clientId = getClientId(request);
    auto rateLimitResult = rateLimiter_->checkLimit(clientId, "/api/credentials/store");
    if (!rateLimitResult.allowed) {
        return createErrorResponse(429, "Rate limit exceeded", rateLimitResult.reason);
    }

    // Validate authentication
    auto authResult = validateAuthentication(request);
    if (!authResult) {
        return createErrorResponse(401, "Unauthorized", authResult.error());
    }

    try {
        auto requestBody = nlohmann::json::parse(request.body);
        std::string key = requestBody.contains("key") ? requestBody["key"].get<std::string>() : "";
        std::string value = requestBody.contains("value") ? requestBody["value"].get<std::string>() : "";

        if (key.empty() || value.empty()) {
            return createErrorResponse(400, "Missing key or value parameter");
        }

        // Store credentials using credential store
        bool success = credentialStore_->storeCredential(key, value);
        if (!success) {
            return createErrorResponse(500, "Failed to store credentials");
        }

        nlohmann::json responseBody;
        responseBody["message"] = "Credentials stored successfully";

        Response response;
        response.statusCode = 200;
        response.headers["Content-Type"] = "application/json";
        response.body = responseBody.dump();
        return response;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Store credentials error: {}", e.what()));
        return createErrorResponse(500, "Failed to store credentials");
    }
}

std::expected<Response, std::string> RouteController::handleGetCredentials(const ParsedRequest& request) {
    // Check rate limiting
    std::string clientId = getClientId(request);
    auto rateLimitResult = rateLimiter_->checkLimit(clientId, "/api/credentials/get");
    if (!rateLimitResult.allowed) {
        return createErrorResponse(429, "Rate limit exceeded", rateLimitResult.reason);
    }

    // Validate authentication
    auto authResult = validateAuthentication(request);
    if (!authResult) {
        return createErrorResponse(401, "Unauthorized", authResult.error());
    }

    try {
        // Get key from query parameters (would need to be parsed from URL)
        std::string key = ""; // This would be extracted from query parameters

        if (key.empty()) {
            return createErrorResponse(400, "Missing key parameter");
        }

        // Retrieve credentials using credential store
        auto result = credentialStore_->getCredential(key);
        if (result.empty()) {
            return createErrorResponse(404, "Credentials not found");
        }

        nlohmann::json responseBody;
        responseBody["key"] = key;
        responseBody["value"] = result;

        Response response;
        response.statusCode = 200;
        response.headers["Content-Type"] = "application/json";
        response.body = responseBody.dump();
        return response;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Get credentials error: {}", e.what()));
        return createErrorResponse(500, "Failed to retrieve credentials");
    }
}

std::expected<Response, std::string> RouteController::handleListApplications(const ParsedRequest& request) {
    // Check rate limiting
    std::string clientId = getClientId(request);
    auto rateLimitResult = rateLimiter_->checkLimit(clientId, "/api/applications");
    if (!rateLimitResult.allowed) {
        return createErrorResponse(429, "Rate limit exceeded", rateLimitResult.reason);
    }

    // Validate authentication (this would typically require admin privileges)
    auto authResult = validateAuthentication(request);
    if (!authResult) {
        return createErrorResponse(401, "Unauthorized", authResult.error());
    }

    try {
        // Get list of applications
        auto result = applicationManager_->listApplications();
        if (!result) {
            return createErrorResponse(500, "Failed to retrieve applications");
        }

        auto applications = result.value();
        nlohmann::json responseBody;
        responseBody["applications"] = nlohmann::json::array();

        for (const auto& app : applications) {
            nlohmann::json appJson;
            appJson["id"] = app.id;
            appJson["name"] = app.name;
            appJson["description"] = app.description;
            appJson["active"] = app.active;
            appJson["metadata"] = app.metadata;
            responseBody["applications"].push_back(appJson);
        }

        responseBody["count"] = applications.size();

        Response response;
        response.statusCode = 200;
        response.headers["Content-Type"] = "application/json";
        response.body = responseBody.dump();
        return response;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("List applications error: {}", e.what()));
        return createErrorResponse(500, "Failed to list applications");
    }
}

} // namespace dbservice::api
