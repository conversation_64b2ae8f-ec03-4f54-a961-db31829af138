Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  5%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:22:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   22 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::parseQueryParams(std::string_view)’:
/home/<USER>/database-service-build/src/api/api_server.cpp:2188:27: warning: parentheses were disambiguated as a function declaration [-Wvexing-parse]
 2188 |     std::istringstream iss(std::string(queryString));
      |                           ^~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:2188:27: note: replace parentheses with braces to declare a variable
 2188 |     std::istringstream iss(std::string(queryString));
      |                           ^~~~~~~~~~~~~~~~~~~~~~~~~~
      |                           -
      |                           {                        -
      |                                                    }
/home/<USER>/database-service-build/src/api/api_server.cpp:2191:24: error: no matching function for call to ‘getline(std::istringstream (&)(std::string), std::string&, char)’
 2191 |     while (std::getline(iss, pair, '&')) {
      |            ~~~~~~~~~~~~^~~~~~~~~~~~~~~~
In file included from /usr/include/c++/14/string:55,
                 from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:2,
                 from /home/<USER>/database-service-build/src/api/api_server.cpp:1:
/usr/include/c++/14/bits/basic_string.tcc:907:5: note: candidate: ‘template<class _CharT, class _Traits, class _Alloc> std::basic_istream<_CharT, _Traits>& std::getline(basic_istream<_CharT, _Traits>&, __cxx11::basic_string<_CharT, _Traits, _Allocator>&, _CharT)’
  907 |     getline(basic_istream<_CharT, _Traits>& __in,
      |     ^~~~~~~
/usr/include/c++/14/bits/basic_string.tcc:907:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:2191:24: note:   mismatched types ‘std::basic_istream<_CharT, _Traits>’ and ‘std::istringstream(std::string)’ {aka ‘std::__cxx11::basic_istringstream<char>(std::__cxx11::basic_string<char>)’}
 2191 |     while (std::getline(iss, pair, '&')) {
      |            ~~~~~~~~~~~~^~~~~~~~~~~~~~~~
In file included from /usr/include/c++/14/string:54:
/usr/include/c++/14/bits/basic_string.h:4117:5: note: candidate: ‘template<class _CharT, class _Traits, class _Alloc> std::basic_istream<_CharT, _Traits>& std::getline(basic_istream<_CharT, _Traits>&, __cxx11::basic_string<_CharT, _Traits, _Allocator>&)’
 4117 |     getline(basic_istream<_CharT, _Traits>& __is,
      |     ^~~~~~~
/usr/include/c++/14/bits/basic_string.h:4117:5: note:   candidate expects 2 arguments, 3 provided
/usr/include/c++/14/bits/basic_string.h:4125:5: note: candidate: ‘template<class _CharT, class _Traits, class _Alloc> std::basic_istream<_CharT, _Traits>& std::getline(basic_istream<_CharT, _Traits>&&, __cxx11::basic_string<_CharT, _Traits, _Allocator>&, _CharT)’
 4125 |     getline(basic_istream<_CharT, _Traits>&& __is,
      |     ^~~~~~~
/usr/include/c++/14/bits/basic_string.h:4125:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:2191:24: note:   mismatched types ‘std::basic_istream<_CharT, _Traits>’ and ‘std::istringstream(std::string)’ {aka ‘std::__cxx11::basic_istringstream<char>(std::__cxx11::basic_string<char>)’}
 2191 |     while (std::getline(iss, pair, '&')) {
      |            ~~~~~~~~~~~~^~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/basic_string.h:4132:5: note: candidate: ‘template<class _CharT, class _Traits, class _Alloc> std::basic_istream<_CharT, _Traits>& std::getline(basic_istream<_CharT, _Traits>&&, __cxx11::basic_string<_CharT, _Traits, _Allocator>&)’
 4132 |     getline(basic_istream<_CharT, _Traits>&& __is,
      |     ^~~~~~~
/usr/include/c++/14/bits/basic_string.h:4132:5: note:   candidate expects 2 arguments, 3 provided
[ 16%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
make[2]: *** [CMakeFiles/database-service.dir/build.make:79: CMakeFiles/database-service.dir/src/api/api_server.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
