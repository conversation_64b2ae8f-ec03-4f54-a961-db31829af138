Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.6s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  4%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
[  9%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:91:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   91 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[ 13%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 18%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<std::shared_ptr<dbservice::core::Connection>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::getConnection(std::chrono::milliseconds)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:187:31: warning: unused parameter ‘timeout’ [-Wunused-parameter]
  187 |     std::chrono::milliseconds timeout) {
      |     ~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<nlohmann::json_abi_v3_11_3::basic_json<>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::executeCachedQuery(const std::string&, const std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > >&, std::chrono::milliseconds)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:328:31: warning: unused parameter ‘ttl’ [-Wunused-parameter]
  328 |     std::chrono::milliseconds ttl) {
      |     ~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<std::shared_ptr<dbservice::core::Transaction>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::beginTransaction(dbservice::core::TransactionIsolationLevel, bool, bool)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:370:31: warning: unused parameter ‘isolationLevel’ [-Wunused-parameter]
  370 |     TransactionIsolationLevel isolationLevel,
      |     ~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:371:10: warning: unused parameter ‘readOnly’ [-Wunused-parameter]
  371 |     bool readOnly,
      |     ~~~~~^~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:372:10: warning: unused parameter ‘deferrable’ [-Wunused-parameter]
  372 |     bool deferrable) {
      |     ~~~~~^~~~~~~~~~
[ 22%] Building CXX object CMakeFiles/database-service.dir/src/core/transaction.cpp.o
[ 27%] Building CXX object CMakeFiles/database-service.dir/src/database_service.cpp.o
[ 31%] Building CXX object CMakeFiles/database-service.dir/src/main.cpp.o
[ 36%] Building CXX object CMakeFiles/database-service.dir/src/metrics/database_metrics.cpp.o
[ 40%] Building CXX object CMakeFiles/database-service.dir/src/metrics/metrics_collector.cpp.o
[ 45%] Building CXX object CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o
[ 50%] Building CXX object CMakeFiles/database-service.dir/src/security/audit_logger.cpp.o
[ 54%] Building CXX object CMakeFiles/database-service.dir/src/security/credential_store.cpp.o
/home/<USER>/database-service-build/src/security/credential_store.cpp: In member function ‘bool dbservice::security::CredentialStore::initialize(const std::string&)’:
/home/<USER>/database-service-build/src/security/credential_store.cpp:46:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   46 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/credential_store.cpp:6:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:47:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   47 |     SHA256_Update(&sha256, encryptionKey.c_str(), encryptionKey.length());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/credential_store.cpp:48:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   48 |     SHA256_Final(key, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
[ 59%] Building CXX object CMakeFiles/database-service.dir/src/security/jwt.cpp.o
[ 63%] Building CXX object CMakeFiles/database-service.dir/src/security/rate_limiter.cpp.o
[ 68%] Building CXX object CMakeFiles/database-service.dir/src/security/security_manager.cpp.o
/home/<USER>/database-service-build/src/security/rate_limiter.cpp: In member function ‘dbservice::security::RateLimitResult dbservice::security::RateLimiter::checkLimit(const std::string&, const std::string&, const std::string&)’:
/home/<USER>/database-service-build/src/security/rate_limiter.cpp:62:24: warning: unused parameter ‘userAgent’ [-Wunused-parameter]
   62 |     const std::string& userAgent) {
      |     ~~~~~~~~~~~~~~~~~~~^~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp: In function ‘std::string dbservice::security::sha256(const std::string&)’:
/home/<USER>/database-service-build/src/security/security_manager.cpp:30:16: warning: ‘int SHA256_Init(SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   30 |     SHA256_Init(&sha256);
      |     ~~~~~~~~~~~^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/security/security_manager.cpp:13:
/usr/include/openssl/sha.h:73:27: note: declared here
   73 | OSSL_DEPRECATEDIN_3_0 int SHA256_Init(SHA256_CTX *c);
      |                           ^~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:31:18: warning: ‘int SHA256_Update(SHA256_CTX*, const void*, size_t)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   31 |     SHA256_Update(&sha256, input.c_str(), input.size());
      |     ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:74:27: note: declared here
   74 | OSSL_DEPRECATEDIN_3_0 int SHA256_Update(SHA256_CTX *c,
      |                           ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/security/security_manager.cpp:32:17: warning: ‘int SHA256_Final(unsigned char*, SHA256_CTX*)’ is deprecated: Since OpenSSL 3.0 [-Wdeprecated-declarations]
   32 |     SHA256_Final(hash, &sha256);
      |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~
/usr/include/openssl/sha.h:76:27: note: declared here
   76 | OSSL_DEPRECATEDIN_3_0 int SHA256_Final(unsigned char *md, SHA256_CTX *c);
      |                           ^~~~~~~~~~~~
[ 72%] Building CXX object CMakeFiles/database-service.dir/src/service/application_manager.cpp.o
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<std::__cxx11::basic_string<char>, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::generateApiKey(int)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:155:47: error: cannot convert ‘<brace-enclosed initializer list>’ to ‘std::span<const std::__cxx11::basic_string<char> >’
  155 |         auto result = connection->executeQuery(checkQuery, {std::to_string(applicationId)});
      |                       ~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
In file included from /home/<USER>/database-service-build/src/service/application_manager.cpp:3:
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:157:71: note:   initializing argument 2 of ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > > dbservice::core::Connection::executeQuery(std::string_view, std::span<const std::__cxx11::basic_string<char> >)’
  157 |     executeQuery(std::string_view query, std::span<const std::string> params = {});
      |                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:171:36: error: cannot convert ‘<brace-enclosed initializer list>’ to ‘std::span<const std::__cxx11::basic_string<char> >’
  171 |         connection->executeNonQuery(updateQuery, {newApiKey, std::to_string(applicationId)});
      |         ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:214:82: note:   initializing argument 2 of ‘int dbservice::core::Connection::executeNonQuery(std::string_view, std::span<const std::__cxx11::basic_string<char> >)’
  214 |     int executeNonQuery(std::string_view statement, std::span<const std::string> params = {});
      |                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<int, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::validateApiKey(const std::string&)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:199:47: error: cannot convert ‘<brace-enclosed initializer list>’ to ‘std::span<const std::__cxx11::basic_string<char> >’
  199 |         auto result = connection->executeQuery(query, {apiKey});
      |                       ~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:157:71: note:   initializing argument 2 of ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > > dbservice::core::Connection::executeQuery(std::string_view, std::span<const std::__cxx11::basic_string<char> >)’
  157 |     executeQuery(std::string_view query, std::span<const std::string> params = {});
      |                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘bool dbservice::service::ApplicationManager::applicationNameExists(const std::string&)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:254:47: error: cannot convert ‘<brace-enclosed initializer list>’ to ‘std::span<const std::__cxx11::basic_string<char> >’
  254 |         auto result = connection->executeQuery(query, {name});
      |                       ~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:157:71: note:   initializing argument 2 of ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > > dbservice::core::Connection::executeQuery(std::string_view, std::span<const std::__cxx11::basic_string<char> >)’
  157 |     executeQuery(std::string_view query, std::span<const std::string> params = {});
      |                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<dbservice::service::Application, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::getApplication(int)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:282:47: error: cannot convert ‘<brace-enclosed initializer list>’ to ‘std::span<const std::__cxx11::basic_string<char> >’
  282 |         auto result = connection->executeQuery(query, {std::to_string(applicationId)});
      |                       ~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:157:71: note:   initializing argument 2 of ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > > dbservice::core::Connection::executeQuery(std::string_view, std::span<const std::__cxx11::basic_string<char> >)’
  157 |     executeQuery(std::string_view query, std::span<const std::string> params = {});
      |                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<dbservice::service::Application, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::getApplicationByName(const std::string&)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:313:47: error: cannot convert ‘<brace-enclosed initializer list>’ to ‘std::span<const std::__cxx11::basic_string<char> >’
  313 |         auto result = connection->executeQuery(query, {name});
      |                       ~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:157:71: note:   initializing argument 2 of ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > > dbservice::core::Connection::executeQuery(std::string_view, std::span<const std::__cxx11::basic_string<char> >)’
  157 |     executeQuery(std::string_view query, std::span<const std::string> params = {});
      |                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<void, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::updateApplicationMetadata(int, const nlohmann::json_abi_v3_11_3::json&)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:375:47: error: cannot convert ‘<brace-enclosed initializer list>’ to ‘std::span<const std::__cxx11::basic_string<char> >’
  375 |         auto result = connection->executeQuery(checkQuery, {std::to_string(applicationId)});
      |                       ~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:157:71: note:   initializing argument 2 of ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > > dbservice::core::Connection::executeQuery(std::string_view, std::span<const std::__cxx11::basic_string<char> >)’
  157 |     executeQuery(std::string_view query, std::span<const std::string> params = {});
      |                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:388:36: error: cannot convert ‘<brace-enclosed initializer list>’ to ‘std::span<const std::__cxx11::basic_string<char> >’
  388 |         connection->executeNonQuery(updateQuery, {metadata.dump(), std::to_string(applicationId)});
      |         ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:214:82: note:   initializing argument 2 of ‘int dbservice::core::Connection::executeNonQuery(std::string_view, std::span<const std::__cxx11::basic_string<char> >)’
  214 |     int executeNonQuery(std::string_view statement, std::span<const std::string> params = {});
      |                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<void, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::deactivateApplication(int)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:444:56: error: cannot convert ‘<brace-enclosed initializer list>’ to ‘std::span<const std::__cxx11::basic_string<char> >’
  444 |         auto rowsAffected = connection->executeNonQuery(updateQuery, {std::to_string(applicationId)});
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:214:82: note:   initializing argument 2 of ‘int dbservice::core::Connection::executeNonQuery(std::string_view, std::span<const std::__cxx11::basic_string<char> >)’
  214 |     int executeNonQuery(std::string_view statement, std::span<const std::string> params = {});
      |                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<void, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::activateApplication(int)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:476:56: error: cannot convert ‘<brace-enclosed initializer list>’ to ‘std::span<const std::__cxx11::basic_string<char> >’
  476 |         auto rowsAffected = connection->executeNonQuery(updateQuery, {std::to_string(applicationId)});
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:214:82: note:   initializing argument 2 of ‘int dbservice::core::Connection::executeNonQuery(std::string_view, std::span<const std::__cxx11::basic_string<char> >)’
  214 |     int executeNonQuery(std::string_view statement, std::span<const std::string> params = {});
      |                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp: In member function ‘std::expected<void, dbservice::service::ApplicationError> dbservice::service::ApplicationManager::linkToDatabase(int, int, const std::string&)’:
/home/<USER>/database-service-build/src/service/application_manager.cpp:508:47: error: cannot convert ‘<brace-enclosed initializer list>’ to ‘std::span<const std::__cxx11::basic_string<char> >’
  508 |         auto result = connection->executeQuery(checkAppQuery, {std::to_string(applicationId)});
      |                       ~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:157:71: note:   initializing argument 2 of ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > > dbservice::core::Connection::executeQuery(std::string_view, std::span<const std::__cxx11::basic_string<char> >)’
  157 |     executeQuery(std::string_view query, std::span<const std::string> params = {});
      |                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:520:42: error: cannot convert ‘<brace-enclosed initializer list>’ to ‘std::span<const std::__cxx11::basic_string<char> >’
  520 |         result = connection->executeQuery(checkMappingQuery, {
      |                  ~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~
  521 |             std::to_string(applicationId),
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  522 |             std::to_string(databaseInstanceId)
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  523 |         });
      |         ~~                                
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:157:71: note:   initializing argument 2 of ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > > dbservice::core::Connection::executeQuery(std::string_view, std::span<const std::__cxx11::basic_string<char> >)’
  157 |     executeQuery(std::string_view query, std::span<const std::string> params = {});
      |                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:533:40: error: cannot convert ‘<brace-enclosed initializer list>’ to ‘std::span<const std::__cxx11::basic_string<char> >’
  533 |             connection->executeNonQuery(updateQuery, {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~
  534 |                 schemaName,
      |                 ~~~~~~~~~~~             
  535 |                 std::to_string(applicationId),
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  536 |                 std::to_string(databaseInstanceId)
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  537 |             });
      |             ~~                          
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:214:82: note:   initializing argument 2 of ‘int dbservice::core::Connection::executeNonQuery(std::string_view, std::span<const std::__cxx11::basic_string<char> >)’
  214 |     int executeNonQuery(std::string_view statement, std::span<const std::string> params = {});
      |                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
/home/<USER>/database-service-build/src/service/application_manager.cpp:545:40: error: cannot convert ‘<brace-enclosed initializer list>’ to ‘std::span<const std::__cxx11::basic_string<char> >’
  545 |             connection->executeNonQuery(insertQuery, {
      |             ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~
  546 |                 std::to_string(applicationId),
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  547 |                 std::to_string(databaseInstanceId),
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  548 |                 schemaName
      |                 ~~~~~~~~~~              
  549 |             });
      |             ~~                          
/home/<USER>/database-service-build/include/database-service/core/connection.hpp:214:82: note:   initializing argument 2 of ‘int dbservice::core::Connection::executeNonQuery(std::string_view, std::span<const std::__cxx11::basic_string<char> >)’
  214 |     int executeNonQuery(std::string_view statement, std::span<const std::string> params = {});
      |                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
make[2]: *** [CMakeFiles/database-service.dir/build.make:289: CMakeFiles/database-service.dir/src/service/application_manager.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
