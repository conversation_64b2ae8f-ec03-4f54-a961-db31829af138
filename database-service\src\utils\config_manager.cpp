#include "database-service/utils/config_manager.hpp"
#include "database-service/utils/logger.hpp"
#include <fstream>
#include <sstream>
#include <format> // C++20 feature
#include <filesystem>
#include <nlohmann/json.hpp>

namespace dbservice::utils {

ConfigManager& ConfigManager::getInstance() {
    static ConfigManager instance;
    return instance;
}

ConfigManager::ConfigManager() {
}

ConfigManager::~ConfigManager() {
}

bool ConfigManager::loadFromFile(const std::string& filename) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    try {
        // Check if file exists
                if (!std::filesystem::exists(std::filesystem::path(filename))) {
            Logger::error(std::format("Configuration file not found: {}", filename));
            return false;
        }
        
        // Open file
        std::ifstream file(filename);
        if (!file.is_open()) {
            Logger::error(std::format("Failed to open configuration file: {}", filename));
            return false;
        }
        
        // Read file
        std::stringstream buffer;
        buffer << file.rdbuf();
        
        // Parse JSON
        nlohmann::json json = nlohmann::json::parse(buffer.str());
        
        // Clear existing configuration
        config_.clear();
        
        // Flatten JSON into key-value pairs
        flattenJson(json, "");
        
        Logger::info(std::format("Configuration loaded from {}", filename));
        return true;
    } catch (const nlohmann::json::exception& e) {
        Logger::error(std::format("JSON parsing error: {}", e.what()));
        return false;
    } catch (const std::exception& e) {
        Logger::error(std::format("Exception during configuration loading: {}", e.what()));
        return false;
    }
}

std::string ConfigManager::getString(const std::string& key, const std::string& defaultValue) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = config_.find(key);
    if (it != config_.end()) {
        return it->second;
    }
    
    return defaultValue;
}

int ConfigManager::getInt(const std::string& key, int defaultValue) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = config_.find(key);
    if (it != config_.end()) {
        try {
            return std::stoi(it->second);
        } catch (const std::exception& e) {
            Logger::warning(std::format("Failed to convert {} to int: {}", key, e.what()));
        }
    }
    
    return defaultValue;
}

bool ConfigManager::getBool(const std::string& key, bool defaultValue) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = config_.find(key);
    if (it != config_.end()) {
        std::string value = it->second;
        if (value == "true" || value == "1" || value == "yes" || value == "y") {
            return true;
        } else if (value == "false" || value == "0" || value == "no" || value == "n") {
            return false;
        }
    }
    
    return defaultValue;
}

double ConfigManager::getDouble(const std::string& key, double defaultValue) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = config_.find(key);
    if (it != config_.end()) {
        try {
            return std::stod(it->second);
        } catch (const std::exception& e) {
            Logger::warning(std::format("Failed to convert {} to double: {}", key, e.what()));
        }
    }
    
    return defaultValue;
}

void ConfigManager::setString(const std::string& key, const std::string& value) {
    std::lock_guard<std::mutex> lock(mutex_);
    config_[key] = value;
}

void ConfigManager::setInt(const std::string& key, int value) {
    std::lock_guard<std::mutex> lock(mutex_);
    config_[key] = std::to_string(value);
}

void ConfigManager::setBool(const std::string& key, bool value) {
    std::lock_guard<std::mutex> lock(mutex_);
    config_[key] = value ? "true" : "false";
}

void ConfigManager::setDouble(const std::string& key, double value) {
    std::lock_guard<std::mutex> lock(mutex_);
    config_[key] = std::to_string(value);
}

nlohmann::json ConfigManager::getJsonArray(const std::string& key) const {
    std::lock_guard<std::mutex> lock(mutex_);

    nlohmann::json result = nlohmann::json::array();

    // Look for array elements with pattern key.0, key.1, etc.
    for (const auto& [configKey, configValue] : config_) {
        if (configKey.substr(0, key.length() + 1) == key + ".") {
            std::string suffix = configKey.substr(key.length() + 1);

            // Check if suffix is a number (array index)
            try {
                size_t index = std::stoull(suffix);

                // Ensure array is large enough
                while (result.size() <= index) {
                    result.push_back(nullptr);
                }

                // Try to parse as JSON, otherwise store as string
                try {
                    result[index] = nlohmann::json::parse(configValue);
                } catch (const nlohmann::json::exception&) {
                    result[index] = configValue;
                }
            } catch (const std::exception&) {
                // Not a numeric index, skip
                continue;
            }
        }
    }

    return result;
}

void ConfigManager::flattenJson(const nlohmann::json& json, const std::string& prefix) {
    for (auto it = json.begin(); it != json.end(); ++it) {
        std::string key = prefix.empty() ? it.key() : prefix + "." + it.key();
        
        if (it->is_object()) {
            flattenJson(*it, key);
        } else if (it->is_array()) {
            // Handle arrays
            for (size_t i = 0; i < it->size(); ++i) {
                std::string arrayKey = key + "." + std::to_string(i);
                
                if ((*it)[i].is_object()) {
                    flattenJson((*it)[i], arrayKey);
                } else {
                    config_[arrayKey] = (*it)[i].dump();
                }
            }
        } else {
            // Handle primitive types
            if (it->is_string()) {
                config_[key] = it->get<std::string>();
            } else if (it->is_boolean()) {
                config_[key] = it->get<bool>() ? "true" : "false";
            } else if (it->is_number_integer()) {
                config_[key] = std::to_string(it->get<int>());
            } else if (it->is_number_float()) {
                config_[key] = std::to_string(it->get<double>());
            } else if (it->is_null()) {
                config_[key] = "";
            } else {
                config_[key] = it->dump();
            }
        }
    }
}

} // namespace dbservice::utils
