# Database Service Directory Structure

This document describes the directory structure of the Database Service project.

## Project Organization

The Database Service project is organized into two main directories:

1. **database-service/**: The main C++23 application source code
2. **database-service-deployment/**: Deployment scripts and configuration files

### Main Application Structure

```
database-service/
├── CMakeLists.txt                  - Main CMake configuration
├── README.md                       - Project documentation
├── QUICKSTART.md                   - Quick start guide
├── cmake/                          - CMake utility files
│   ├── check_std_expected.cpp      - C++23 std::expected feature check
│   ├── check_std_format.cpp        - C++20 std::format feature check
│   └── check_std_print.cpp         - C++23 std::print feature check
├── config/                         - Configuration files
│   ├── config.json                 - Main configuration
│   ├── config-dev.json             - Development configuration
│   └── config-prod.json            - Production configuration
├── include/                        - Header files
│   └── database-service/           - Public API headers
│       ├── api/                    - API server headers
│       ├── core/                   - Core functionality headers
│       ├── database_service.hpp    - Main service interface
│       ├── metrics/                - Monitoring and metrics headers
│       ├── schema/                 - Schema management headers
│       ├── security/               - Security headers
│       ├── service/                - Service layer headers
│       └── utils/                  - Utility headers
├── nginx/                          - Nginx configuration
│   └── database-service.conf       - Reverse proxy configuration
├── scripts/                        - Build and integration scripts
│   ├── build.sh                    - Linux build script
│   ├── integrate-with-git-server.sh - Git server integration
│   ├── setup-reverse-proxy.sh      - Nginx setup script
│   └── test-integration.sh         - Integration testing
├── src/                            - Implementation files
│   ├── api/                        - API implementation
│   ├── core/                       - Core implementation
│   ├── database_service.cpp        - Main service implementation
│   ├── main.cpp                    - Entry point
│   ├── metrics/                    - Monitoring implementation
│   ├── schema/                     - Schema implementation
│   ├── security/                   - Security implementation
│   ├── service/                    - Service layer implementation
│   └── utils/                      - Utility implementation
├── systemd/                        - Systemd service files
│   └── database-service.service.in - Service template
└── tests/                          - Test files
    ├── CMakeLists.txt              - Test configuration
    ├── api_server_test.cpp         - API server tests
    ├── connection_test.cpp         - Connection tests
    ├── main.cpp                    - Test entry point
    ├── schema_manager_test.cpp     - Schema manager tests
    └── security_manager_test.cpp   - Security manager tests
```

### Deployment Structure

```
database-service-deployment/
├── README.md                       - Deployment documentation
├── DATABASE-SCRIPTS.md             - Database setup documentation
├── requirements.json               - Deployment requirements
├── setup-certificate-access.sh     - SSL certificate setup
├── deployment_files/               - Deployment assets
│   ├── examples/                   - Example configurations
│   ├── migrations/                 - Database migrations
│   ├── schemas/                    - Database schemas
│   ├── sql/                        - SQL scripts
│   ├── systemd/                    - Systemd service files
│   └── tests/                      - Deployment tests
├── deployment_logs/                - Build and deployment logs
│   └── build-*.log                 - Individual build logs
└── deployment_scripts/             - PowerShell deployment scripts
    ├── deploy-database-service-modular.ps1 - Main deployment script
    ├── Common.psm1                 - Common PowerShell functions
    ├── DEPLOYMENT.md               - Deployment guide
    ├── README.md                   - Script documentation
    ├── requirements.json           - Script requirements
    ├── build_runner.ps1            - Build execution script
    ├── config/                     - Deployment configurations
    ├── logs/                       - Script logs
    └── Modules/                    - PowerShell modules
        ├── Build-Project.psm1      - Build functionality
        ├── Configuration-Manager.psm1 - Configuration management
        ├── Install-Dependencies.psm1 - Dependency installation
        └── Test-ServerReadiness.psm1 - Server validation
```

## Source Code Organization

The source code is organized into the following components:

1. **Core**: Database connection and transaction management
2. **API**: HTTP API server and request handlers
3. **Schema**: Database schema management and migrations
4. **Security**: Authentication, authorization, and user management
5. **Utils**: Utility classes for configuration and logging
6. **Main Service**: Coordinates all components

## Production Server Structure

On the Linux production server, the Database Service is built and deployed using the PowerShell deployment script. The process uses a temporary build workspace and then installs to the production directory.

### Temporary Build Workspace

The build process uses a temporary workspace in the user's home directory to avoid permissions issues:

```
/home/<USER>/database-service-build/
├── source/                         - Source code (copied from Windows)
│   ├── CMakeLists.txt              - CMake configuration
│   ├── config/                     - Configuration files
│   ├── include/                    - Header files
│   ├── src/                        - Source files
│   ├── systemd/                    - Service templates
│   └── tests/                      - Test files
└── build/                          - Build output directory
    ├── CMakeCache.txt              - CMake cache
    ├── CMakeFiles/                 - CMake build files
    ├── Makefile                    - Generated Makefile
    └── bin/                        - Compiled executables
        └── database-service        - Main executable (after successful build)
```

> **Note**: This build directory is temporary and used only for compilation. The executable is compiled here and then installed to the production directory.

### Production Installation Directory

After successful build and installation, the Database Service is installed in the standard production location:

```
/opt/database-service/
├── bin/                            - Executable files
│   └── database-service            - Main executable (installed from build workspace)
├── config/                         - Configuration files
│   └── config.json                 - Production configuration
├── lib/                            - Library files (if needed)
├── logs/                           - Log files
│   └── database-service.log        - Main log file
└── schemas/                        - Schema files
    └── ...                         - SQL schema files
```

### System Integration

The service is managed by systemd with a service file at:

```
/etc/systemd/system/database-service.service
```

Log files may also be found in the system journal:

```
/var/log/journal/
```

### Current Build Status

As of the latest successful build (2025-06-18), the executable has been successfully compiled in the build workspace:
```
/home/<USER>/database-service-build/build/bin/database-service
```

**Next Steps**:
- Run Menu Option 10 to install the executable to production directory (`/opt/database-service/`)
- Run Menu Option 11 to initialize the database
- Run Menu Option 12 to start the service

The build process uses:
- **GCC 14.2.0** with C++23 standard
- **CMake** for build configuration
- **Release build** configuration
- **5-minute SSH timeout** for build operations

## Configuration Files

The Database Service uses the following configuration files:

1. **config.json**: Main configuration file
2. **config.template.json**: Template configuration file with placeholders

## Log Files

The Database Service generates the following log files:

1. **database-service.log**: Main log file
2. **database-service-error.log**: Error log file (if configured)

## Systemd Service

The Database Service is managed by systemd with the following service file:

```
/etc/systemd/system/database-service.service
```

This service file is generated from the template in `systemd/database-service.service.in` during installation.

## Deployment Logs and Build Artifacts

### Build Logs

The PowerShell deployment script generates detailed build logs in:

```
database-service-deployment/deployment_logs/
└── build-YYYYMMDD-HHMMSS.log    - Individual build logs with timestamps
```

Example recent successful build log:
```
database-service-deployment/deployment_logs/build-20250618-194010.log
```

### Build Artifacts

After a successful build, the compiled executable is initially located in the build workspace:

```
/home/<USER>/database-service-build/build/bin/database-service
```

After installation (Menu Option 10), the executable is copied to the production directory:

```
/opt/database-service/bin/database-service
```

### PowerShell Modules

The deployment system uses modular PowerShell scripts:

```
database-service-deployment/deployment_scripts/Modules/
├── Build-Project.psm1              - Handles compilation and build process
├── Configuration-Manager.psm1      - Manages configuration files
├── Install-Dependencies.psm1       - Installs system dependencies
└── Test-ServerReadiness.psm1       - Validates server prerequisites
```

### Key Features

- **SSH Timeout**: 5-minute timeout for build operations
- **Build Verification**: Automatic success/failure detection
- **Detailed Logging**: Comprehensive build logs with timestamps
- **Modular Design**: Separate modules for different deployment phases
- **Error Handling**: Robust error detection and reporting
