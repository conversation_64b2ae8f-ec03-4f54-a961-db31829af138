{"database": {"host": "localhost", "port": 5432, "name": "database_service", "user": "database_service_user", "password": "CHANGE_THIS_PASSWORD", "ssl": {"enabled": false, "mode": "verify-full", "cert_path": "/etc/letsencrypt/live/chcit.org/fullchain.pem", "key_path": "/etc/letsencrypt/live/chcit.org/privkey.pem", "ca_path": "/etc/letsencrypt/live/chcit.org/chain.pem", "crl_path": "", "reject_expired": true}, "pool": {"max_connections": 10}}, "api": {"port": 8081, "host": "127.0.0.1", "base_path": "/api", "cors": {"enabled": true, "allowed_origins": ["*"], "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allowed_headers": ["Content-Type", "Authorization"], "allow_credentials": true, "max_age": 86400}, "ssl": {"enabled": false, "note": "SSL termination handled by Nginx reverse proxy"}}, "security": {"enable_authentication": true, "jwt_secret": "development-secret-change-in-production", "token_expiration_seconds": 3600, "refresh_token_expiration_seconds": 86400, "password_hash_algorithm": "bcrypt", "password_hash_cost": 12, "secure_credential_storage": {"enabled": false, "note": "Disabled for development - enable in production"}}, "logging": {"enabled": true, "level": "info", "file": "/var/log/database-service/database-service.log", "max_size_mb": 10, "max_files": 5, "console": true}, "schema": {"directory": "/opt/database-service/schemas", "auto_migrate": true, "version_table": "schema_version"}, "metrics": {"enabled": true, "collection_interval_seconds": 60, "retention_days": 7}}