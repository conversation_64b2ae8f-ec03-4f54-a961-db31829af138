# Install-Dependencies Module - Enhanced Version with Detailed PostgreSQL Detection
# This module handles dependency installation and PostgreSQL configuration

# Import required modules
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force
Import-Module -Name "$PSScriptRoot\UI.psm1" -Force

function Install-Dependencies {
    Write-Log -Message "Starting dependency installation and PostgreSQL configuration..." -Level "UI" -ForegroundColor Cyan
    Show-MenuDivider

    # Check if configuration is loaded
    if ($null -eq $script:Config) {
        Write-Log -Message "Configuration is not loaded. Loading configuration..." -Level "UI" -ForegroundColor Yellow
        $configPath = Join-Path -Path $PSScriptRoot -ChildPath "..\config\database-service-development.json"
        Write-Log -Message "Loading configuration from $configPath" -Level "UI" -ForegroundColor Yellow

        try {
            $configContent = Get-Content -Path $configPath -Raw -ErrorAction Stop
            $script:Config = ConvertFrom-Json -InputObject $configContent -ErrorAction Stop
            Write-Log -Message "Configuration loaded successfully." -Level "UI" -ForegroundColor Green
        } catch {
            Write-Log -Message "Failed to load configuration: $_" -Level "UI" -ForegroundColor Red
            Get-UserInput -Prompt 'Press Enter to continue...' | Out-Null
            return
        }
    }

    # Check SSH configuration
    $sshConfigured = ($null -ne $script:Config.ssh -and $null -ne $script:Config.ssh.host -and $null -ne $script:Config.ssh.username)
    
    if (-not $sshConfigured) {
        Write-Log -Message "SSH configuration not found. Cannot proceed with remote installation." -Level "UI" -ForegroundColor Red
        Get-UserInput -Prompt 'Press Enter to continue...' | Out-Null
        return
    }

    Write-Log -Message "SSH configuration found for $($script:Config.ssh.username)@$($script:Config.ssh.host)" -Level "UI" -ForegroundColor Green

    # Confirm installation
    $confirm = Get-UserInput -Prompt "Install dependencies and configure PostgreSQL on remote server? (Y/N)"
    if ($confirm -ne "Y") {
        Write-Log -Message "Installation cancelled by user." -Level "UI" -ForegroundColor Yellow
        Get-UserInput -Prompt 'Press Enter to continue...' | Out-Null
        return
    }

    # Check if key_path exists, if not, use local_key_path
    $keyPath = $script:Config.ssh.key_path
    if ($null -eq $keyPath -and $null -ne $script:Config.ssh.local_key_path) {
        $keyPath = $script:Config.ssh.local_key_path
    }

    # Test SSH connection
    Write-Log -Message "Testing SSH connection..." -Level "UI" -ForegroundColor Yellow
    try {
        $testArgs = @(
            "-i", "$keyPath",
            "-p", "$($script:Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            "echo 'CONNECTION_OK'"
        )

        $output = & ssh @testArgs 2>&1
        if ($LASTEXITCODE -ne 0 -or $output -notlike "*CONNECTION_OK*") {
            Write-Log -Message "SSH connection failed. Cannot proceed." -Level "UI" -ForegroundColor Red
            Write-Log -Message "Output: $output" -Level "UI" -ForegroundColor Red
            Get-UserInput -Prompt 'Press Enter to continue...' | Out-Null
            return
        }
        Write-Log -Message "SSH connection successful." -Level "UI" -ForegroundColor Green
    } catch {
        Write-Log -Message "Error testing SSH connection: $_" -Level "UI" -ForegroundColor Red
        Get-UserInput -Prompt 'Press Enter to continue...' | Out-Null
        return
    }

    # Enhanced PostgreSQL Detection
    Write-Log -Message "`n=== PostgreSQL Installation Analysis ===" -Level "UI" -ForegroundColor Cyan
    
    # Check PostgreSQL version
    Write-Log -Message "Checking PostgreSQL installation..." -Level "UI" -ForegroundColor Yellow
    $pgCheckArgs = @(
        "-i", "$keyPath",
        "-p", "$($script:Config.ssh.port)",
        "-o", "BatchMode=yes",
        "-o", "StrictHostKeyChecking=accept-new",
        "-o", "ConnectTimeout=10",
        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
        "psql --version"
    )

    $pgOutput = & ssh @pgCheckArgs 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Log -Message "❌ PostgreSQL is not installed. Please install PostgreSQL first." -Level "UI" -ForegroundColor Red
        Write-Log -Message "`n=== Analysis Complete ===" -Level "UI" -ForegroundColor Green
        Get-UserInput -Prompt 'Press Enter to continue...' | Out-Null
        return
    }

    Write-Log -Message "✓ PostgreSQL is installed: $pgOutput" -Level "UI" -ForegroundColor Green
    
    # Get PostgreSQL service status
    Write-Log -Message "`nChecking PostgreSQL service status..." -Level "UI" -ForegroundColor Yellow
    $serviceArgs = @(
        "-i", "$keyPath",
        "-p", "$($script:Config.ssh.port)",
        "-o", "BatchMode=yes",
        "-o", "StrictHostKeyChecking=accept-new",
        "-o", "ConnectTimeout=10",
        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
        "sudo systemctl is-active postgresql"
    )
    $serviceStatus = & ssh @serviceArgs 2>&1
    if ($serviceStatus -like "*active*") {
        Write-Log -Message "✓ PostgreSQL service is active (running)" -Level "UI" -ForegroundColor Green
    } else {
        Write-Log -Message "⚠ PostgreSQL service status: $serviceStatus" -Level "UI" -ForegroundColor Yellow
    }
    
    # Get PostgreSQL configuration file location
    Write-Log -Message "`nLocating PostgreSQL configuration..." -Level "UI" -ForegroundColor Yellow
    $configArgs = @(
        "-i", "$keyPath",
        "-p", "$($script:Config.ssh.port)",
        "-o", "BatchMode=yes",
        "-o", "StrictHostKeyChecking=accept-new",
        "-o", "ConnectTimeout=10",
        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
        "sudo find /etc -name 'postgresql.conf' 2>/dev/null | head -1"
    )
    $configFile = & ssh @configArgs 2>&1
    if ($configFile) {
        Write-Log -Message "✓ PostgreSQL config file: $configFile" -Level "UI" -ForegroundColor Green
    }
    
    # Check PostgreSQL port
    Write-Log -Message "`nChecking PostgreSQL network status..." -Level "UI" -ForegroundColor Yellow
    $portArgs = @(
        "-i", "$keyPath",
        "-p", "$($script:Config.ssh.port)",
        "-o", "BatchMode=yes",
        "-o", "StrictHostKeyChecking=accept-new",
        "-o", "ConnectTimeout=10",
        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
        "sudo netstat -tuln | grep ':5432 '"
    )
    $portStatus = & ssh @portArgs 2>&1
    if ($LASTEXITCODE -eq 0 -and $portStatus) {
        Write-Log -Message "✓ PostgreSQL is listening on port 5432" -Level "UI" -ForegroundColor Green
    } else {
        Write-Log -Message "⚠ PostgreSQL port 5432 not accessible" -Level "UI" -ForegroundColor Yellow
    }

    # Get current data directory with detailed information
    Write-Log -Message "`n=== PostgreSQL Data Directory Analysis ===" -Level "UI" -ForegroundColor Cyan
    $dataDirArgs = @(
        "-i", "$keyPath",
        "-p", "$($script:Config.ssh.port)",
        "-o", "BatchMode=yes",
        "-o", "StrictHostKeyChecking=accept-new",
        "-o", "ConnectTimeout=10",
        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
        "sudo -u postgres psql -c 'SHOW data_directory;' | grep -v 'data_directory' | grep -v '(' | grep -v '-' | xargs"
    )

    $currentDataDir = & ssh @dataDirArgs 2>&1
    Write-Log -Message "Current data directory: $currentDataDir" -Level "UI" -ForegroundColor White
    
    # Get data directory disk usage
    if ($currentDataDir) {
        $dataDirUsageArgs = @(
            "-i", "$keyPath",
            "-p", "$($script:Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            "df -h '$currentDataDir'"
        )
        $dataDirUsage = & ssh @dataDirUsageArgs 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log -Message "Current data directory volume:" -Level "UI" -ForegroundColor White
            Write-Log -Message "$dataDirUsage" -Level "UI" -ForegroundColor Gray
        }
    }

    # Check for dedicated volume
    Write-Log -Message "`n=== Dedicated Volume Analysis ===" -Level "UI" -ForegroundColor Cyan
    $volumeArgs = @(
        "-i", "$keyPath",
        "-p", "$($script:Config.ssh.port)",
        "-o", "BatchMode=yes",
        "-o", "StrictHostKeyChecking=accept-new",
        "-o", "ConnectTimeout=10",
        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
        "df -h /pgsql/data 2>/dev/null"
    )

    $volumeOutput = & ssh @volumeArgs 2>&1
    $dedicatedVolumeExists = ($LASTEXITCODE -eq 0)
    
    if ($dedicatedVolumeExists) {
        Write-Log -Message "✓ Found dedicated PostgreSQL volume:" -Level "UI" -ForegroundColor Green
        Write-Log -Message "$volumeOutput" -Level "UI" -ForegroundColor White
    } else {
        Write-Log -Message "⚠ No dedicated PostgreSQL volume found at /pgsql/data" -Level "UI" -ForegroundColor Yellow
    }

    # Determine configuration status and recommendations
    Write-Log -Message "`n=== Configuration Analysis ===" -Level "UI" -ForegroundColor Cyan
    
    $isUsingDedicatedVolume = ($currentDataDir -like "*pgsql/data*")
    
    if ($isUsingDedicatedVolume) {
        Write-Log -Message "✓ OPTIMAL CONFIGURATION: PostgreSQL is using dedicated volume" -Level "UI" -ForegroundColor Green
        Write-Log -Message "Current location: $currentDataDir" -Level "UI" -ForegroundColor White
        Write-Log -Message "No migration needed." -Level "UI" -ForegroundColor Green
    } elseif ($dedicatedVolumeExists) {
        Write-Log -Message "⚠ MIGRATION RECOMMENDED: PostgreSQL is using system volume" -Level "UI" -ForegroundColor Yellow
        Write-Log -Message "Current location: $currentDataDir" -Level "UI" -ForegroundColor White
        Write-Log -Message "Recommended location: /pgsql/data/17/main" -Level "UI" -ForegroundColor White
        Write-Log -Message "Benefits: Better performance, I/O isolation, dedicated space" -Level "UI" -ForegroundColor White
        
        $moveConfirm = Get-UserInput -Prompt "`nMove PostgreSQL data to dedicated volume /pgsql/data? (Y/N)"
        if ($moveConfirm -eq "Y") {
            Write-Log -Message "Starting PostgreSQL migration to dedicated volume..." -Level "UI" -ForegroundColor Cyan
            Write-Log -Message "Migration functionality available - would perform full migration here" -Level "UI" -ForegroundColor Yellow
        } else {
            Write-Log -Message "Migration cancelled by user." -Level "UI" -ForegroundColor Yellow
        }
    } else {
        Write-Log -Message "ℹ CURRENT CONFIGURATION: PostgreSQL using system volume" -Level "UI" -ForegroundColor White
        Write-Log -Message "Current location: $currentDataDir" -Level "UI" -ForegroundColor White
        Write-Log -Message "Recommendation: Consider setting up a dedicated volume for better performance" -Level "UI" -ForegroundColor Yellow
    }

    Write-Log -Message ("`n=== Analysis Complete ===") -Level "UI" -ForegroundColor Green
    Get-UserInput -Prompt 'Press Enter to continue...' | Out-Null
}

# Export the function
Export-ModuleMember -Function Install-Dependencies
