# Install-Dependencies Module - Simplified Version
# This module handles dependency installation and PostgreSQL configuration

# Import required modules
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force
Import-Module -Name "$PSScriptRoot\UI.psm1" -Force

function Install-Dependencies {
    Write-Log -Message "Starting dependency installation and PostgreSQL configuration..." -Level "UI" -ForegroundColor Cyan
    Show-MenuDivider

    # Check if configuration is loaded
    if ($null -eq $script:Config) {
        Write-Log -Message "Configuration is not loaded. Loading configuration..." -Level "UI" -ForegroundColor Yellow
        # Try to load configuration
        $configPath = Join-Path -Path $PSScriptRoot -ChildPath "..\config\database-service-development.json"
        Write-Log -Message "Loading configuration from $configPath" -Level "UI" -ForegroundColor Yellow

        try {
            $configContent = Get-Content -Path $configPath -Raw -ErrorAction Stop
            $script:Config = ConvertFrom-Json -InputObject $configContent -ErrorAction Stop
            Write-Log -Message "Configuration loaded successfully." -Level "UI" -ForegroundColor Green
        } catch {
            Write-Log -Message "Failed to load configuration: $_" -Level "UI" -ForegroundColor Red
            Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
            return
        }
    }

    # Check SSH configuration
    $sshConfigured = ($null -ne $script:Config.ssh -and $null -ne $script:Config.ssh.host -and $null -ne $script:Config.ssh.username)
    
    if (-not $sshConfigured) {
        Write-Log -Message "SSH configuration not found. Cannot proceed with remote installation." -Level "UI" -ForegroundColor Red
        Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
        return
    }

    Write-Log -Message "SSH configuration found for $($script:Config.ssh.username)@$($script:Config.ssh.host)" -Level "UI" -ForegroundColor Green
    
    # Confirm installation
    $confirm = Get-UserInput -Prompt "Install dependencies and configure PostgreSQL on remote server? (Y/N)"
    if ($confirm -ne "Y") {
        Write-Log -Message "Installation cancelled by user." -Level "UI" -ForegroundColor Yellow
        Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
        return
    }

    # Test SSH connection
    Write-Log -Message "Testing SSH connection..." -Level "UI" -ForegroundColor Yellow
    try {
        # Check if key_path exists, if not, use local_key_path
        $keyPath = $script:Config.ssh.key_path
        if ($null -eq $keyPath -and $null -ne $script:Config.ssh.local_key_path) {
            $keyPath = $script:Config.ssh.local_key_path
        }

        $testArgs = @(
            "-i", "$keyPath",
            "-p", "$($script:Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            "echo 'CONNECTION_OK'"
        )

        $output = & ssh @testArgs 2>&1
        if ($LASTEXITCODE -ne 0 -or $output -notlike "*CONNECTION_OK*") {
            Write-Log -Message "SSH connection failed. Cannot proceed." -Level "UI" -ForegroundColor Red
            Write-Log -Message "Output: $output" -Level "UI" -ForegroundColor Red
            Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
            return
        }
        Write-Log -Message "SSH connection successful." -Level "UI" -ForegroundColor Green
    } catch {
        Write-Log -Message "Error testing SSH connection: $_" -Level "UI" -ForegroundColor Red
        Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
        return
    }

    # Check PostgreSQL installation
    Write-Log -Message "`nChecking PostgreSQL installation..." -Level "UI" -ForegroundColor Cyan
    $pgCheckArgs = @(
        "-i", "$keyPath",
        "-p", "$($script:Config.ssh.port)",
        "-o", "BatchMode=yes",
        "-o", "StrictHostKeyChecking=accept-new",
        "-o", "ConnectTimeout=10",
        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
        "psql --version"
    )

    $pgOutput = & ssh @pgCheckArgs 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Log -Message "PostgreSQL is installed: $pgOutput" -Level "UI" -ForegroundColor Green
        
        # Check current data directory
        Write-Log -Message "Checking current PostgreSQL data directory..." -Level "UI" -ForegroundColor Yellow
        $dataDirArgs = @(
            "-i", "$keyPath",
            "-p", "$($script:Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            "sudo -u postgres psql -c 'SHOW data_directory;' | grep -v 'data_directory' | grep -v '(' | grep -v '-' | xargs"
        )

        $currentDataDir = & ssh @dataDirArgs 2>&1
        Write-Log -Message "Current data directory: $currentDataDir" -Level "UI" -ForegroundColor White

        # Check if dedicated volume exists
        Write-Log -Message "Checking for dedicated PostgreSQL volume..." -Level "UI" -ForegroundColor Yellow
        $volumeArgs = @(
            "-i", "$keyPath",
            "-p", "$($script:Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            "df -h /pgsql/data"
        )

        $volumeOutput = & ssh @volumeArgs 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log -Message "Found dedicated PostgreSQL volume:" -Level "UI" -ForegroundColor Green
            Write-Log -Message "$volumeOutput" -Level "UI" -ForegroundColor White

            # Check if PostgreSQL needs to be moved to dedicated volume
            if ($currentDataDir -like "*var/lib/postgresql*") {
                Write-Log -Message "`nPostgreSQL is using system volume. Moving to dedicated volume..." -Level "UI" -ForegroundColor Yellow
                
                $moveConfirm = Get-UserInput -Prompt "Move PostgreSQL data to dedicated volume /pgsql/data? (Y/N)"
                if ($moveConfirm -eq "Y") {
                    # Move PostgreSQL to dedicated volume
                    Write-Log -Message "Stopping PostgreSQL service..." -Level "UI" -ForegroundColor Yellow
                    $stopArgs = @(
                        "-i", "$keyPath",
                        "-p", "$($script:Config.ssh.port)",
                        "-o", "BatchMode=yes",
                        "-o", "StrictHostKeyChecking=accept-new",
                        "-o", "ConnectTimeout=10",
                        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                        "sudo systemctl stop postgresql"
                    )
                    & ssh @stopArgs 2>&1

                    Write-Log -Message "Preparing dedicated volume..." -Level "UI" -ForegroundColor Yellow
                    $prepareArgs = @(
                        "-i", "$keyPath",
                        "-p", "$($script:Config.ssh.port)",
                        "-o", "BatchMode=yes",
                        "-o", "StrictHostKeyChecking=accept-new",
                        "-o", "ConnectTimeout=10",
                        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                        "sudo chown postgres:postgres /pgsql/data; sudo chmod 700 /pgsql/data; sudo mkdir -p /pgsql/data/17; sudo chown postgres:postgres /pgsql/data/17"
                    )
                    & ssh @prepareArgs 2>&1

                    Write-Log -Message "Copying PostgreSQL data..." -Level "UI" -ForegroundColor Yellow
                    $copyArgs = @(
                        "-i", "$keyPath",
                        "-p", "$($script:Config.ssh.port)",
                        "-o", "BatchMode=yes",
                        "-o", "StrictHostKeyChecking=accept-new",
                        "-o", "ConnectTimeout=10",
                        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                        "sudo rsync -av /var/lib/postgresql/17/main/ /pgsql/data/17/main/; sudo chown -R postgres:postgres /pgsql/data/17/"
                    )
                    & ssh @copyArgs 2>&1

                    Write-Log -Message "Updating PostgreSQL configuration..." -Level "UI" -ForegroundColor Yellow
                    $configArgs = @(
                        "-i", "$keyPath",
                        "-p", "$($script:Config.ssh.port)",
                        "-o", "BatchMode=yes",
                        "-o", "StrictHostKeyChecking=accept-new",
                        "-o", "ConnectTimeout=10",
                        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                        "sudo sed -i 's|/var/lib/postgresql/17/main|/pgsql/data/17/main|g' /etc/postgresql/17/main/postgresql.conf"
                    )
                    & ssh @configArgs 2>&1

                    Write-Log -Message "Starting PostgreSQL service..." -Level "UI" -ForegroundColor Yellow
                    $startArgs = @(
                        "-i", "$keyPath",
                        "-p", "$($script:Config.ssh.port)",
                        "-o", "BatchMode=yes",
                        "-o", "StrictHostKeyChecking=accept-new",
                        "-o", "ConnectTimeout=10",
                        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                        "sudo systemctl start postgresql; sudo systemctl enable postgresql"
                    )
                    & ssh @startArgs 2>&1

                    # Verify the move
                    Write-Log -Message "Verifying PostgreSQL configuration..." -Level "UI" -ForegroundColor Yellow
                    $verifyDataDir = & ssh @dataDirArgs 2>&1
                    if ($verifyDataDir -like "*pgsql/data*") {
                        Write-Log -Message "SUCCESS: PostgreSQL is now using dedicated volume: $verifyDataDir" -Level "UI" -ForegroundColor Green
                    } else {
                        Write-Log -Message "WARNING: PostgreSQL may not be using dedicated volume: $verifyDataDir" -Level "UI" -ForegroundColor Yellow
                    }
                } else {
                    Write-Log -Message "PostgreSQL move cancelled by user." -Level "UI" -ForegroundColor Yellow
                }
            } else {
                Write-Log -Message "PostgreSQL is already using dedicated volume: $currentDataDir" -Level "UI" -ForegroundColor Green
            }
        } else {
            Write-Log -Message "No dedicated PostgreSQL volume found at /pgsql/data" -Level "UI" -ForegroundColor Yellow
            Write-Log -Message "PostgreSQL will continue using system volume: $currentDataDir" -Level "UI" -ForegroundColor White
        }
    } else {
        Write-Log -Message "PostgreSQL is not installed. Please install PostgreSQL first." -Level "UI" -ForegroundColor Red
    }

    Write-Log -Message "`nDependency check and PostgreSQL configuration completed." -Level "UI" -ForegroundColor Green
    Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
}

# Export the function
Export-ModuleMember -Function Install-Dependencies
