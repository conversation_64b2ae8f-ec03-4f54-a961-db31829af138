# Install Dependencies Module

# Import common module
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
# Import Logger module
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force
# Import UI module
Import-Module -Name "$PSScriptRoot\UI.psm1" -Force

function Install-Dependencies {
    Clear-Host
    # Enable UI Mode for menu display
    if (Get-Command -Name Enable-UIMode -ErrorAction SilentlyContinue) {
        Enable-UIMode
    }

    # Use UI module functions for consistent display
    Show-MenuTitle -Title "Install Dependencies"
    Show-MenuDivider
    Write-Log -Message " " -Level "UI"

    # Check if configuration is loaded
    if ($null -eq $script:Config) {
        Write-Log -Message "Configuration is not loaded. Loading configuration..." -Level "UI" -ForegroundColor Yellow
        # Try to load configuration
        $configPath = Join-Path -Path $PSScriptRoot -ChildPath "..\config\database-service-development.json"
        Write-Log -Message "Loading configuration from $configPath" -Level "UI" -ForegroundColor Yellow

        try {
            $configContent = Get-Content -Path $configPath -Raw -ErrorAction Stop
            $script:Config = ConvertFrom-Json -InputObject $configContent -ErrorAction Stop
            Write-Log -Message "Configuration loaded successfully." -Level "UI" -ForegroundColor Green
        } catch {
            Write-Log -Message "Failed to load configuration: $_" -Level "UI" -ForegroundColor Red
            Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
            # Don't try to call Show-MainMenu here - just return
            return
        }
    }

    # Check if project settings exist
    if ($null -eq $script:Config.project) {
        Write-Log -Message "Project settings are not configured. Please configure project settings first." -Level "UI" -ForegroundColor Red
        Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
        return
    }

    # Display project information
    Write-Log -Message "Project: $($script:Config.project.name)" -Level "UI" -ForegroundColor Cyan
    $version = if ($script:Config.version.number) { $script:Config.version.number } else { "Not specified" }
    $environment = if ($script:Config.environment) { $script:Config.environment } else { "Development" }
    Write-Log -Message "Version: $version" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "Environment: $environment" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "Description: $($script:Config.project.description)" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message " " -Level "UI"

    # Try to load dependencies from requirements.json if it exists
    # Otherwise, use hardcoded default dependencies
    $requirementsPath = $null

    # Try to find requirements.json in various locations
    $possiblePaths = @(
        (Join-Path -Path $PSScriptRoot -ChildPath "..\requirements.json"),
        (Join-Path -Path $PSScriptRoot -ChildPath "..\..\requirements.json"),
        (Join-Path -Path (Split-Path -Path $PSScriptRoot -Parent) -ChildPath "requirements.json"),
        (Join-Path -Path (Split-Path -Path (Split-Path -Path $PSScriptRoot -Parent) -Parent) -ChildPath "requirements.json")
    )

    foreach ($path in $possiblePaths) {
        if (Test-Path -Path $path) {
            $requirementsPath = $path
            Write-Log -Message "Found requirements.json at: $requirementsPath" -Level "UI" -ForegroundColor Green
            break
        }
    }

    if ($null -eq $requirementsPath) {
        Write-Log -Message "Requirements file not found. Using default dependencies." -Level "UI" -ForegroundColor Yellow
    } else {
        Write-Log -Message "Loading dependencies from requirements.json..." -Level "UI" -ForegroundColor Yellow

        # Copy requirements.json to the deployment scripts directory for future use
        try {
            $targetPath = Join-Path -Path $PSScriptRoot -ChildPath "..\requirements.json"
            if (-not (Test-Path -Path $targetPath)) {
                Copy-Item -Path $requirementsPath -Destination $targetPath -Force
                Write-Log -Message "Copied requirements.json to deployment scripts directory." -Level "UI" -ForegroundColor Green
            }
        } catch {
            Write-Log -Message "Failed to copy requirements.json: $_" -Level "UI" -ForegroundColor Yellow
        }
    }

    # Define default dependencies based on the requirements.json file
    $defaultDependencies = @(
        @{
            name = "GCC 14 Compiler"
            command = "apt-get install -y g++-14"
            check = "g++-14 --version"
            description = "GCC 14 compiler with C++23 support (required for modules and coroutines)"
        },
        @{
            name = "CMake"
            command = "apt-get install -y cmake"
            check = "cmake --version"
            description = "CMake build system (minimum version 3.20.0)"
        },
        @{
            name = "Build Essential"
            command = "apt-get install -y build-essential"
            check = "dpkg -s build-essential"
            description = "Build tools (make, etc.)"
        },
        @{
            name = "Boost Libraries"
            command = "apt-get install -y libboost-all-dev"
            check = "dpkg -l | grep libboost"
            description = "Boost C++ libraries (system, program_options components required)"
        },
        @{
            name = "PostgreSQL Client Libraries"
            command = "apt-get install -y libpq-dev"
            check = "dpkg -s libpq-dev"
            description = "PostgreSQL client development libraries"
        },
        @{
            name = "pkg-config"
            command = "apt-get install -y pkg-config"
            check = "dpkg -s pkg-config"
            description = "Helper tool for discovering installed libraries"
        },
        @{
            name = "PostgreSQL C++ API"
            command = "apt-get install -y libpqxx-dev"
            check = "dpkg -s libpqxx-dev"
            description = "C++ client API for PostgreSQL"
        },
        @{
            name = "OpenSSL Development"
            command = "apt-get install -y libssl-dev"
            check = "dpkg -s libssl-dev"
            description = "OpenSSL development libraries"
        },
        @{
            name = "JSON Library"
            command = "apt-get install -y nlohmann-json3-dev"
            check = "dpkg -s nlohmann-json3-dev"
            description = "JSON for Modern C++"
        },
        @{
            name = "PostgreSQL Server"
            command = "apt-get install -y postgresql postgresql-contrib"
            check = "psql --version"
            description = "PostgreSQL database server"
            postInstall = $true
        },
        @{
            name = "Ninja Build System"
            command = "apt-get install -y ninja-build"
            check = "dpkg -s ninja-build"
            description = "High-speed build system required for C++ modules support in CMake"
        },
        @{
            name = "Git"
            command = "apt-get install -y git"
            check = "git --version"
            description = "Version control system (for source code management)"
        },
        @{
            name = "Curl"
            command = "apt-get install -y curl"
            check = "curl --version"
            description = "Command line tool for transferring data (for API testing)"
        },
        @{
            name = "Net Tools"
            command = "apt-get install -y net-tools"
            check = "netstat --version"
            description = "Network utilities (for port checking and diagnostics)"
        }
    )

    # Always use the default dependencies
    $script:Config | Add-Member -MemberType NoteProperty -Name "dependencies" -Value $defaultDependencies -Force

    # Save updated configuration
    try {
        $configPath = Join-Path -Path $PSScriptRoot -ChildPath "..\config\database-service-development.json"
        $script:Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configPath -Force
        Write-Log -Message "Dependencies configuration updated successfully." -Level "UI" -ForegroundColor Green
    } catch {
        Write-Log -Message "Failed to save updated configuration: $_" -Level "UI" -ForegroundColor Red
    }

    # Display dependencies to be installed with descriptions
    Write-Log -Message "The following dependencies will be installed:" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message " " -Level "UI"

    # Group dependencies by type for better organization
    Write-Log -Message "Compilers and Build Tools:" -Level "UI" -ForegroundColor Yellow
    foreach ($dependency in $script:Config.dependencies | Where-Object { $_.name -match "GCC|CMake|Build Essential|Ninja" }) {
        Write-Log -Message "  - $($dependency.name): $($dependency.description)" -Level "UI" -ForegroundColor White
    }

    Write-Log -Message " " -Level "UI"
    Write-Log -Message "Libraries:" -Level "UI" -ForegroundColor Yellow
    foreach ($dependency in $script:Config.dependencies | Where-Object { $_.name -match "Boost|pkg-config|OpenSSL|JSON" }) {
        Write-Log -Message "  - $($dependency.name): $($dependency.description)" -Level "UI" -ForegroundColor White
    }

    Write-Log -Message " " -Level "UI"
    Write-Log -Message "Database Components:" -Level "UI" -ForegroundColor Yellow
    foreach ($dependency in $script:Config.dependencies | Where-Object { $_.name -match "PostgreSQL" }) {
        Write-Log -Message "  - $($dependency.name): $($dependency.description)" -Level "UI" -ForegroundColor White
    }

    Write-Log -Message " " -Level "UI"
    Write-Log -Message "System Utilities:" -Level "UI" -ForegroundColor Yellow
    foreach ($dependency in $script:Config.dependencies | Where-Object { $_.name -match "Git|Curl|Net Tools" }) {
        Write-Log -Message "  - $($dependency.name): $($dependency.description)" -Level "UI" -ForegroundColor White
    }

    Write-Log -Message " " -Level "UI"

    # Display system requirements
    Write-Log -Message "System Requirements:" -Level "UI" -ForegroundColor Yellow
    Write-Log -Message "  Service User/Group:" -Level "UI" -ForegroundColor White
    Write-Log -Message "  - User: database-service, Group: database-service" -Level "UI" -ForegroundColor White
    
    Write-Log -Message "`nDirectory Permissions:" -Level "UI" -ForegroundColor Yellow
    $directoryInfo = @(
        "  - /opt/database-service (Owner: database-service, Group: database-service, Permissions: 775)",
        "  - /opt/database-service/bin (Owner: database-service, Group: database-service, Permissions: 775)",
        "  - /opt/database-service/config (Owner: database-service, Group: database-service, Permissions: 775)",
        "  - /opt/database-service/lib (Owner: database-service, Group: database-service, Permissions: 775)",
        "  - /opt/database-service/logs (Owner: database-service, Group: database-service, Permissions: 775)",
        "  - /opt/database-service/sql (Owner: database-service, Group: database-service, Permissions: 775)",
        "  - /etc/systemd/system, Permissions: 644",
        "  - /etc/letsencrypt/live/git.chcit.org",
        "  - /home/<USER>/database-service-build (Owner: btaylor-admin, Group: btaylor-admin, Permissions: 775)"
    )
    
    foreach ($dirInfo in $directoryInfo) {
        Write-Log -Message $dirInfo -Level "UI" -ForegroundColor White
    }
    
    Write-Log -Message " " -Level "UI"

    # Validate system requirements if SSH is configured
    if ($sshConfigured) {
        Write-Log -Message "System Requirements Validation:" -Level "UI" -ForegroundColor Yellow

        # Check CPU cores
        $cpuCheckArgs = @(
            "-i", "$($script:Config.ssh.key_path)",
            "-p", "$($script:Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            "cat /proc/cpuinfo | grep processor | wc -l"
        )

        $cpuCores = & ssh @cpuCheckArgs 2>&1
        if ($LASTEXITCODE -eq 0) {
            $cores = [int]$cpuCores.Trim()
            if ($cores -ge 2) {
                Write-Log -Message "  ✓ CPU Cores: $cores (minimum 2 required)" -Level "UI" -ForegroundColor Green
            } else {
                Write-Log -Message "  ⚠ CPU Cores: $cores (minimum 2 recommended)" -Level "UI" -ForegroundColor Yellow
            }
        }

        # Check memory
        $memCheckArgs = @(
            "-i", "$($script:Config.ssh.key_path)",
            "-p", "$($script:Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            "free -h | grep Mem | awk '{print `$2}'"
        )

        $memory = & ssh @memCheckArgs 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log -Message "  ✓ Memory: $($memory.Trim()) (minimum 2G recommended)" -Level "UI" -ForegroundColor Green
        }

        # Check disk space
        $diskCheckArgs = @(
            "-i", "$($script:Config.ssh.key_path)",
            "-p", "$($script:Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            "df -h / | grep / | awk '{print `$4}'"
        )

        $diskSpace = & ssh @diskCheckArgs 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log -Message "  ✓ Available Disk Space: $($diskSpace.Trim()) (minimum 5G recommended)" -Level "UI" -ForegroundColor Green
        }

        # Check PostgreSQL dedicated volume
        $pgVolumeCheckArgs = @(
            "-i", "$($script:Config.ssh.key_path)",
            "-p", "$($script:Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            "df -h /pgsql/data 2>/dev/null | tail -1 | awk '{print `$2, `$4}' || echo 'No dedicated PostgreSQL volume'"
        )

        $pgVolume = & ssh @pgVolumeCheckArgs 2>&1
        if ($pgVolume -notlike "*No dedicated PostgreSQL volume*") {
            Write-Log -Message "  ✓ PostgreSQL Volume: $($pgVolume.Trim())" -Level "UI" -ForegroundColor Green
        } else {
            Write-Log -Message "  ⚠ PostgreSQL Volume: Using system volume" -Level "UI" -ForegroundColor Yellow
        }

        # Check port availability
        Write-Log -Message "Port Availability:" -Level "UI" -ForegroundColor Yellow

        # Check service port (8081 from updated config)
        $servicePortCheckArgs = @(
            "-i", "$($script:Config.ssh.key_path)",
            "-p", "$($script:Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            "sudo netstat -tuln | grep -w 8081 || echo 'Port 8081 is available'"
        )

        $servicePortResult = & ssh @servicePortCheckArgs 2>&1
        if ($servicePortResult -like "*Port 8081 is available*") {
            Write-Log -Message "  ✓ Service Port 8081: Available" -Level "UI" -ForegroundColor Green
        } else {
            Write-Log -Message "  ⚠ Service Port 8081: In use - $servicePortResult" -Level "UI" -ForegroundColor Yellow
        }

        # Check PostgreSQL port
        $pgPortCheckArgs = @(
            "-i", "$($script:Config.ssh.key_path)",
            "-p", "$($script:Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            "sudo netstat -tuln | grep -w 5432 || echo 'PostgreSQL port 5432 not listening'"
        )

        $pgPortResult = & ssh @pgPortCheckArgs 2>&1
        if ($pgPortResult -notlike "*PostgreSQL port 5432 not listening*") {
            Write-Log -Message "  ✓ PostgreSQL Port 5432: Listening" -Level "UI" -ForegroundColor Green
        } else {
            Write-Log -Message "  ⚠ PostgreSQL Port 5432: Not listening (will be started)" -Level "UI" -ForegroundColor Yellow
        }

        # Check SSL certificates
        Write-Log -Message "SSL Certificate Check:" -Level "UI" -ForegroundColor Yellow
        $sslCheckArgs = @(
            "-i", "$($script:Config.ssh.key_path)",
            "-p", "$($script:Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            "test -d /etc/letsencrypt/live/git.chcit.org && test -f /etc/letsencrypt/live/git.chcit.org/fullchain.pem && echo 'SSL certificates found' || echo 'SSL certificates not found'"
        )

        $sslResult = & ssh @sslCheckArgs 2>&1
        if ($sslResult -like "*SSL certificates found*") {
            Write-Log -Message "  ✓ SSL Certificates: Found at /etc/letsencrypt/live/git.chcit.org/" -Level "UI" -ForegroundColor Green
        } else {
            Write-Log -Message "  ⚠ SSL Certificates: Not found (API will use HTTP only)" -Level "UI" -ForegroundColor Yellow
        }

        Write-Log -Message " " -Level "UI"
    }

    # Confirm installation
    $sshConfigured = ($null -ne $script:Config.ssh -and $null -ne $script:Config.ssh.host -and $null -ne $script:Config.ssh.username)
    
    if ($sshConfigured) {
        # Check if key_path exists, if not, use local_key_path
        if ($null -eq $script:Config.ssh.key_path -and $null -ne $script:Config.ssh.local_key_path) {
            $script:Config.ssh | Add-Member -MemberType NoteProperty -Name "key_path" -Value $script:Config.ssh.local_key_path -Force
            Write-Log -Message "Using local_key_path as key_path: $($script:Config.ssh.key_path)" -Level "UI" -ForegroundColor Yellow
        }

        Write-Log -Message "SSH configuration found for $($script:Config.ssh.username)@$($script:Config.ssh.host)." -Level "UI" -ForegroundColor Yellow
        $combinedPrompt = Get-UserInput -Prompt "Install dependencies and configure directories on remote server? (Y/N)"
        
        if ($combinedPrompt -ne "Y") {
            Write-Log -Message "Installation cancelled by user." -Level "UI" -ForegroundColor Yellow
            Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
            return
        }
        
        $remoteInstall = $true
    } else {
        # No SSH configuration, just confirm local installation
        $confirm = Get-UserInput -Prompt "Proceed with installation? (Y/N)"

        if ($confirm -ne "Y") {
            Write-Log -Message "Installation cancelled by user." -Level "UI" -ForegroundColor Yellow
            Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
            return
        }
        
        $remoteInstall = $false
    }

    # Check if SSH configuration exists for remote installation
    if ($remoteInstall) {
        Write-Log -Message "`nChecking installation and build directories..." -Level "UI" -ForegroundColor Cyan
        Show-MenuDivider
    
        # Define all required directories with their permissions
        $requiredDirectories = @(
            @{
                Path = "/opt/database-service"
                Owner = "database-service"
                Group = "database-service"
                Permissions = "775"
                Description = "Main installation directory"
            },
            @{
                Path = "/opt/database-service/bin"
                Owner = "database-service"
                Group = "database-service"
                Permissions = "775"
                Description = "Binary executables directory"
            },
            @{
                Path = "/opt/database-service/config"
                Owner = "database-service"
                Group = "database-service"
                Permissions = "775"
                Description = "Configuration files directory"
            },
            @{
                Path = "/opt/database-service/lib"
                Owner = "database-service"
                Group = "database-service"
                Permissions = "775"
                Description = "Library files directory"
            },
            @{
                Path = "/opt/database-service/logs"
                Owner = "database-service"
                Group = "database-service"
                Permissions = "775"
                Description = "Log files directory"
            },
            @{
                Path = "/opt/database-service/sql"
                Owner = "database-service"
                Group = "database-service"
                Permissions = "775"
                Description = "SQL scripts directory"
            },
            @{
                Path = "/etc/systemd/system"
                Owner = "root"
                Group = "root"
                Permissions = "644"
                Description = "Systemd service files directory"
                CheckOnly = $true # Don't try to create this directory
            },
            @{
                Path = "/etc/letsencrypt/live/git.chcit.org"
                Owner = "root"
                Group = "root"
                Permissions = "750"
                Description = "SSL certificates directory"
                CheckOnly = $true # Don't try to create this directory
            },
            @{
                Path = "/home/<USER>/database-service-build"
                Owner = "btaylor-admin"
                Group = "btaylor-admin"
                Permissions = "775"
                Description = "Build directory"
            }
        )

        # Check and create service user/group if needed
        Write-Log -Message "Checking if database-service user exists..." -Level "UI" -ForegroundColor Yellow
        $checkUserArgs = @(
            "-i", "$($script:Config.ssh.key_path)",
            "-p", "$($script:Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            "id -u database-service 2>/dev/null || echo 'USER_NOT_EXISTS'"
        )

        $checkUserOutput = & ssh @checkUserArgs 2>&1
        
        if ($checkUserOutput -like "*USER_NOT_EXISTS*") {
            Write-Log -Message "Creating database-service user and group..." -Level "UI" -ForegroundColor Yellow
            $createUserArgs = @(
                "-i", "$($script:Config.ssh.key_path)",
                "-p", "$($script:Config.ssh.port)",
                "-o", "BatchMode=yes",
                "-o", "StrictHostKeyChecking=accept-new",
                "-o", "ConnectTimeout=10",
                "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                "sudo useradd -m -s /bin/bash database-service"
            )

            $createUserOutput = & ssh @createUserArgs 2>&1
            $createUserExitCode = $LASTEXITCODE

            if ($createUserExitCode -eq 0) {
                Write-Log -Message "Service user created successfully" -Level "UI" -ForegroundColor Green
            } else {
                Write-Log -Message "Failed to create service user: $createUserOutput" -Level "UI" -ForegroundColor Red
            }
        } else {
            Write-Log -Message "Service user database-service already exists" -Level "UI" -ForegroundColor Green
        }

        # Check and create each required directory with proper permissions
        foreach ($dir in $requiredDirectories) {
            Write-Log -Message "Checking directory: $($dir.Path) ($($dir.Description))" -Level "UI" -ForegroundColor Yellow
            
            $checkDirArgs = @(
                "-i", "$($script:Config.ssh.key_path)",
                "-p", "$($script:Config.ssh.port)",
                "-o", "BatchMode=yes",
                "-o", "StrictHostKeyChecking=accept-new",
                "-o", "ConnectTimeout=10",
                "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                "ls -la '$($dir.Path)' 2>/dev/null || echo 'NOT_EXISTS'"
            )

            $checkDirOutput = & ssh @checkDirArgs 2>&1

            if ($checkDirOutput -notlike "*NOT_EXISTS*") {
                # Directory exists, check permissions
                Write-Log -Message "Directory exists: $($dir.Path)" -Level "UI" -ForegroundColor Green
                
                # Check current permissions
                $checkPermArgs = @(
                    "-i", "$($script:Config.ssh.key_path)",
                    "-p", "$($script:Config.ssh.port)",
                    "-o", "BatchMode=yes",
                    "-o", "StrictHostKeyChecking=accept-new",
                    "-o", "ConnectTimeout=10",
                    "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                    "stat -c '%U:%G %a' '$($dir.Path)'"
                )

                $checkPermOutput = & ssh @checkPermArgs 2>&1
                $currentPerms = $checkPermOutput -split ' '
                $currentOwner = $currentPerms[0]
                $currentMode = $currentPerms[1]
                
                # Check if permissions need to be updated
                $expectedOwner = "$($dir.Owner):$($dir.Group)"
                
                if ($currentOwner -ne $expectedOwner -or $currentMode -ne $dir.Permissions) {
                    Write-Log -Message "Current permissions: $currentOwner $currentMode, Expected: $expectedOwner $($dir.Permissions)" -Level "UI" -ForegroundColor Yellow
                    Write-Log -Message "Updating permissions..." -Level "UI" -ForegroundColor Yellow
                    
                    $updatePermArgs = @(
                        "-i", "$($script:Config.ssh.key_path)",
                        "-p", "$($script:Config.ssh.port)",
                        "-o", "BatchMode=yes",
                        "-o", "StrictHostKeyChecking=accept-new",
                        "-o", "ConnectTimeout=10",
                        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                        "sudo chown $expectedOwner '$($dir.Path)' && sudo chmod $($dir.Permissions) '$($dir.Path)'"
                    )

                    $updatePermOutput = & ssh @updatePermArgs 2>&1
                    $updatePermExitCode = $LASTEXITCODE

                    if ($updatePermExitCode -eq 0) {
                        Write-Log -Message "Permissions updated successfully" -Level "UI" -ForegroundColor Green
                    } else {
                        Write-Log -Message "Failed to update permissions: $updatePermOutput" -Level "UI" -ForegroundColor Red
                    }
                } else {
                    Write-Log -Message "Permissions are correct: $currentOwner $currentMode" -Level "UI" -ForegroundColor Green
                }
            } else {
                # Directory doesn't exist, create it if not check-only
                if ($dir.CheckOnly) {
                    Write-Log -Message "Directory does not exist but is check-only, not creating: $($dir.Path)" -Level "UI" -ForegroundColor Yellow
                } else {
                    Write-Log -Message "Directory does not exist: $($dir.Path)" -Level "UI" -ForegroundColor Yellow
                    Write-Log -Message "Creating directory..." -Level "UI" -ForegroundColor Yellow
                    
                    $createDirArgs = @(
                        "-i", "$($script:Config.ssh.key_path)",
                        "-p", "$($script:Config.ssh.port)",
                        "-o", "BatchMode=yes",
                        "-o", "StrictHostKeyChecking=accept-new",
                        "-o", "ConnectTimeout=10",
                        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                        "sudo mkdir -p '$($dir.Path)' && sudo chown $($dir.Owner):$($dir.Group) '$($dir.Path)' && sudo chmod $($dir.Permissions) '$($dir.Path)'"
                    )

                    $createDirOutput = & ssh @createDirArgs 2>&1
                    $createDirExitCode = $LASTEXITCODE

                    if ($createDirExitCode -eq 0) {
                        Write-Log -Message "Directory created and configured successfully: $($dir.Path)" -Level "UI" -ForegroundColor Green
                    } else {
                        Write-Log -Message "Failed to create directory: $($dir.Path)" -Level "UI" -ForegroundColor Red
                        Write-Log -Message "Output: $createDirOutput" -Level "UI" -ForegroundColor Red
                    }
                }
            }
        }
    }

    # Install dependencies
    Write-Log -Message "`nInstalling dependencies..." -Level "UI" -ForegroundColor Cyan
    Show-MenuDivider

    $successCount = 0
    $failureCount = 0

    if ($remoteInstall) {
        # Test SSH connection before proceeding
        Write-Log -Message "Testing SSH connection..." -Level "UI" -ForegroundColor Yellow

        try {
            # Validate SSH configuration
            if ($null -eq $script:Config.ssh.key_path) {
                Write-Log -Message "SSH key path is not defined in configuration." -Level "UI" -ForegroundColor Red
                Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
                return
            }

            # Check if key file exists
            if (-not (Test-Path -Path $script:Config.ssh.key_path)) {
                Write-Log -Message "SSH key file not found at: $($script:Config.ssh.key_path)" -Level "UI" -ForegroundColor Red
                Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
                return
            }

            # Log SSH connection details for debugging
            Write-Log -Message "Connecting to SSH server with the following parameters:" -Level "UI" -ForegroundColor Yellow
            Write-Log -Message "  - Host: $($script:Config.ssh.host)" -Level "UI" -ForegroundColor Yellow
            Write-Log -Message "  - Port: $($script:Config.ssh.port)" -Level "UI" -ForegroundColor Yellow
            Write-Log -Message "  - Username: $($script:Config.ssh.username)" -Level "UI" -ForegroundColor Yellow
            Write-Log -Message "  - Key Path: $($script:Config.ssh.key_path)" -Level "UI" -ForegroundColor Yellow

            $sshArgs = @(
                "-i", "$($script:Config.ssh.key_path)",
                "-p", "$($script:Config.ssh.port)",
                "-o", "BatchMode=yes",
                "-o", "StrictHostKeyChecking=accept-new",
                "-o", "ConnectTimeout=10",
                "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                "echo 'CONNECTION_OK'"
            )

            # Log the full SSH command for debugging
            $sshCommand = "ssh " + ($sshArgs -join " ")
            Write-Log -Message "Executing SSH command: $sshCommand" -Level "UI" -ForegroundColor Yellow

            $output = & ssh @sshArgs 2>&1
            $exitCode = $LASTEXITCODE

            if ($exitCode -eq 0 -and $output -like "*CONNECTION_OK*") {
                Write-Log -Message "SSH connection successful." -Level "UI" -ForegroundColor Green
            } else {
                Write-Log -Message "SSH connection failed. Cannot install dependencies." -Level "UI" -ForegroundColor Red
                Write-Log -Message "SSH command output: $output" -Level "UI" -ForegroundColor Red
                Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
                return
            }
        } catch {
            Write-Log -Message "Error testing SSH connection: $_" -Level "UI" -ForegroundColor Red
            Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
            return
        }

        # Update package repositories
        Write-Log -Message "Updating package repositories..." -Level "UI" -ForegroundColor Yellow
        try {
            $updateArgs = @(
                "-i", "$($script:Config.ssh.key_path)",
                "-p", "$($script:Config.ssh.port)",
                "-o", "BatchMode=yes",
                "-o", "StrictHostKeyChecking=accept-new",
                "-o", "ConnectTimeout=10",
                "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                "sudo apt-get update"
            )

            # Log the command for debugging
            $updateCommand = "ssh " + ($updateArgs -join " ")
            Write-Log -Message "Executing update command: $updateCommand" -Level "UI" -ForegroundColor Yellow

            $updateOutput = & ssh @updateArgs 2>&1
            if ($LASTEXITCODE -ne 0) {
                Write-Log -Message "Failed to update package repositories." -Level "UI" -ForegroundColor Red
                Write-Log -Message "Output: $updateOutput" -Level "UI" -ForegroundColor Red
            } else {
                Write-Log -Message "Package repositories updated successfully." -Level "UI" -ForegroundColor Green
            }
        } catch {
            Write-Log -Message "Error updating package repositories: $_" -Level "UI" -ForegroundColor Red
        }

        # Install each dependency
        foreach ($dependency in $script:Config.dependencies) {
            Write-Log -Message "`nInstalling $($dependency.name)..." -Level "UI" -ForegroundColor Cyan

            try {
                # First check if already installed
                $checkArgs = @(
                    "-i", "$($script:Config.ssh.key_path)",
                    "-p", "$($script:Config.ssh.port)",
                    "-o", "BatchMode=yes",
                    "-o", "StrictHostKeyChecking=accept-new",
                    "-o", "ConnectTimeout=10",
                    "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                    "$($dependency.check)"
                )

                # Log the command for debugging
                $checkCommand = "ssh " + ($checkArgs -join " ")
                Write-Log -Message "Checking if $($dependency.name) is already installed: $checkCommand" -Level "UI" -ForegroundColor Yellow

                $checkOutput = & ssh @checkArgs 2>&1
                $checkExitCode = $LASTEXITCODE

                if ($checkExitCode -eq 0) {
                    Write-Log -Message "$($dependency.name) is already installed: $($checkOutput)" -Level "UI" -ForegroundColor Green
                    $successCount++
                    continue
                }

                # Install the dependency
                $installArgs = @(
                    "-i", "$($script:Config.ssh.key_path)",
                    "-p", "$($script:Config.ssh.port)",
                    "-o", "BatchMode=yes",
                    "-o", "StrictHostKeyChecking=accept-new",
                    "-o", "ConnectTimeout=10",
                    "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                    "sudo $($dependency.command)"
                )

                # Log the command for debugging
                $installCommand = "ssh " + ($installArgs -join " ")
                Write-Log -Message "Installing $($dependency.name): $installCommand" -Level "UI" -ForegroundColor Yellow
                Write-Log -Message "Running: sudo $($dependency.command)" -Level "UI" -ForegroundColor White

                $installOutput = & ssh @installArgs 2>&1
                $installExitCode = $LASTEXITCODE

                if ($installExitCode -eq 0) {
                    # Verify installation
                    $verifyArgs = @(
                        "-i", "$($script:Config.ssh.key_path)",
                        "-p", "$($script:Config.ssh.port)",
                        "-o", "BatchMode=yes",
                        "-o", "StrictHostKeyChecking=accept-new",
                        "-o", "ConnectTimeout=10",
                        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                        "$($dependency.check)"
                    )

                    # Log the command for debugging
                    $verifyCommand = "ssh " + ($verifyArgs -join " ")
                    Write-Log -Message "Verifying $($dependency.name) installation: $verifyCommand" -Level "UI" -ForegroundColor Yellow

                    $verifyOutput = & ssh @verifyArgs 2>&1
                    $verifyExitCode = $LASTEXITCODE

                    if ($verifyExitCode -eq 0) {
                        Write-Log -Message "$($dependency.name) installed successfully: $($verifyOutput)" -Level "UI" -ForegroundColor Green
                        $successCount++
                    } else {
                        Write-Log -Message "$($dependency.name) installation verification failed." -Level "UI" -ForegroundColor Red
                        Write-Log -Message "Output: $verifyOutput" -Level "UI" -ForegroundColor Red
                        $failureCount++
                    }
                } else {
                    Write-Log -Message "Failed to install $($dependency.name)." -Level "UI" -ForegroundColor Red
                    Write-Log -Message "Output: $installOutput" -Level "UI" -ForegroundColor Red
                    $failureCount++
                }
            } catch {
                Write-Log -Message "`nError installing $($dependency.name): $_" -Level "UI" -ForegroundColor Red
                $failureCount++
            }
        }
    } else {
        # Local installation simulation (since we can't actually install on the local Windows machine)
        foreach ($dependency in $script:Config.dependencies) {
            Write-Log -Message "`nInstalling $($dependency.name)..." -Level "UI" -ForegroundColor Cyan
            Write-Log -Message "Simulating local installation of $($dependency.name)..." -Level "UI" -ForegroundColor Yellow
            Start-Sleep -Seconds 2  # Simulate installation time
            Write-Log -Message "$($dependency.name) installation simulated successfully." -Level "UI" -ForegroundColor Green
            $successCount++
        }
    }

    # Configure PostgreSQL if it was installed
    if ($remoteInstall -and $successCount -gt 0) {
        $postgresInstalled = $script:Config.dependencies | Where-Object { $_.name -eq "PostgreSQL Server" -and $_.postInstall }
        if ($postgresInstalled) {
            Write-Log -Message "`nConfiguring PostgreSQL..." -Level "UI" -ForegroundColor Cyan
            Show-MenuDivider

            # Check if dedicated PostgreSQL volume exists
            Write-Log -Message "Checking for dedicated PostgreSQL volume..." -Level "UI" -ForegroundColor Yellow
            $checkVolumeArgs = @(
                "-i", "$($script:Config.ssh.key_path)",
                "-p", "$($script:Config.ssh.port)",
                "-o", "BatchMode=yes",
                "-o", "StrictHostKeyChecking=accept-new",
                "-o", "ConnectTimeout=10",
                "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                "df -h /pgsql/data 2>/dev/null || echo 'NO_DEDICATED_VOLUME'"
            )

            $volumeOutput = & ssh @checkVolumeArgs 2>&1
            $hasDedicatedVolume = $volumeOutput -notlike "*NO_DEDICATED_VOLUME*"

            if ($hasDedicatedVolume) {
                Write-Log -Message "Found dedicated PostgreSQL volume at /pgsql/data" -Level "UI" -ForegroundColor Green
                Write-Log -Message "Volume info: $volumeOutput" -Level "UI" -ForegroundColor White

                # Check if PostgreSQL is already using the dedicated volume
                $checkCurrentDataDirArgs = @(
                    "-i", "$($script:Config.ssh.key_path)",
                    "-p", "$($script:Config.ssh.port)",
                    "-o", "BatchMode=yes",
                    "-o", "StrictHostKeyChecking=accept-new",
                    "-o", "ConnectTimeout=10",
                    "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                    "sudo -u postgres psql -c 'SHOW data_directory;' 2>/dev/null || echo 'POSTGRES_NOT_RUNNING'"
                )

                $currentDataDir = & ssh @checkCurrentDataDirArgs 2>&1
                $needsMove = $currentDataDir -like "*var/lib/postgresql*"

                if ($needsMove) {
                    Write-Log -Message "PostgreSQL is using system volume, moving to dedicated volume..." -Level "UI" -ForegroundColor Yellow

                    # Stop PostgreSQL
                    Write-Log -Message "Stopping PostgreSQL service..." -Level "UI" -ForegroundColor Yellow
                    $stopPgArgs = @(
                        "-i", "$($script:Config.ssh.key_path)",
                        "-p", "$($script:Config.ssh.port)",
                        "-o", "BatchMode=yes",
                        "-o", "StrictHostKeyChecking=accept-new",
                        "-o", "ConnectTimeout=10",
                        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                        "sudo systemctl stop postgresql"
                    )

                    $stopOutput = & ssh @stopPgArgs 2>&1
                    if ($LASTEXITCODE -eq 0) {
                        Write-Log -Message "PostgreSQL stopped successfully" -Level "UI" -ForegroundColor Green

                        # Prepare dedicated volume
                        Write-Log -Message "Preparing dedicated volume for PostgreSQL..." -Level "UI" -ForegroundColor Yellow
                        $prepareVolumeArgs = @(
                            "-i", "$($script:Config.ssh.key_path)",
                            "-p", "$($script:Config.ssh.port)",
                            "-o", "BatchMode=yes",
                            "-o", "StrictHostKeyChecking=accept-new",
                            "-o", "ConnectTimeout=10",
                            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                            "sudo chown postgres:postgres /pgsql/data && sudo chmod 700 /pgsql/data && sudo mkdir -p /pgsql/data/17 && sudo chown postgres:postgres /pgsql/data/17"
                        )

                        $prepareOutput = & ssh @prepareVolumeArgs 2>&1
                        if ($LASTEXITCODE -eq 0) {
                            Write-Log -Message "Dedicated volume prepared successfully" -Level "UI" -ForegroundColor Green

                            # Move PostgreSQL data
                            Write-Log -Message "Moving PostgreSQL data to dedicated volume..." -Level "UI" -ForegroundColor Yellow
                            $moveDataArgs = @(
                                "-i", "$($script:Config.ssh.key_path)",
                                "-p", "$($script:Config.ssh.port)",
                                "-o", "BatchMode=yes",
                                "-o", "StrictHostKeyChecking=accept-new",
                                "-o", "ConnectTimeout=10",
                                "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                                "sudo rsync -av /var/lib/postgresql/17/main/ /pgsql/data/17/main/ && sudo chown -R postgres:postgres /pgsql/data/17/"
                            )

                            $moveOutput = & ssh @moveDataArgs 2>&1
                            if ($LASTEXITCODE -eq 0) {
                                Write-Log -Message "PostgreSQL data moved successfully" -Level "UI" -ForegroundColor Green

                                # Update PostgreSQL configuration
                                Write-Log -Message "Updating PostgreSQL configuration..." -Level "UI" -ForegroundColor Yellow
                                $updateConfigArgs = @(
                                    "-i", "$($script:Config.ssh.key_path)",
                                    "-p", "$($script:Config.ssh.port)",
                                    "-o", "BatchMode=yes",
                                    "-o", "StrictHostKeyChecking=accept-new",
                                    "-o", "ConnectTimeout=10",
                                    "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                                    "sudo sed -i `"s|data_directory = '/var/lib/postgresql/17/main'|data_directory = '/pgsql/data/17/main'|g`" /etc/postgresql/17/main/postgresql.conf"
                                )

                                $configOutput = & ssh @updateConfigArgs 2>&1
                                if ($LASTEXITCODE -eq 0) {
                                    Write-Log -Message "PostgreSQL configuration updated successfully" -Level "UI" -ForegroundColor Green
                                } else {
                                    Write-Log -Message "Failed to update PostgreSQL configuration: $configOutput" -Level "UI" -ForegroundColor Red
                                }
                            } else {
                                Write-Log -Message "Failed to move PostgreSQL data: $moveOutput" -Level "UI" -ForegroundColor Red
                            }
                        } else {
                            Write-Log -Message "Failed to prepare dedicated volume: $prepareOutput" -Level "UI" -ForegroundColor Red
                        }
                    } else {
                        Write-Log -Message "Failed to stop PostgreSQL: $stopOutput" -Level "UI" -ForegroundColor Red
                    }
                } else {
                    Write-Log -Message "PostgreSQL is already using dedicated volume" -Level "UI" -ForegroundColor Green
                }
            } else {
                Write-Log -Message "No dedicated PostgreSQL volume found, using system volume" -Level "UI" -ForegroundColor Yellow
            }

            # Start and enable PostgreSQL service
            Write-Log -Message "Starting PostgreSQL service..." -Level "UI" -ForegroundColor Yellow
            $startPgArgs = @(
                "-i", "$($script:Config.ssh.key_path)",
                "-p", "$($script:Config.ssh.port)",
                "-o", "BatchMode=yes",
                "-o", "StrictHostKeyChecking=accept-new",
                "-o", "ConnectTimeout=10",
                "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                "sudo systemctl start postgresql && sudo systemctl enable postgresql"
            )

            $startPgOutput = & ssh @startPgArgs 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Log -Message "PostgreSQL service started and enabled successfully" -Level "UI" -ForegroundColor Green

                # Verify data directory
                $verifyDataDirArgs = @(
                    "-i", "$($script:Config.ssh.key_path)",
                    "-p", "$($script:Config.ssh.port)",
                    "-o", "BatchMode=yes",
                    "-o", "StrictHostKeyChecking=accept-new",
                    "-o", "ConnectTimeout=10",
                    "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                    "sudo -u postgres psql -c 'SHOW data_directory;'"
                )

                $verifyOutput = & ssh @verifyDataDirArgs 2>&1
                Write-Log -Message "Current PostgreSQL data directory: $verifyOutput" -Level "UI" -ForegroundColor White

                # Set postgres user password
                Write-Log -Message "Configuring PostgreSQL authentication..." -Level "UI" -ForegroundColor Yellow
                $setPgPasswordArgs = @(
                    "-i", "$($script:Config.ssh.key_path)",
                    "-p", "$($script:Config.ssh.port)",
                    "-o", "BatchMode=yes",
                    "-o", "StrictHostKeyChecking=accept-new",
                    "-o", "ConnectTimeout=10",
                    "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                    "sudo -u postgres psql -c `"ALTER USER postgres PASSWORD 'postgres';`""
                )

                $setPgPasswordOutput = & ssh @setPgPasswordArgs 2>&1
                if ($LASTEXITCODE -eq 0) {
                    Write-Log -Message "PostgreSQL postgres user password set successfully" -Level "UI" -ForegroundColor Green
                } else {
                    Write-Log -Message "Failed to set postgres user password: $setPgPasswordOutput" -Level "UI" -ForegroundColor Yellow
                }

                # Get database configuration from config
                $dbUser = if ($script:Config.database.user) { $script:Config.database.user } else { "database_service_user" }
                $dbName = if ($script:Config.database.name) { $script:Config.database.name } else { "database_service" }
                $dbPassword = if ($script:Config.database.password) { $script:Config.database.password } else { "CHANGE_THIS_PASSWORD" }

                # Create database service user and database
                Write-Log -Message "Creating database service user and database..." -Level "UI" -ForegroundColor Yellow
                Write-Log -Message "  Database: $dbName" -Level "UI" -ForegroundColor White
                Write-Log -Message "  User: $dbUser" -Level "UI" -ForegroundColor White

                $createDbUserArgs = @(
                    "-i", "$($script:Config.ssh.key_path)",
                    "-p", "$($script:Config.ssh.port)",
                    "-o", "BatchMode=yes",
                    "-o", "StrictHostKeyChecking=accept-new",
                    "-o", "ConnectTimeout=10",
                    "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
                    "sudo -u postgres psql -c `"CREATE USER $dbUser WITH PASSWORD '$dbPassword';`" && sudo -u postgres psql -c `"CREATE DATABASE $dbName OWNER $dbUser;`""
                )

                $createDbUserOutput = & ssh @createDbUserArgs 2>&1
                if ($LASTEXITCODE -eq 0) {
                    Write-Log -Message "Database service user and database created successfully" -Level "UI" -ForegroundColor Green
                    Write-Log -Message "Database: $dbName" -Level "UI" -ForegroundColor White
                    Write-Log -Message "User: $dbUser" -Level "UI" -ForegroundColor White
                    if ($dbPassword -eq "CHANGE_THIS_PASSWORD") {
                        Write-Log -Message "Password: $dbPassword (update in config.json)" -Level "UI" -ForegroundColor Yellow
                    } else {
                        Write-Log -Message "Password: [Using password from config.json]" -Level "UI" -ForegroundColor Green
                    }
                } else {
                    Write-Log -Message "Database user/database may already exist or creation failed: $createDbUserOutput" -Level "UI" -ForegroundColor Yellow
                }

            } else {
                Write-Log -Message "Failed to start PostgreSQL service: $startPgOutput" -Level "UI" -ForegroundColor Red
            }
        }
    }

    # Display installation summary
    Show-MenuDivider
    Write-Log -Message "Installation Summary:" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "  - Total dependencies: $(@($script:Config.dependencies).Count)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  - Successfully installed: $successCount" -Level "UI" -ForegroundColor Green

    if ($failureCount -gt 0) {
        Write-Log -Message "  - Failed to install: $failureCount" -Level "UI" -ForegroundColor Red
    } else {
        Write-Log -Message "  - Failed to install: $failureCount" -Level "UI" -ForegroundColor White
    }

    # Show next steps if PostgreSQL was configured
    if ($remoteInstall -and $successCount -gt 0) {
        $postgresInstalled = $script:Config.dependencies | Where-Object { $_.name -eq "PostgreSQL Server" }
        if ($postgresInstalled) {
            Write-Log -Message "`nNext Steps:" -Level "UI" -ForegroundColor Cyan
            Write-Log -Message "1. Update database password in config.json (change CHANGE_THIS_PASSWORD)" -Level "UI" -ForegroundColor White
            Write-Log -Message "2. Build the application (Option 9)" -Level "UI" -ForegroundColor White
            Write-Log -Message "3. Install the service (Option 10)" -Level "UI" -ForegroundColor White
            Write-Log -Message "4. Initialize database schema (Option 11)" -Level "UI" -ForegroundColor White
            Write-Log -Message "5. Start the service (Option 12)" -Level "UI" -ForegroundColor White
            Write-Log -Message " " -Level "UI"
            Write-Log -Message "PostgreSQL Configuration:" -Level "UI" -ForegroundColor Cyan
            $dbUser = if ($script:Config.database.user) { $script:Config.database.user } else { "database_service_user" }
            $dbName = if ($script:Config.database.name) { $script:Config.database.name } else { "database_service" }
            $dbPassword = if ($script:Config.database.password) { $script:Config.database.password } else { "CHANGE_THIS_PASSWORD" }
            Write-Log -Message "- Database: $dbName" -Level "UI" -ForegroundColor White
            Write-Log -Message "- User: $dbUser" -Level "UI" -ForegroundColor White
            if ($dbPassword -eq "CHANGE_THIS_PASSWORD") {
                Write-Log -Message "- Password: $dbPassword (update this!)" -Level "UI" -ForegroundColor Yellow
            } else {
                Write-Log -Message "- Password: [Configured in config.json]" -Level "UI" -ForegroundColor Green
            }
            if ($hasDedicatedVolume) {
                Write-Log -Message "- Data Location: /pgsql/data/17/main (dedicated 98GB volume)" -Level "UI" -ForegroundColor Green
            } else {
                Write-Log -Message "- Data Location: /var/lib/postgresql/17/main (system volume)" -Level "UI" -ForegroundColor Yellow
            }
        }
    }

    # Disable UI Mode after menu display
    if (Get-Command -Name Disable-UIMode -ErrorAction SilentlyContinue) {
        Disable-UIMode
    }

    Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
    return
}

# Export the function
Export-ModuleMember -Function Install-Dependencies
