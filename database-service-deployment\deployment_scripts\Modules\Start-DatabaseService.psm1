# Start Database Service Module

# Import common module
Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force
# Import Logger module
Import-Module -Name "$PSScriptRoot\Logger\Logger.psm1" -Force

function Start-DatabaseService {
    Clear-Host
    # Enable UI Mode for menu display
    if (Get-Command -Name Enable-UIMode -ErrorAction SilentlyContinue) {
        Enable-UIMode
    }
    Write-Log -Message "========================================================" -Level "Info" -Component "DatabaseService"
    Write-Log -Message "               Start Database Service                 " -Level "Info" -Component "DatabaseService"
    Write-Log -Message "========================================================" -Level "Info" -Component "DatabaseService"
    Write-Log -Message " " -Level "Info" -Component "DatabaseService"
    # Check if global Config is available
    if ($null -eq $global:Config) {
        Write-Log -Message "Global configuration not found. Attempting to load..." -Level "Warning" -Component "DatabaseService"
        $global:Config = Get-Configuration
    }

    if ($null -eq $global:Config -or $null -eq $global:Config.ssh) {
        Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error" -Component "DatabaseService"
        if (Get-Command -Name Wait-ForUser -ErrorAction SilentlyContinue) {
            Wait-ForUser
        } else {
            Write-Host "Press Enter to continue..." -ForegroundColor Yellow
            Read-Host | Out-Null
        }
        if (Get-Command -Name Show-MainMenu -ErrorAction SilentlyContinue) {
            Show-MainMenu
        }
        return
    }

    # Use global config
    $Config = $global:Config

    Write-Log -Message "Starting database service on $($Config.ssh.host)..." -Level "Info" -Component "DatabaseService"

    # Start the database service
    Write-Log -Message "Starting database service..." -Level "Info" -Component "DatabaseService"
    $startCmd = "sudo systemctl start database-service"
    Invoke-RemoteCommand -Command $startCmd

    # Check if the service started successfully
    Write-Log -Message "Checking service status..." -Level "Info" -Component "DatabaseService"
    $statusCmd = "sudo systemctl is-active database-service"
    $serviceStatus = Invoke-RemoteCommand -Command $statusCmd -Silent
    if ([string]::IsNullOrEmpty($serviceStatus)) { $serviceStatus = "unknown" }

    if ($serviceStatus -eq "active") {
        Write-Log -Message "Database service started successfully!" -Level "Info" -Component "DatabaseService"

        # Show detailed status
        Write-Log -Message "Service Status:" -Level "Info" -Component "DatabaseService"
        $detailedStatusCmd = "sudo systemctl status database-service --no-pager"
        Invoke-RemoteCommand -Command $detailedStatusCmd
    } else {
        Write-Log -Message "Failed to start database service!" -Level "Error" -Component "DatabaseService"
        Write-Log -Message "Service status: $serviceStatus" -Level "Info" -Component "DatabaseService"

        # Show error logs
        Write-Log -Message "Recent service logs:" -Level "Info" -Component "DatabaseService"
        $logsCmd = "sudo journalctl -u database-service -n 10 --no-pager"
        Invoke-RemoteCommand -Command $logsCmd
    }
    
    # Disable UI Mode after menu display
    if (Get-Command -Name Disable-UIMode -ErrorAction SilentlyContinue) {
        Disable-UIMode
    }
    
    if (Get-Command -Name Wait-ForUser -ErrorAction SilentlyContinue) {
        Wait-ForUser
    } else {
        Write-Host "Press Enter to continue..." -ForegroundColor Yellow
        Read-Host | Out-Null
    }
    
    if (Get-Command -Name Show-MainMenu -ErrorAction SilentlyContinue) {
        Show-MainMenu
    }
}

# Export the function
Export-ModuleMember -Function Start-DatabaseService
