Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
CMake Warning (dev) at CMakeLists.txt:46 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  5%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
[ 11%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleHealthCheck(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:22:94: warning: unused parameter ‘request’ [-Wunused-parameter]
   22 | std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
      |                                                                         ~~~~~~~~~~~~~~~~~~~~~^~~~~~~
[ 16%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 22%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
[ 27%] Building CXX object CMakeFiles/database-service.dir/src/core/transaction.cpp.o
[ 33%] Building CXX object CMakeFiles/database-service.dir/src/database_service.cpp.o
In file included from /home/<USER>/database-service-build/src/database_service.cpp:1:
/home/<USER>/database-service-build/include/database-service/database_service.hpp:108:19: error: ‘nlohmann’ was not declared in this scope
  108 |     std::expected<nlohmann::json, std::string> getDatabaseMetrics() const;
      |                   ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/database_service.hpp:108:46: error: wrong number of template arguments (1, should be 2)
  108 |     std::expected<nlohmann::json, std::string> getDatabaseMetrics() const;
      |                                              ^
In file included from /home/<USER>/database-service-build/include/database-service/database_service.hpp:6:
/usr/include/c++/14/expected:61:11: note: provided for ‘template<class _Tp, class _Er> class std::expected’
   61 |     class expected;
      |           ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/database_service.hpp:108:10: error: ‘<expression error>’ in namespace ‘std’ does not name a type
  108 |     std::expected<nlohmann::json, std::string> getDatabaseMetrics() const;
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/database_service.hpp:114:19: error: ‘nlohmann’ was not declared in this scope
  114 |     std::expected<nlohmann::json, std::string> getConnectionPoolMetrics() const;
      |                   ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/database_service.hpp:114:46: error: wrong number of template arguments (1, should be 2)
  114 |     std::expected<nlohmann::json, std::string> getConnectionPoolMetrics() const;
      |                                              ^
/usr/include/c++/14/expected:61:11: note: provided for ‘template<class _Tp, class _Er> class std::expected’
   61 |     class expected;
      |           ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/database_service.hpp:114:10: error: ‘<expression error>’ in namespace ‘std’ does not name a type
  114 |     std::expected<nlohmann::json, std::string> getConnectionPoolMetrics() const;
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/database_service.hpp:120:19: error: ‘nlohmann’ was not declared in this scope
  120 |     std::expected<nlohmann::json, std::string> getQueryPerformanceMetrics() const;
      |                   ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/database_service.hpp:120:46: error: wrong number of template arguments (1, should be 2)
  120 |     std::expected<nlohmann::json, std::string> getQueryPerformanceMetrics() const;
      |                                              ^
/usr/include/c++/14/expected:61:11: note: provided for ‘template<class _Tp, class _Er> class std::expected’
   61 |     class expected;
      |           ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/database_service.hpp:120:10: error: ‘<expression error>’ in namespace ‘std’ does not name a type
  120 |     std::expected<nlohmann::json, std::string> getQueryPerformanceMetrics() const;
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
[ 38%] Building CXX object CMakeFiles/database-service.dir/src/main.cpp.o
/home/<USER>/database-service-build/src/database_service.cpp: In member function ‘std::expected<void, std::__cxx11::basic_string<char> > dbservice::DatabaseService::initialize()’:
/home/<USER>/database-service-build/src/database_service.cpp:31:22: error: request for member ‘load’ in ‘((dbservice::DatabaseService*)this)->dbservice::DatabaseService::initialized_’, which is of non-class type ‘bool’
   31 |     if (initialized_.load(std::memory_order_relaxed)) {
      |                      ^~~~
/home/<USER>/database-service-build/src/database_service.cpp:216:22: error: request for member ‘store’ in ‘((dbservice::DatabaseService*)this)->dbservice::DatabaseService::initialized_’, which is of non-class type ‘bool’
  216 |         initialized_.store(true, std::memory_order_relaxed);
      |                      ^~~~~
/home/<USER>/database-service-build/src/database_service.cpp: In member function ‘std::expected<void, std::__cxx11::basic_string<char> > dbservice::DatabaseService::start()’:
/home/<USER>/database-service-build/src/database_service.cpp:248:23: error: request for member ‘load’ in ‘((dbservice::DatabaseService*)this)->dbservice::DatabaseService::initialized_’, which is of non-class type ‘bool’
  248 |     if (!initialized_.load(std::memory_order_relaxed)) {
      |                       ^~~~
/home/<USER>/database-service-build/src/database_service.cpp:254:18: error: request for member ‘load’ in ‘((dbservice::DatabaseService*)this)->dbservice::DatabaseService::running_’, which is of non-class type ‘bool’
  254 |     if (running_.load(std::memory_order_relaxed)) {
      |                  ^~~~
/home/<USER>/database-service-build/src/database_service.cpp:265:105: error: ‘struct dbservice::api::ApiError’ has no member named ‘toString’
  265 |             std::string errorMsg = std::format("Failed to start API server: {}", apiStartResult.error().toString());
      |                                                                                                         ^~~~~~~~
/home/<USER>/database-service-build/src/database_service.cpp:270:18: error: request for member ‘store’ in ‘((dbservice::DatabaseService*)this)->dbservice::DatabaseService::running_’, which is of non-class type ‘bool’
  270 |         running_.store(true, std::memory_order_relaxed);
      |                  ^~~~~
/home/<USER>/database-service-build/src/database_service.cpp: In member function ‘void dbservice::DatabaseService::stop()’:
/home/<USER>/database-service-build/src/database_service.cpp:281:19: error: request for member ‘load’ in ‘((dbservice::DatabaseService*)this)->dbservice::DatabaseService::running_’, which is of non-class type ‘bool’
  281 |     if (!running_.load(std::memory_order_relaxed)) {
      |                   ^~~~
/home/<USER>/database-service-build/src/database_service.cpp:293:105: error: ‘struct dbservice::api::ApiError’ has no member named ‘toString’
  293 |                 utils::Logger::error(std::format("Error stopping API server: {}", apiStopResult.error().toString()));
      |                                                                                                         ^~~~~~~~
/home/<USER>/database-service-build/src/database_service.cpp:304:18: error: request for member ‘store’ in ‘((dbservice::DatabaseService*)this)->dbservice::DatabaseService::running_’, which is of non-class type ‘bool’
  304 |         running_.store(false, std::memory_order_relaxed);
      |                  ^~~~~
/home/<USER>/database-service-build/src/database_service.cpp: In lambda function:
/home/<USER>/database-service-build/src/database_service.cpp:322:57: error: request for member ‘load’ in ‘this->dbservice::DatabaseService::running_’, which is of non-class type ‘bool’
  322 |     waitCondition_.wait(lock, [this] { return !running_.load(std::memory_order_relaxed); });
      |                                                         ^~~~
/home/<USER>/database-service-build/src/database_service.cpp: In member function ‘bool dbservice::DatabaseService::isRunning() const’:
/home/<USER>/database-service-build/src/database_service.cpp:326:21: error: request for member ‘load’ in ‘((const dbservice::DatabaseService*)this)->dbservice::DatabaseService::running_’, which is of non-class type ‘const bool’
  326 |     return running_.load(std::memory_order_relaxed);
      |                     ^~~~
/home/<USER>/database-service-build/src/database_service.cpp: At global scope:
/home/<USER>/database-service-build/src/database_service.cpp:345:44: error: no declaration matches ‘std::expected<nlohmann::json_abi_v3_11_3::basic_json<>, std::__cxx11::basic_string<char> > dbservice::DatabaseService::getDatabaseMetrics() const’
  345 | std::expected<nlohmann::json, std::string> DatabaseService::getDatabaseMetrics() const {
      |                                            ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/database_service.cpp:345:44: note: no functions named ‘std::expected<nlohmann::json_abi_v3_11_3::basic_json<>, std::__cxx11::basic_string<char> > dbservice::DatabaseService::getDatabaseMetrics() const’
/home/<USER>/database-service-build/include/database-service/database_service.hpp:33:7: note: ‘class dbservice::DatabaseService’ defined here
   33 | class DatabaseService : public std::enable_shared_from_this<DatabaseService> {
      |       ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/database_service.cpp:372:44: error: no declaration matches ‘std::expected<nlohmann::json_abi_v3_11_3::basic_json<>, std::__cxx11::basic_string<char> > dbservice::DatabaseService::getConnectionPoolMetrics() const’
  372 | std::expected<nlohmann::json, std::string> DatabaseService::getConnectionPoolMetrics() const {
      |                                            ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/database_service.cpp:372:44: note: no functions named ‘std::expected<nlohmann::json_abi_v3_11_3::basic_json<>, std::__cxx11::basic_string<char> > dbservice::DatabaseService::getConnectionPoolMetrics() const’
/home/<USER>/database-service-build/include/database-service/database_service.hpp:33:7: note: ‘class dbservice::DatabaseService’ defined here
   33 | class DatabaseService : public std::enable_shared_from_this<DatabaseService> {
      |       ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/database_service.cpp:389:44: error: no declaration matches ‘std::expected<nlohmann::json_abi_v3_11_3::basic_json<>, std::__cxx11::basic_string<char> > dbservice::DatabaseService::getQueryPerformanceMetrics() const’
  389 | std::expected<nlohmann::json, std::string> DatabaseService::getQueryPerformanceMetrics() const {
      |                                            ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/database_service.cpp:389:44: note: no functions named ‘std::expected<nlohmann::json_abi_v3_11_3::basic_json<>, std::__cxx11::basic_string<char> > dbservice::DatabaseService::getQueryPerformanceMetrics() const’
/home/<USER>/database-service-build/include/database-service/database_service.hpp:33:7: note: ‘class dbservice::DatabaseService’ defined here
   33 | class DatabaseService : public std::enable_shared_from_this<DatabaseService> {
      |       ^~~~~~~~~~~~~~~
In file included from /home/<USER>/database-service-build/src/main.cpp:1:
/home/<USER>/database-service-build/include/database-service/database_service.hpp:108:19: error: ‘nlohmann’ was not declared in this scope
  108 |     std::expected<nlohmann::json, std::string> getDatabaseMetrics() const;
      |                   ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/database_service.hpp:108:46: error: wrong number of template arguments (1, should be 2)
  108 |     std::expected<nlohmann::json, std::string> getDatabaseMetrics() const;
      |                                              ^
In file included from /home/<USER>/database-service-build/include/database-service/database_service.hpp:6:
/usr/include/c++/14/expected:61:11: note: provided for ‘template<class _Tp, class _Er> class std::expected’
   61 |     class expected;
      |           ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/database_service.hpp:108:10: error: ‘<expression error>’ in namespace ‘std’ does not name a type
  108 |     std::expected<nlohmann::json, std::string> getDatabaseMetrics() const;
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/database_service.hpp:114:19: error: ‘nlohmann’ was not declared in this scope
  114 |     std::expected<nlohmann::json, std::string> getConnectionPoolMetrics() const;
      |                   ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/database_service.hpp:114:46: error: wrong number of template arguments (1, should be 2)
  114 |     std::expected<nlohmann::json, std::string> getConnectionPoolMetrics() const;
      |                                              ^
/usr/include/c++/14/expected:61:11: note: provided for ‘template<class _Tp, class _Er> class std::expected’
   61 |     class expected;
      |           ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/database_service.hpp:114:10: error: ‘<expression error>’ in namespace ‘std’ does not name a type
  114 |     std::expected<nlohmann::json, std::string> getConnectionPoolMetrics() const;
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/database_service.hpp:120:19: error: ‘nlohmann’ was not declared in this scope
  120 |     std::expected<nlohmann::json, std::string> getQueryPerformanceMetrics() const;
      |                   ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/database_service.hpp:120:46: error: wrong number of template arguments (1, should be 2)
  120 |     std::expected<nlohmann::json, std::string> getQueryPerformanceMetrics() const;
      |                                              ^
/usr/include/c++/14/expected:61:11: note: provided for ‘template<class _Tp, class _Er> class std::expected’
   61 |     class expected;
      |           ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/database_service.hpp:120:10: error: ‘<expression error>’ in namespace ‘std’ does not name a type
  120 |     std::expected<nlohmann::json, std::string> getQueryPerformanceMetrics() const;
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
make[2]: *** [CMakeFiles/database-service.dir/build.make:149: CMakeFiles/database-service.dir/src/database_service.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[2]: *** [CMakeFiles/database-service.dir/build.make:163: CMakeFiles/database-service.dir/src/main.cpp.o] Error 1
make[1]: *** [CMakeFiles/Makefile2:109: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
