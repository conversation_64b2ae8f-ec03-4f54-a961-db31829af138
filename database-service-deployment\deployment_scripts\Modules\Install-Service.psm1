{{ ... }}
# Install Service Module

# Import common module
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
# Import Logger module
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force

# Safe logging wrapper to prevent empty string errors
function Write-SafeLog {
    param(
        [string]$Message,
        [string]$Level = "Info",
        [string]$Component = "Service"
    )
    if ([string]::IsNullOrEmpty($Message)) {
        $Message = "[Empty message]"
    }
    Write-Log -Message $Message -Level $Level -Component $Component
}

function Install-Service {
    try {
        Clear-Host
        Write-SafeLog -Message "========== Install Service ==========" -Level "Info"
        Write-SafeLog -Message "               Install Service" -Level "Info"
        Write-SafeLog -Message "========== Install Service ==========" -Level "Info"

        # Check if global Config is available
        if ($null -eq $global:Config) {
            Write-SafeLog -Message "Global configuration not found. Attempting to load..." -Level "Warning"
            try {
                $global:Config = Get-Configuration
            } catch {
                Write-SafeLog -Message "Failed to load configuration: $($_.Exception.Message)" -Level "Error"
                Wait-ForUser
                return
            }
        }

        if ($null -eq $global:Config -or $null -eq $global:Config.ssh) {
            Write-SafeLog -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error"
            Wait-ForUser
            return
        }

        # Use global config
        $Config = $global:Config

        # Validate required configuration sections
        if ($null -eq $Config.project) {
            Write-SafeLog -Message "Project configuration section missing" -Level "Error"
            Wait-ForUser
            return
        }

        if ($null -eq $Config.service) {
            Write-SafeLog -Message "Service configuration section missing" -Level "Error"
            Wait-ForUser
            return
        }
    }
    catch {
        Write-SafeLog -Message "Error in Install-Service initialization: $($_.Exception.Message)" -Level "Error"
        Write-SafeLog -Message "Stack trace: $($_.ScriptStackTrace)" -Level "Error"
        Wait-ForUser
        return
    }

    try {
        # Get the build directory path
    $buildDir = $Config.project.remote_build_dir
    if ([string]::IsNullOrEmpty($buildDir)) {
        $sshUsername = $Config.ssh.username
        if ([string]::IsNullOrEmpty($sshUsername)) {
            Write-SafeLog -Message "SSH username not found in configuration." -Level "Error"
            Wait-ForUser
            return
        }
        $buildDir = "/home/<USER>/database-service-build"
        Write-SafeLog -Message "Remote build directory not specified in config. Using default: $buildDir" -Level "Warning"
    }

    Write-SafeLog -Message "Proceeding with installation from build directory: $buildDir" -Level "Info"
    Write-SafeLog -Message "Expected executable location: $buildDir/build/bin/database-service" -Level "Info"

    # Skip existence checks and proceed with installation
    # (Build was successful, so executable should exist)

    # Create the installation directory
    $installDir = $Config.project.remote_install_dir
    if ([string]::IsNullOrEmpty($installDir)) {
        $installDir = "/opt/database-service"
        Write-SafeLog -Message "Remote install directory not specified in config. Using default: $installDir" -Level "Warning"
    }
    Write-SafeLog -Message "Creating installation directory: $installDir" -Level "Info"
    $createDirCmd = "sudo mkdir -p $installDir/bin $installDir/config $installDir/logs"
    Invoke-RemoteCommand -Command $createDirCmd

    # Copy the compiled executable from build directory to installation directory
    Write-SafeLog -Message "Copying executable from build directory to installation directory..." -Level "Info"
    $copyExeCmd = "sudo cp $buildDir/build/bin/database-service $installDir/bin/"
    Invoke-RemoteCommand -Command $copyExeCmd

    # Copy configuration files
    Write-SafeLog -Message "Copying configuration files..." -Level "Info"
    $copyConfigCmd = "sudo cp $buildDir/source/config/config.json $installDir/config/config.json"
    Invoke-RemoteCommand -Command $copyConfigCmd

    # Create required directories
    Write-SafeLog -Message "Creating required directories..." -Level "Info"
    $createDirsCmd = @"
sudo mkdir -p /var/log/database-service && \
sudo mkdir -p $installDir/schemas && \
sudo chown -R database-service:database-service /var/log/database-service && \
sudo chmod 755 /var/log/database-service
"@
    Invoke-RemoteCommand -Command $createDirsCmd

    # Get service user and group early for use in file operations
    $serviceUser = $Config.service.user
    $serviceGroup = $Config.service.group

    if ([string]::IsNullOrEmpty($serviceUser)) {
        $serviceUser = "database-service"
        Write-SafeLog -Message "Service user not found in configuration. Using default: $serviceUser" -Level "Warning"
    }
    if ([string]::IsNullOrEmpty($serviceGroup)) {
        $serviceGroup = "database-service"
        Write-SafeLog -Message "Service group not found in configuration. Using default: $serviceGroup" -Level "Warning"
    }

    # Copy schema files from local deployment_files to server
    Write-SafeLog -Message "Copying schema files..." -Level "Info"
    $localSchemaDir = Join-Path (Split-Path -Parent $PSScriptRoot) "deployment_files\schemas"

    if (Test-Path $localSchemaDir) {
        $schemaFiles = Get-ChildItem -Path $localSchemaDir -Filter "*.sql"
        if ($schemaFiles.Count -gt 0) {
            Write-SafeLog -Message "Found $($schemaFiles.Count) schema files to copy" -Level "Info"
            foreach ($schemaFile in $schemaFiles) {
                Write-SafeLog -Message "Copying schema file: $($schemaFile.Name)" -Level "Info"
                try {
                    # Copy file to temp location on server first
                    $tempPath = "/tmp/$($schemaFile.Name)"
                    $scpCmd = "scp -i `"$($Config.ssh.key_path)`" -P $($Config.ssh.port) `"$($schemaFile.FullName)`" $($Config.ssh.username)@$($Config.ssh.host):$tempPath"
                    $scpResult = Invoke-Expression $scpCmd 2>&1

                    if ($LASTEXITCODE -eq 0) {
                        # Move to final location with proper permissions
                        $moveCmd = "sudo mv $tempPath $installDir/schemas/ && sudo chown ${serviceUser}:${serviceGroup} $installDir/schemas/$($schemaFile.Name)"
                        Invoke-RemoteCommand -Command $moveCmd
                        Write-SafeLog -Message "Successfully copied $($schemaFile.Name)" -Level "Info"
                    } else {
                        Write-SafeLog -Message "Failed to copy $($schemaFile.Name): $scpResult" -Level "Warning"
                    }
                } catch {
                    Write-SafeLog -Message "Error copying $($schemaFile.Name): $($_.Exception.Message)" -Level "Warning"
                }
            }
        } else {
            Write-SafeLog -Message "No .sql files found in schema directory" -Level "Warning"
        }
    } else {
        Write-SafeLog -Message "Local schema directory not found: $localSchemaDir" -Level "Warning"
    }

    # Check if the service is already installed
    $serviceName = $Config.service.name
    if ([string]::IsNullOrEmpty($serviceName)) {
        Write-SafeLog -Message "Service name not found in configuration." -Level "Error"
        Wait-ForUser
        return
    }
    Write-SafeLog -Message "Service name: $serviceName" -Level "Info"

    $checkServiceCmd = "systemctl list-unit-files | grep $serviceName.service | wc -l"
    $serviceExists = Invoke-RemoteCommand -Command $checkServiceCmd -Silent
    if ([string]::IsNullOrEmpty($serviceExists)) { $serviceExists = "0" }

    if ($serviceExists -gt 0) {
        Write-SafeLog -Message "Service $serviceName is already installed." -Level "Warning"
        $reinstall = Read-Host "Do you want to reinstall the service? (y/n)"

        if ($reinstall -ne "y") {
            Write-SafeLog -Message "Installation cancelled." -Level "Warning"
            return
        }

        # Stop the service before reinstalling
        Write-SafeLog -Message "Stopping existing service..." -Level "Info"
        $stopCmd = "sudo systemctl stop $serviceName"
        Invoke-RemoteCommand -Command $stopCmd
    }

    # Create the service user and group if they don't exist
    # (Variables already defined earlier in the script)

    Write-SafeLog -Message "Service user: $serviceUser, Service group: $serviceGroup" -Level "Info"
    Write-SafeLog -Message "Checking if service user and group exist..." -Level "Info"

    # Check if the group exists
    $checkGroupCmd = "getent group $serviceGroup > /dev/null 2>&1 && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $groupExists = Invoke-RemoteCommand -Command $checkGroupCmd -Silent
    if ([string]::IsNullOrEmpty($groupExists)) { $groupExists = "NOT_EXISTS" }

    if ($groupExists -ne "EXISTS") {
        Write-SafeLog -Message "Creating service group: $serviceGroup" -Level "Info"
        $createGroupCmd = "sudo groupadd $serviceGroup"
        Invoke-RemoteCommand -Command $createGroupCmd
    } else {
        Write-SafeLog -Message "Service group $serviceGroup already exists." -Level "Info"
    }

    # Check if the user exists
    $checkUserCmd = "id -u $serviceUser > /dev/null 2>&1 && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $userExists = Invoke-RemoteCommand -Command $checkUserCmd -Silent
    if ([string]::IsNullOrEmpty($userExists)) { $userExists = "NOT_EXISTS" }

    if ($userExists -ne "EXISTS") {
        Write-SafeLog -Message "Creating service user: $serviceUser" -Level "Info"
        $createUserCmd = "sudo useradd -r -g $serviceGroup -d $installDir -s /bin/false $serviceUser"
        Invoke-RemoteCommand -Command $createUserCmd
    } else {
        Write-SafeLog -Message "Service user $serviceUser already exists." -Level "Info"
    }

    # Set permissions on the installation directory
    Write-SafeLog -Message "Setting permissions on installation directory..." -Level "Info"
    $setPermCmd = "sudo chown -R ${serviceUser}:${serviceGroup} $installDir"
    Invoke-RemoteCommand -Command $setPermCmd

    # Make the executable file executable
    $chmodCmd = "sudo chmod +x $installDir/bin/database-service"
    Invoke-RemoteCommand -Command $chmodCmd

    # Create the systemd service file using the template
    Write-SafeLog -Message "Creating systemd service file..." -Level "Info"

    $serviceDescription = $Config.service.description
    if ([string]::IsNullOrEmpty($serviceDescription)) {
        $serviceDescription = "Database Service"
        Write-SafeLog -Message "Service description not found in configuration. Using default: $serviceDescription" -Level "Warning"
    }
    Write-SafeLog -Message "Service description: $serviceDescription" -Level "Info"

    # Use the template from deployment_files and substitute variables
    $serviceFile = @"
[Unit]
Description=$serviceDescription
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=$serviceUser
Group=$serviceGroup
WorkingDirectory=$installDir
ExecStart=$installDir/bin/database-service --config $installDir/config/config.json
Restart=on-failure
RestartSec=5
Environment="LD_LIBRARY_PATH=$installDir/lib"

# Security enhancements
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
"@

    # Write the service file directly to the server
    Write-SafeLog -Message "Installing systemd service file..." -Level "Info"
    $createServiceCmd = @"
sudo tee /etc/systemd/system/$serviceName.service > /dev/null << 'EOF'
$serviceFile
EOF
"@
    Invoke-RemoteCommand -Command $createServiceCmd

    # Reload systemd and enable the service
    Write-SafeLog -Message "Enabling service..." -Level "Info"
    $enableServiceCmd = @"
sudo systemctl daemon-reload && \
sudo systemctl enable $serviceName
"@
    Invoke-RemoteCommand -Command $enableServiceCmd

    # Verify the service was installed
    $checkServiceCmd = "systemctl list-unit-files | grep $serviceName.service | wc -l"
    $serviceExists = Invoke-RemoteCommand -Command $checkServiceCmd -Silent
    if ([string]::IsNullOrEmpty($serviceExists)) { $serviceExists = "0" }

    if ($serviceExists -gt 0) {
        Write-SafeLog -Message "Service $serviceName installed successfully!" -Level "Success"
        Write-SafeLog -Message "Executable installed at: $installDir/bin/database-service" -Level "Info"
        Write-SafeLog -Message "Configuration file: $installDir/config/config.json" -Level "Info"
        Write-SafeLog -Message "Service file: /etc/systemd/system/$serviceName.service" -Level "Info"
    } else {
        Write-SafeLog -Message "Failed to install service $serviceName." -Level "Error"
    }

    Wait-ForUser

    } catch {
        Write-SafeLog -Message "Critical error in Install-Service: $($_.Exception.Message)" -Level "Error"
        Write-SafeLog -Message "Error details: $($_.Exception.ToString())" -Level "Error"
        Write-SafeLog -Message "Stack trace: $($_.ScriptStackTrace)" -Level "Error"
        Wait-ForUser
    }
}

# Export the main function
Export-ModuleMember -Function Install-Service
