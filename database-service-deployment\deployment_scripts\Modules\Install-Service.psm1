{{ ... }}
# Install Service Module

# Import common module
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
# Import Logger module
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force

function Install-Service {
    Clear-Host
    Write-Log -Message "========== Install Service ==========" -Level "Info" -Component "Service"
    Write-Log -Message "               Install Service" -Level "Info" -Component "Service"
    Write-Log -Message "========== Install Service ==========" -Level "Info" -Component "Service"

    if ($null -eq $Config -or $null -eq $Config.ssh) {
        Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error" -Component "Service"
        Wait-ForUser
        & "$PSScriptRoot\Set-Environment.ps1"
        return
    }

    # Check if the build directory exists (where the compiled executable should be)
    $buildDir = $Config.project.remote_build_dir
    if ([string]::IsNullOrEmpty($buildDir)) {
        $buildDir = "/home/<USER>/database-service-build"
        Write-Log -Message "Remote build directory not specified in config. Using default: $buildDir" -Level "Warning" -Component "Service"
    }

    # Check if the build directory structure exists
    Write-Log -Message "Checking for compiled executable at: $buildDir/build/bin/database-service" -Level "Info" -Component "Service"

    # First check if the build directory exists
    $checkBuildDirCmd = "ls -la $buildDir/build/ 2>/dev/null || echo 'BUILD_DIR_NOT_EXISTS'"
    Write-Log -Message "Executing command: $checkBuildDirCmd" -Level "Info" -Component "Service"
    $buildDirResult = Invoke-RemoteCommand -Command $checkBuildDirCmd
    if ([string]::IsNullOrEmpty($buildDirResult)) { $buildDirResult = "NO_OUTPUT" }
    Write-Log -Message "Build directory check result: $buildDirResult" -Level "Info" -Component "Service"

    # Check if the bin directory exists
    $checkBinDirCmd = "ls -la $buildDir/build/bin/ 2>/dev/null || echo 'BIN_DIR_NOT_EXISTS'"
    Write-Log -Message "Executing command: $checkBinDirCmd" -Level "Info" -Component "Service"
    $binDirResult = Invoke-RemoteCommand -Command $checkBinDirCmd
    if ([string]::IsNullOrEmpty($binDirResult)) { $binDirResult = "NO_OUTPUT" }
    Write-Log -Message "Bin directory check result: $binDirResult" -Level "Info" -Component "Service"

    # Check for the specific executable
    $checkBuildCmd = "test -f $buildDir/build/bin/database-service && echo 'EXISTS' || echo 'NOT_EXISTS'"
    Write-Log -Message "Executing command: $checkBuildCmd" -Level "Info" -Component "Service"
    $buildExists = Invoke-RemoteCommand -Command $checkBuildCmd
    if ([string]::IsNullOrEmpty($buildExists)) { $buildExists = "NO_OUTPUT" }
    Write-Log -Message "Executable check result: '$buildExists'" -Level "Info" -Component "Service"

    if ($buildExists -ne "EXISTS") {
        Write-Log -Message "Compiled executable not found at: $buildDir/build/bin/database-service" -Level "Error" -Component "Service"
        Write-Log -Message "Please build the project first (Menu Option 9)." -Level "Warning" -Component "Service"
        Wait-ForUser
        return
    }

    # Create the installation directory
    $installDir = $Config.project.remote_install_dir
    if ([string]::IsNullOrEmpty($installDir)) {
        $installDir = "/opt/database-service"
        Write-Log -Message "Remote install directory not specified in config. Using default: $installDir" -Level "Warning" -Component "Service"
    }
    Write-Log -Message "Creating installation directory: $installDir" -Level "Info" -Component "Service"
    $createDirCmd = "sudo mkdir -p $installDir/bin $installDir/config $installDir/logs"
    Invoke-RemoteCommand -Command $createDirCmd

    # Copy the compiled executable from build directory to installation directory
    Write-Log -Message "Copying executable from build directory to installation directory..." -Level "Info" -Component "Service"
    $copyExeCmd = "sudo cp $buildDir/build/bin/database-service $installDir/bin/"
    Invoke-RemoteCommand -Command $copyExeCmd

    # Copy configuration files
    Write-Log -Message "Copying configuration files..." -Level "Info" -Component "Service"
    $copyConfigCmd = "sudo cp $buildDir/source/config/config-prod.json $installDir/config/config.json"
    Invoke-RemoteCommand -Command $copyConfigCmd

    # Check if the service is already installed
    $serviceName = $Config.service.name
    $checkServiceCmd = "systemctl list-unit-files | grep $serviceName.service | wc -l"
    $serviceExists = Invoke-RemoteCommand -Command $checkServiceCmd -Silent

    if ($serviceExists -gt 0) {
        Write-Log -Message "Service $serviceName is already installed." -Level "Warning" -Component "Service"
        $reinstall = Read-Host "Do you want to reinstall the service? (y/n)"

        if ($reinstall -ne "y") {
            Write-Log -Message "Installation cancelled." -Level "Warning" -Component "Service"
            return
        }

        # Stop the service before reinstalling
        Write-Log -Message "Stopping existing service..." -Level "Info" -Component "Service"
        $stopCmd = "sudo systemctl stop $serviceName"
        Invoke-RemoteCommand -Command $stopCmd
    }

    # Create the service user and group if they don't exist
    $serviceUser = $Config.service.user
    $serviceGroup = $Config.service.group

    Write-Log -Message "Checking if service user and group exist..." -Level "Info" -Component "Service"

    # Check if the group exists
    $checkGroupCmd = "getent group $serviceGroup > /dev/null 2>&1 && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $groupExists = Invoke-RemoteCommand -Command $checkGroupCmd -Silent

    if ($groupExists -ne "EXISTS") {
        Write-Log -Message "Creating service group: $serviceGroup" -Level "Info" -Component "Service"
        $createGroupCmd = "sudo groupadd $serviceGroup"
        Invoke-RemoteCommand -Command $createGroupCmd
    } else {
        Write-Log -Message "Service group $serviceGroup already exists." -Level "Info" -Component "Service"
    }

    # Check if the user exists
    $checkUserCmd = "id -u $serviceUser > /dev/null 2>&1 && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $userExists = Invoke-RemoteCommand -Command $checkUserCmd -Silent

    if ($userExists -ne "EXISTS") {
        Write-Log -Message "Creating service user: $serviceUser" -Level "Info" -Component "Service"
        $createUserCmd = "sudo useradd -r -g $serviceGroup -d $installDir -s /bin/false $serviceUser"
        Invoke-RemoteCommand -Command $createUserCmd
    } else {
        Write-Log -Message "Service user $serviceUser already exists." -Level "Info" -Component "Service"
    }

    # Set permissions on the installation directory
    Write-Log -Message "Setting permissions on installation directory..." -Level "Info" -Component "Service"
    $setPermCmd = "sudo chown -R ${serviceUser}:${serviceGroup} $installDir"
    Invoke-RemoteCommand -Command $setPermCmd

    # Make the executable file executable
    $chmodCmd = "sudo chmod +x $installDir/bin/database-service"
    Invoke-RemoteCommand -Command $chmodCmd

    # Create the systemd service file
    Write-Log -Message "Creating systemd service file..." -Level "Info" -Component "Service"

    $serviceDescription = $Config.service.description
    $serviceFile = @"
[Unit]
Description=$serviceDescription
After=network.target postgresql.service

[Service]
Type=simple
User=$serviceUser
Group=$serviceGroup
WorkingDirectory=$installDir
ExecStart=$installDir/bin/database-service --config $installDir/config/config.json
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
"@

    # Write the service file directly to the server
    Write-Log -Message "Installing systemd service file..." -Level "Info" -Component "Service"
    $createServiceCmd = @"
sudo tee /etc/systemd/system/$serviceName.service > /dev/null << 'EOF'
$serviceFile
EOF
"@
    Invoke-RemoteCommand -Command $createServiceCmd

    # Reload systemd and enable the service
    Write-Log -Message "Enabling service..." -Level "Info" -Component "Service"
    $enableServiceCmd = @"
sudo systemctl daemon-reload && \
sudo systemctl enable $serviceName
"@
    Invoke-RemoteCommand -Command $enableServiceCmd

    # Verify the service was installed
    $checkServiceCmd = "systemctl list-unit-files | grep $serviceName.service | wc -l"
    $serviceExists = Invoke-RemoteCommand -Command $checkServiceCmd -Silent

    if ($serviceExists -gt 0) {
        Write-Log -Message "Service $serviceName installed successfully!" -Level "Info" -Component "Service"
        Write-Log -Message "Executable installed at: $installDir/bin/database-service" -Level "Info" -Component "Service"
        Write-Log -Message "Configuration file: $installDir/config/config.json" -Level "Info" -Component "Service"
        Write-Log -Message "Service file: /etc/systemd/system/$serviceName.service" -Level "Info" -Component "Service"
    } else {
        Write-Log -Message "Failed to install service $serviceName." -Level "Error" -Component "Service"
    }

    Wait-ForUser
}

# Export the main function
Export-ModuleMember -Function Install-Service
