{{ ... }}
# Install Service Module

# Import common module
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
# Import Logger module
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force

function Install-Service {
    Clear-Host
    Write-Log -Message "========== Install Service ==========" -Level "Info" -Component "Service"
    Write-Log -Message "               Install Service" -Level "Info" -Component "Service"
    Write-Log -Message "========== Install Service ==========" -Level "Info" -Component "Service"

    if ($null -eq $Config -or $null -eq $Config.ssh) {
        Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error" -Component "Service"
        Wait-ForUser
        & "$PSScriptRoot\Set-Environment.ps1"
        return
    }

    # Get the build directory path
    $buildDir = $Config.project.remote_build_dir
    if ([string]::IsNullOrEmpty($buildDir)) {
        $buildDir = "/home/<USER>/database-service-build"
        Write-Log -Message "Remote build directory not specified in config. Using default: $buildDir" -Level "Warning" -Component "Service"
    }

    Write-Log -Message "Proceeding with installation from build directory: $buildDir" -Level "Info" -Component "Service"
    Write-Log -Message "Expected executable location: $buildDir/build/bin/database-service" -Level "Info" -Component "Service"

    # Skip existence checks and proceed with installation
    # (Build was successful, so executable should exist)

    # Create the installation directory
    $installDir = $Config.project.remote_install_dir
    if ([string]::IsNullOrEmpty($installDir)) {
        $installDir = "/opt/database-service"
        Write-Log -Message "Remote install directory not specified in config. Using default: $installDir" -Level "Warning" -Component "Service"
    }
    Write-Log -Message "Creating installation directory: $installDir" -Level "Info" -Component "Service"
    $createDirCmd = "sudo mkdir -p $installDir/bin $installDir/config $installDir/logs"
    Invoke-RemoteCommand -Command $createDirCmd

    # Copy the compiled executable from build directory to installation directory
    Write-Log -Message "Copying executable from build directory to installation directory..." -Level "Info" -Component "Service"
    $copyExeCmd = "sudo cp $buildDir/build/bin/database-service $installDir/bin/"
    Invoke-RemoteCommand -Command $copyExeCmd

    # Copy configuration files
    Write-Log -Message "Copying configuration files..." -Level "Info" -Component "Service"
    $copyConfigCmd = "sudo cp $buildDir/source/config/config-prod.json $installDir/config/config.json"
    Invoke-RemoteCommand -Command $copyConfigCmd

    # Check if the service is already installed
    $serviceName = $Config.service.name
    if ([string]::IsNullOrEmpty($serviceName)) {
        Write-Log -Message "Service name not found in configuration." -Level "Error" -Component "Service"
        Wait-ForUser
        return
    }
    Write-Log -Message "Service name: $serviceName" -Level "Info" -Component "Service"

    $checkServiceCmd = "systemctl list-unit-files | grep $serviceName.service | wc -l"
    $serviceExists = Invoke-RemoteCommand -Command $checkServiceCmd -Silent
    if ([string]::IsNullOrEmpty($serviceExists)) { $serviceExists = "0" }

    if ($serviceExists -gt 0) {
        Write-Log -Message "Service $serviceName is already installed." -Level "Warning" -Component "Service"
        $reinstall = Read-Host "Do you want to reinstall the service? (y/n)"

        if ($reinstall -ne "y") {
            Write-Log -Message "Installation cancelled." -Level "Warning" -Component "Service"
            return
        }

        # Stop the service before reinstalling
        Write-Log -Message "Stopping existing service..." -Level "Info" -Component "Service"
        $stopCmd = "sudo systemctl stop $serviceName"
        Invoke-RemoteCommand -Command $stopCmd
    }

    # Create the service user and group if they don't exist
    $serviceUser = $Config.service.user
    $serviceGroup = $Config.service.group

    if ([string]::IsNullOrEmpty($serviceUser)) {
        Write-Log -Message "Service user not found in configuration." -Level "Error" -Component "Service"
        Wait-ForUser
        return
    }
    if ([string]::IsNullOrEmpty($serviceGroup)) {
        Write-Log -Message "Service group not found in configuration." -Level "Error" -Component "Service"
        Wait-ForUser
        return
    }

    Write-Log -Message "Service user: $serviceUser, Service group: $serviceGroup" -Level "Info" -Component "Service"
    Write-Log -Message "Checking if service user and group exist..." -Level "Info" -Component "Service"

    # Check if the group exists
    $checkGroupCmd = "getent group $serviceGroup > /dev/null 2>&1 && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $groupExists = Invoke-RemoteCommand -Command $checkGroupCmd -Silent
    if ([string]::IsNullOrEmpty($groupExists)) { $groupExists = "NOT_EXISTS" }

    if ($groupExists -ne "EXISTS") {
        Write-Log -Message "Creating service group: $serviceGroup" -Level "Info" -Component "Service"
        $createGroupCmd = "sudo groupadd $serviceGroup"
        Invoke-RemoteCommand -Command $createGroupCmd
    } else {
        Write-Log -Message "Service group $serviceGroup already exists." -Level "Info" -Component "Service"
    }

    # Check if the user exists
    $checkUserCmd = "id -u $serviceUser > /dev/null 2>&1 && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $userExists = Invoke-RemoteCommand -Command $checkUserCmd -Silent
    if ([string]::IsNullOrEmpty($userExists)) { $userExists = "NOT_EXISTS" }

    if ($userExists -ne "EXISTS") {
        Write-Log -Message "Creating service user: $serviceUser" -Level "Info" -Component "Service"
        $createUserCmd = "sudo useradd -r -g $serviceGroup -d $installDir -s /bin/false $serviceUser"
        Invoke-RemoteCommand -Command $createUserCmd
    } else {
        Write-Log -Message "Service user $serviceUser already exists." -Level "Info" -Component "Service"
    }

    # Set permissions on the installation directory
    Write-Log -Message "Setting permissions on installation directory..." -Level "Info" -Component "Service"
    $setPermCmd = "sudo chown -R ${serviceUser}:${serviceGroup} $installDir"
    Invoke-RemoteCommand -Command $setPermCmd

    # Make the executable file executable
    $chmodCmd = "sudo chmod +x $installDir/bin/database-service"
    Invoke-RemoteCommand -Command $chmodCmd

    # Create the systemd service file
    Write-Log -Message "Creating systemd service file..." -Level "Info" -Component "Service"

    $serviceDescription = $Config.service.description
    if ([string]::IsNullOrEmpty($serviceDescription)) {
        $serviceDescription = "Database Service"
        Write-Log -Message "Service description not found in configuration. Using default: $serviceDescription" -Level "Warning" -Component "Service"
    }
    Write-Log -Message "Service description: $serviceDescription" -Level "Info" -Component "Service"
    $serviceFile = @"
[Unit]
Description=$serviceDescription
After=network.target postgresql.service

[Service]
Type=simple
User=$serviceUser
Group=$serviceGroup
WorkingDirectory=$installDir
ExecStart=$installDir/bin/database-service --config $installDir/config/config.json
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
"@

    # Write the service file directly to the server
    Write-Log -Message "Installing systemd service file..." -Level "Info" -Component "Service"
    $createServiceCmd = @"
sudo tee /etc/systemd/system/$serviceName.service > /dev/null << 'EOF'
$serviceFile
EOF
"@
    Invoke-RemoteCommand -Command $createServiceCmd

    # Reload systemd and enable the service
    Write-Log -Message "Enabling service..." -Level "Info" -Component "Service"
    $enableServiceCmd = @"
sudo systemctl daemon-reload && \
sudo systemctl enable $serviceName
"@
    Invoke-RemoteCommand -Command $enableServiceCmd

    # Verify the service was installed
    $checkServiceCmd = "systemctl list-unit-files | grep $serviceName.service | wc -l"
    $serviceExists = Invoke-RemoteCommand -Command $checkServiceCmd -Silent
    if ([string]::IsNullOrEmpty($serviceExists)) { $serviceExists = "0" }

    if ($serviceExists -gt 0) {
        Write-Log -Message "Service $serviceName installed successfully!" -Level "Info" -Component "Service"
        Write-Log -Message "Executable installed at: $installDir/bin/database-service" -Level "Info" -Component "Service"
        Write-Log -Message "Configuration file: $installDir/config/config.json" -Level "Info" -Component "Service"
        Write-Log -Message "Service file: /etc/systemd/system/$serviceName.service" -Level "Info" -Component "Service"
    } else {
        Write-Log -Message "Failed to install service $serviceName." -Level "Error" -Component "Service"
    }

    Wait-ForUser
}

# Export the main function
Export-ModuleMember -Function Install-Service
