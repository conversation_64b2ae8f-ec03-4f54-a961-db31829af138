{{ ... }}
# Install Service Module

# Import common module
try {
    Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force -ErrorAction Stop
} catch {
    Write-Host "Failed to import Common module: $_" -ForegroundColor Red
}

# Import Logger module
try {
    Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force -ErrorAction Stop
} catch {
    Write-Host "Failed to import Logger module: $_" -ForegroundColor Red
}

# Safe logging wrapper to prevent empty string errors
function Write-SafeLog {
    param(
        [string]$Message,
        [string]$Level = "Info",
        [string]$Component = "Service"
    )

    # Extra safety checks
    if ([string]::IsNullOrEmpty($Message)) {
        $Message = "[Empty message]"
    }

    # Check if Write-Log function is available
    if (Get-Command -Name Write-Log -ErrorAction SilentlyContinue) {
        try {
            Write-Log -Message $Message -Level $Level -Component $Component
        } catch {
            # Fallback to Write-Host if Write-Log fails
            $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            Write-Host "[$timestamp] [$Level] [$Component] $Message" -ForegroundColor $(if ($Level -eq "Error") { "Red" } elseif ($Level -eq "Warning") { "Yellow" } elseif ($Level -eq "Success") { "Green" } else { "White" })
        }
    } else {
        # Fallback if Write-Log is not available
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Write-Host "[$timestamp] [$Level] [$Component] $Message" -ForegroundColor $(if ($Level -eq "Error") { "Red" } elseif ($Level -eq "Warning") { "Yellow" } elseif ($Level -eq "Success") { "Green" } else { "White" })
    }
}

function Install-Service {
    try {
        Clear-Host

        Write-SafeLog -Message "========== Install Service ==========" -Level "Info"
        Write-SafeLog -Message "               Install Service" -Level "Info"
        Write-SafeLog -Message "========== Install Service ==========" -Level "Info"

        # Check if global Config is available
        if ($null -eq $global:Config) {
            Write-SafeLog -Message "Global configuration not found. Attempting to load..." -Level "Warning"
            try {
                $global:Config = Get-Configuration
            } catch {
                Write-SafeLog -Message "Failed to load configuration: $($_.Exception.Message)" -Level "Error"
                Wait-ForUser
                return
            }
        }

        if ($null -eq $global:Config -or $null -eq $global:Config.ssh) {
            Write-SafeLog -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error"
            Wait-ForUser
            return
        }

        # Use global config
        $Config = $global:Config

        # Validate required configuration sections
        if ($null -eq $Config.project) {
            Write-SafeLog -Message "Project configuration section missing" -Level "Error"
            Wait-ForUser
            return
        }

        if ($null -eq $Config.service) {
            Write-SafeLog -Message "Service configuration section missing" -Level "Error"
            Wait-ForUser
            return
        }
    }
    catch {
        Write-SafeLog -Message "Error in Install-Service initialization: $($_.Exception.Message)" -Level "Error"
        Write-SafeLog -Message "Stack trace: $($_.ScriptStackTrace)" -Level "Error"
        Wait-ForUser
        return
    }

    try {
        # Get the build directory path
    $buildDir = $Config.project.remote_build_dir
    if ([string]::IsNullOrEmpty($buildDir)) {
        $sshUsername = $Config.ssh.username
        if ([string]::IsNullOrEmpty($sshUsername)) {
            Write-SafeLog -Message "SSH username not found in configuration." -Level "Error"
            Wait-ForUser
            return
        }
        $buildDir = "/home/<USER>/database-service-build"
        Write-SafeLog -Message "Remote build directory not specified in config. Using default: $buildDir" -Level "Warning"
    }

    Write-SafeLog -Message "Proceeding with installation from build directory: $buildDir" -Level "Info"
    Write-SafeLog -Message "Expected executable location: $buildDir/build/bin/database-service" -Level "Info"

    # Skip existence checks and proceed with installation
    # (Build was successful, so executable should exist)

    # Create the installation directory
    $installDir = $Config.project.remote_install_dir
    if ([string]::IsNullOrEmpty($installDir)) {
        $installDir = "/opt/database-service"
        Write-SafeLog -Message "Remote install directory not specified in config. Using default: $installDir" -Level "Warning"
    }
    Write-SafeLog -Message "Creating installation directory: $installDir" -Level "Info"
    $createDirCmd = "sudo mkdir -p $installDir/bin $installDir/config $installDir/logs"
    Invoke-RemoteCommand -Command $createDirCmd

    # Copy the compiled executable from build directory to installation directory
    Write-SafeLog -Message "Copying executable from build directory to installation directory..." -Level "Info"
    $copyExeCmd = "sudo cp $buildDir/build/bin/database-service $installDir/bin/"
    Invoke-RemoteCommand -Command $copyExeCmd

    # Copy configuration files
    Write-SafeLog -Message "Copying configuration files..." -Level "Info"
    $copyConfigCmd = "sudo cp $buildDir/source/config/config.json $installDir/config/config.json"
    Invoke-RemoteCommand -Command $copyConfigCmd

    # Create required directories
    Write-SafeLog -Message "Creating required directories..." -Level "Info"
    $createDirsCmd = @"
sudo mkdir -p /var/log/database-service && \
sudo mkdir -p $installDir/schemas && \
sudo mkdir -p $installDir/migrations && \
sudo mkdir -p $installDir/examples && \
sudo chown -R database-service:database-service /var/log/database-service && \
sudo chmod 755 /var/log/database-service
"@
    Invoke-RemoteCommand -Command $createDirsCmd

    # Get service user and group early for use in file operations
    $serviceUser = $Config.service.user
    $serviceGroup = $Config.service.group

    if ([string]::IsNullOrEmpty($serviceUser)) {
        $serviceUser = "database-service"
        Write-SafeLog -Message "Service user not found in configuration. Using default: $serviceUser" -Level "Warning"
    }
    if ([string]::IsNullOrEmpty($serviceGroup)) {
        $serviceGroup = "database-service"
        Write-SafeLog -Message "Service group not found in configuration. Using default: $serviceGroup" -Level "Warning"
    }

    # Copy schema files from local deployment_files to server
    Write-SafeLog -Message "Copying schema files..." -Level "Info"
    # Go up two levels from Modules directory to get to the deployment root, then to deployment_files
    $deploymentRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
    $localSchemaDir = Join-Path $deploymentRoot "deployment_files\schemas"
    Write-SafeLog -Message "Looking for schema files in: $localSchemaDir" -Level "Info"

    if (Test-Path $localSchemaDir) {
        $schemaFiles = Get-ChildItem -Path $localSchemaDir -Filter "*.sql"
        if ($schemaFiles.Count -gt 0) {
            Write-SafeLog -Message "Found $($schemaFiles.Count) schema files to copy" -Level "Info"
            foreach ($schemaFile in $schemaFiles) {
                Write-SafeLog -Message "Copying schema file: $($schemaFile.Name)" -Level "Info"
                try {
                    # Copy file to temp location on server first
                    $tempPath = "/tmp/$($schemaFile.Name)"
                    $scpCmd = "scp -i `"$($Config.ssh.key_path)`" -P $($Config.ssh.port) `"$($schemaFile.FullName)`" $($Config.ssh.username)@$($Config.ssh.host):$tempPath"
                    $scpResult = Invoke-Expression $scpCmd 2>&1

                    if ($LASTEXITCODE -eq 0) {
                        # Move to final location with proper permissions
                        $moveCmd = "sudo mv $tempPath $installDir/schemas/ && sudo chown ${serviceUser}:${serviceGroup} $installDir/schemas/$($schemaFile.Name)"
                        Invoke-RemoteCommand -Command $moveCmd
                        Write-SafeLog -Message "Successfully copied $($schemaFile.Name)" -Level "Info"
                    } else {
                        Write-SafeLog -Message "Failed to copy $($schemaFile.Name): $scpResult" -Level "Warning"
                    }
                } catch {
                    Write-SafeLog -Message "Error copying $($schemaFile.Name): $($_.Exception.Message)" -Level "Warning"
                }
            }
        } else {
            Write-SafeLog -Message "No .sql files found in schema directory" -Level "Warning"
        }
    } else {
        Write-SafeLog -Message "Local schema directory not found: $localSchemaDir" -Level "Warning"
    }

    # Copy migration files from local deployment_files to server
    Write-SafeLog -Message "Copying migration files..." -Level "Info"
    $localMigrationDir = Join-Path $deploymentRoot "deployment_files\migrations"
    Write-SafeLog -Message "Looking for migration files in: $localMigrationDir" -Level "Info"

    if (Test-Path $localMigrationDir) {
        $migrationFiles = Get-ChildItem -Path $localMigrationDir -Filter "*.sql"
        if ($migrationFiles.Count -gt 0) {
            Write-SafeLog -Message "Found $($migrationFiles.Count) migration files to copy" -Level "Info"
            foreach ($migrationFile in $migrationFiles) {
                Write-SafeLog -Message "Copying migration file: $($migrationFile.Name)" -Level "Info"
                try {
                    # Copy file to temp location on server first
                    $tempPath = "/tmp/$($migrationFile.Name)"
                    $scpCmd = "scp -i `"$($Config.ssh.key_path)`" -P $($Config.ssh.port) `"$($migrationFile.FullName)`" $($Config.ssh.username)@$($Config.ssh.host):$tempPath"
                    $scpResult = Invoke-Expression $scpCmd 2>&1

                    if ($LASTEXITCODE -eq 0) {
                        # Move to final location with proper permissions
                        $moveCmd = "sudo mv $tempPath $installDir/migrations/ && sudo chown ${serviceUser}:${serviceGroup} $installDir/migrations/$($migrationFile.Name)"
                        Invoke-RemoteCommand -Command $moveCmd
                        Write-SafeLog -Message "Successfully copied $($migrationFile.Name)" -Level "Info"
                    } else {
                        Write-SafeLog -Message "Failed to copy $($migrationFile.Name): $scpResult" -Level "Warning"
                    }
                } catch {
                    Write-SafeLog -Message "Error copying $($migrationFile.Name): $($_.Exception.Message)" -Level "Warning"
                }
            }
        } else {
            Write-SafeLog -Message "No .sql files found in migration directory" -Level "Info"
        }
    } else {
        Write-SafeLog -Message "Local migration directory not found: $localMigrationDir" -Level "Info"
    }

    # Copy example files from local deployment_files to server
    Write-SafeLog -Message "Copying example files..." -Level "Info"
    $localExampleDir = Join-Path $deploymentRoot "deployment_files\examples"
    Write-SafeLog -Message "Looking for example files in: $localExampleDir" -Level "Info"

    if (Test-Path $localExampleDir) {
        $exampleFiles = Get-ChildItem -Path $localExampleDir -Filter "*.cpp"
        if ($exampleFiles.Count -gt 0) {
            Write-SafeLog -Message "Found $($exampleFiles.Count) example files to copy" -Level "Info"
            foreach ($exampleFile in $exampleFiles) {
                Write-SafeLog -Message "Copying example file: $($exampleFile.Name)" -Level "Info"
                try {
                    # Copy file to temp location on server first
                    $tempPath = "/tmp/$($exampleFile.Name)"
                    $scpCmd = "scp -i `"$($Config.ssh.key_path)`" -P $($Config.ssh.port) `"$($exampleFile.FullName)`" $($Config.ssh.username)@$($Config.ssh.host):$tempPath"
                    $scpResult = Invoke-Expression $scpCmd 2>&1

                    if ($LASTEXITCODE -eq 0) {
                        # Move to final location with proper permissions
                        $moveCmd = "sudo mv $tempPath $installDir/examples/ && sudo chown ${serviceUser}:${serviceGroup} $installDir/examples/$($exampleFile.Name)"
                        Invoke-RemoteCommand -Command $moveCmd
                        Write-SafeLog -Message "Successfully copied $($exampleFile.Name)" -Level "Info"
                    } else {
                        Write-SafeLog -Message "Failed to copy $($exampleFile.Name): $scpResult" -Level "Warning"
                    }
                } catch {
                    Write-SafeLog -Message "Error copying $($exampleFile.Name): $($_.Exception.Message)" -Level "Warning"
                }
            }
        } else {
            Write-SafeLog -Message "No .cpp files found in example directory" -Level "Info"
        }
    } else {
        Write-SafeLog -Message "Local example directory not found: $localExampleDir" -Level "Info"
    }

    # Check if the service is already installed
    $serviceName = $Config.service.name
    if ([string]::IsNullOrEmpty($serviceName)) {
        Write-SafeLog -Message "Service name not found in configuration." -Level "Error"
        Wait-ForUser
        return
    }
    Write-SafeLog -Message "Service name: $serviceName" -Level "Info"

    $checkServiceCmd = "systemctl list-unit-files | grep $serviceName.service | wc -l"
    $serviceExists = Invoke-RemoteCommand -Command $checkServiceCmd -Silent
    if ([string]::IsNullOrEmpty($serviceExists)) { $serviceExists = "0" }

    if ($serviceExists -gt 0) {
        Write-SafeLog -Message "Service $serviceName is already installed." -Level "Warning"
        $reinstall = Read-Host "Do you want to reinstall the service? (y/n)"

        if ($reinstall -ne "y") {
            Write-SafeLog -Message "Installation cancelled." -Level "Warning"
            return
        }

        # Stop the service before reinstalling
        Write-SafeLog -Message "Stopping existing service..." -Level "Info"
        $stopCmd = "sudo systemctl stop $serviceName"
        Invoke-RemoteCommand -Command $stopCmd
    }

    # Create the service user and group if they don't exist
    # (Variables already defined earlier in the script)

    Write-SafeLog -Message "Service user: $serviceUser, Service group: $serviceGroup" -Level "Info"
    Write-SafeLog -Message "Checking if service user and group exist..." -Level "Info"

    # Check if the group exists
    $checkGroupCmd = "getent group $serviceGroup > /dev/null 2>&1 && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $groupExists = Invoke-RemoteCommand -Command $checkGroupCmd -Silent
    if ([string]::IsNullOrEmpty($groupExists)) { $groupExists = "NOT_EXISTS" }

    if ($groupExists -ne "EXISTS") {
        Write-SafeLog -Message "Creating service group: $serviceGroup" -Level "Info"
        $createGroupCmd = "sudo groupadd $serviceGroup"
        Invoke-RemoteCommand -Command $createGroupCmd
    } else {
        Write-SafeLog -Message "Service group $serviceGroup already exists." -Level "Info"
    }

    # Check if the user exists
    $checkUserCmd = "id -u $serviceUser > /dev/null 2>&1 && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $userExists = Invoke-RemoteCommand -Command $checkUserCmd -Silent
    if ([string]::IsNullOrEmpty($userExists)) { $userExists = "NOT_EXISTS" }

    if ($userExists -ne "EXISTS") {
        Write-SafeLog -Message "Creating service user: $serviceUser" -Level "Info"
        $createUserCmd = "sudo useradd -r -g $serviceGroup -d $installDir -s /bin/false $serviceUser"
        Invoke-RemoteCommand -Command $createUserCmd
    } else {
        Write-SafeLog -Message "Service user $serviceUser already exists." -Level "Info"
    }

    # Set permissions on the installation directory
    Write-SafeLog -Message "Setting permissions on installation directory..." -Level "Info"
    $setPermCmd = "sudo chown -R ${serviceUser}:${serviceGroup} $installDir"
    Invoke-RemoteCommand -Command $setPermCmd

    # Make the executable file executable
    $chmodCmd = "sudo chmod +x $installDir/bin/database-service"
    Invoke-RemoteCommand -Command $chmodCmd

    # Create the systemd service file using the template
    Write-SafeLog -Message "Creating systemd service file..." -Level "Info"

    $serviceDescription = $Config.service.description
    if ([string]::IsNullOrEmpty($serviceDescription)) {
        $serviceDescription = "Database Service"
        Write-SafeLog -Message "Service description not found in configuration. Using default: $serviceDescription" -Level "Warning"
    }
    Write-SafeLog -Message "Service description: $serviceDescription" -Level "Info"

    # Load and process the systemd service template from deployment_files
    $systemdTemplateDir = Join-Path $deploymentRoot "deployment_files\systemd"
    $templateFile = Join-Path $systemdTemplateDir "database-service.service.in"

    if (Test-Path $templateFile) {
        Write-SafeLog -Message "Using systemd service template: $templateFile" -Level "Info"
        $serviceTemplate = Get-Content $templateFile -Raw

        # Substitute all template variables with actual values
        $serviceFile = $serviceTemplate -replace '@CMAKE_INSTALL_PREFIX@', $installDir
        $serviceFile = $serviceFile -replace '@SERVICE_USER@', $serviceUser
        $serviceFile = $serviceFile -replace '@SERVICE_GROUP@', $serviceGroup
        $serviceFile = $serviceFile -replace '@SERVICE_DESCRIPTION@', $serviceDescription

        Write-SafeLog -Message "Systemd service template processed successfully" -Level "Info"
        Write-SafeLog -Message "Template variables substituted:" -Level "Info"
        Write-SafeLog -Message "  CMAKE_INSTALL_PREFIX: $installDir" -Level "Info"
        Write-SafeLog -Message "  SERVICE_USER: $serviceUser" -Level "Info"
        Write-SafeLog -Message "  SERVICE_GROUP: $serviceGroup" -Level "Info"
        Write-SafeLog -Message "  SERVICE_DESCRIPTION: $serviceDescription" -Level "Info"

        # Validate the processed template
        Write-SafeLog -Message "Validating processed systemd service file..." -Level "Info"
        if ($serviceFile -match 'ExecStart=.*database-service.*--config.*config\.json') {
            Write-SafeLog -Message "✓ ExecStart command is valid" -Level "Success"
        } else {
            Write-SafeLog -Message "✗ ExecStart command may be invalid" -Level "Warning"
        }

        if ($serviceFile -match "User=$serviceUser" -and $serviceFile -match "Group=$serviceGroup") {
            Write-SafeLog -Message "✓ User and Group configuration is valid" -Level "Success"
        } else {
            Write-SafeLog -Message "✗ User/Group configuration may be invalid" -Level "Warning"
        }

        if ($serviceFile -match 'WorkingDirectory=' -and $serviceFile -match 'Environment=') {
            Write-SafeLog -Message "✓ Working directory and environment are configured" -Level "Success"
        } else {
            Write-SafeLog -Message "✗ Working directory or environment may be missing" -Level "Warning"
        }
    } else {
        Write-SafeLog -Message "Systemd template not found, using fallback inline template" -Level "Warning"
        # Fallback to inline template
        $serviceFile = @"
[Unit]
Description=$serviceDescription
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=$serviceUser
Group=$serviceGroup
WorkingDirectory=$installDir
ExecStart=$installDir/bin/database-service --config $installDir/config/config.json
Restart=on-failure
RestartSec=5
Environment="LD_LIBRARY_PATH=$installDir/lib"

# Security enhancements
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
"@
    }

    # Write the service file directly to the server
    Write-SafeLog -Message "Installing systemd service file..." -Level "Info"
    $createServiceCmd = @"
sudo tee /etc/systemd/system/$serviceName.service > /dev/null << 'EOF'
$serviceFile
EOF
"@
    Invoke-RemoteCommand -Command $createServiceCmd

    # Reload systemd and enable the service
    Write-SafeLog -Message "Enabling service..." -Level "Info"

    # First reload systemd
    $reloadCmd = "sudo systemctl daemon-reload"
    $reloadResult = Invoke-RemoteCommand -Command $reloadCmd
    Write-SafeLog -Message "Systemd daemon reloaded" -Level "Info"

    # Then enable the service
    $enableCmd = "sudo systemctl enable $serviceName"
    $enableResult = Invoke-RemoteCommand -Command $enableCmd

    if ($enableResult -match "Created symlink" -or $enableResult -match "enabled" -or [string]::IsNullOrEmpty($enableResult)) {
        Write-SafeLog -Message "Service enabled successfully" -Level "Success"
    } else {
        Write-SafeLog -Message "Service enable result: $enableResult" -Level "Warning"
    }

    # Verify the service was installed
    Write-SafeLog -Message "Verifying service installation..." -Level "Info"

    # Check if service file exists
    $checkFileCmd = "test -f /etc/systemd/system/$serviceName.service && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $serviceFileExists = Invoke-RemoteCommand -Command $checkFileCmd -Silent
    Write-SafeLog -Message "Service file check result: '$serviceFileExists'" -Level "Info"

    # Check if service is in systemctl list
    $checkServiceCmd = "systemctl list-unit-files | grep '$serviceName.service' | wc -l"
    $serviceExists = Invoke-RemoteCommand -Command $checkServiceCmd -Silent
    Write-SafeLog -Message "Service in unit files count: '$serviceExists'" -Level "Info"

    # Fallback check - just look for the service file directly
    $listServiceCmd = "ls -la /etc/systemd/system/$serviceName.service"
    $serviceFileList = Invoke-RemoteCommand -Command $listServiceCmd -Silent
    Write-SafeLog -Message "Direct file listing: '$serviceFileList'" -Level "Info"

    # Determine if installation was successful
    $installSuccess = $false

    # Check multiple conditions for success
    if ($serviceFileExists -eq "EXISTS") {
        $installSuccess = $true
        Write-SafeLog -Message "✓ Service file exists" -Level "Success"
    } elseif (-not [string]::IsNullOrEmpty($serviceFileList) -and $serviceFileList -notmatch "No such file") {
        $installSuccess = $true
        Write-SafeLog -Message "✓ Service file confirmed via direct listing" -Level "Success"
    }

    if ($installSuccess) {
        Write-SafeLog -Message "Service $serviceName installed successfully!" -Level "Success"
        Write-SafeLog -Message "✓ Executable installed at: $installDir/bin/database-service" -Level "Success"
        Write-SafeLog -Message "✓ Configuration file: $installDir/config/config.json" -Level "Success"
        Write-SafeLog -Message "✓ Service file: /etc/systemd/system/$serviceName.service" -Level "Success"
        Write-SafeLog -Message "✓ Schema files: Copied to $installDir/schemas/" -Level "Success"
        Write-SafeLog -Message "✓ Migration files: Copied to $installDir/migrations/" -Level "Success"
        Write-SafeLog -Message "✓ Example files: Copied to $installDir/examples/" -Level "Success"

        # Show service status
        $statusCmd = "sudo systemctl status $serviceName --no-pager -l"
        $statusResult = Invoke-RemoteCommand -Command $statusCmd
        Write-SafeLog -Message "Service status:" -Level "Info"
        if (-not [string]::IsNullOrEmpty($statusResult)) {
            Write-Host $statusResult -ForegroundColor Cyan
        } else {
            Write-SafeLog -Message "Service status command returned empty result" -Level "Warning"
        }

        # Show next steps
        Write-SafeLog -Message " " -Level "Info"
        Write-SafeLog -Message "🎯 Next Steps:" -Level "Info"
        Write-SafeLog -Message "1. Initialize database (Option 11)" -Level "Info"
        Write-SafeLog -Message "2. Start the service (Option 12)" -Level "Info"
        Write-SafeLog -Message "3. Check service status (Option 4)" -Level "Info"

    } else {
        Write-SafeLog -Message "Service installation verification failed" -Level "Error"
        Write-SafeLog -Message "Service exists in unit files: '$serviceExists'" -Level "Error"
        Write-SafeLog -Message "Service file exists check: '$serviceFileExists'" -Level "Error"
        Write-SafeLog -Message "Direct file listing: '$serviceFileList'" -Level "Error"

        # Show any systemctl errors
        if (-not [string]::IsNullOrEmpty($enableResult)) {
            Write-SafeLog -Message "Enable command output: $enableResult" -Level "Error"
        }

        # Try to show what went wrong
        $debugCmd = "sudo systemctl status $serviceName --no-pager -l"
        $debugResult = Invoke-RemoteCommand -Command $debugCmd
        Write-SafeLog -Message "Debug - Service status:" -Level "Error"
        if (-not [string]::IsNullOrEmpty($debugResult)) {
            Write-Host $debugResult -ForegroundColor Red
        } else {
            Write-SafeLog -Message "Service status command returned empty result" -Level "Error"
        }

        # Manual verification suggestion
        Write-SafeLog -Message " " -Level "Info"
        Write-SafeLog -Message "💡 Manual Verification:" -Level "Info"
        Write-SafeLog -Message "Run Option 16 (Custom Command) and execute:" -Level "Info"
        Write-SafeLog -Message "sudo systemctl status database-service" -Level "Info"
        Write-SafeLog -Message "ls -la /etc/systemd/system/database-service.service" -Level "Info"
    }

    Wait-ForUser

    } catch {
        Write-SafeLog -Message "Critical error in Install-Service: $($_.Exception.Message)" -Level "Error"
        Write-SafeLog -Message "Error details: $($_.Exception.ToString())" -Level "Error"
        Write-SafeLog -Message "Stack trace: $($_.ScriptStackTrace)" -Level "Error"
        Wait-ForUser
    }
}

# Export the main function
Export-ModuleMember -Function Install-Service
