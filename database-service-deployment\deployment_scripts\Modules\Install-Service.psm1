{{ ... }}
# Install Service Module

# Import common module
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
# Import Logger module
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force

# Safe logging wrapper to prevent empty string errors
function Write-SafeLog {
    param(
        [string]$Message,
        [string]$Level = "Info",
        [string]$Component = "Service"
    )
    if ([string]::IsNullOrEmpty($Message)) {
        $Message = "[Empty message]"
    }
    Write-Log -Message $Message -Level $Level -Component $Component
}

function Install-Service {
    try {
        Clear-Host
        Write-SafeLog -Message "========== Install Service ==========" -Level "Info"
        Write-SafeLog -Message "               Install Service" -Level "Info"
        Write-SafeLog -Message "========== Install Service ==========" -Level "Info"

        # Check if global Config is available
        if ($null -eq $global:Config) {
            Write-SafeLog -Message "Global configuration not found. Attempting to load..." -Level "Warning"
            $global:Config = Get-Configuration
        }

        if ($null -eq $global:Config -or $null -eq $global:Config.ssh) {
            Write-SafeLog -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error"
            Wait-ForUser
            & "$PSScriptRoot\Set-Environment.ps1"
            return
        }

        # Use global config
        $Config = $global:Config
    }
    catch {
        Write-SafeLog -Message "Error in Install-Service initialization: $($_.Exception.Message)" -Level "Error"
        Wait-ForUser
        return
    }

    # Get the build directory path
    $buildDir = $Config.project.remote_build_dir
    if ([string]::IsNullOrEmpty($buildDir)) {
        $sshUsername = $Config.ssh.username
        if ([string]::IsNullOrEmpty($sshUsername)) {
            Write-SafeLog -Message "SSH username not found in configuration." -Level "Error"
            Wait-ForUser
            return
        }
        $buildDir = "/home/<USER>/database-service-build"
        Write-SafeLog -Message "Remote build directory not specified in config. Using default: $buildDir" -Level "Warning"
    }

    Write-SafeLog -Message "Proceeding with installation from build directory: $buildDir" -Level "Info"
    Write-SafeLog -Message "Expected executable location: $buildDir/build/bin/database-service" -Level "Info"

    # Skip existence checks and proceed with installation
    # (Build was successful, so executable should exist)

    # Create the installation directory
    $installDir = $Config.project.remote_install_dir
    if ([string]::IsNullOrEmpty($installDir)) {
        $installDir = "/opt/database-service"
        Write-SafeLog -Message "Remote install directory not specified in config. Using default: $installDir" -Level "Warning"
    }
    Write-SafeLog -Message "Creating installation directory: $installDir" -Level "Info"
    $createDirCmd = "sudo mkdir -p $installDir/bin $installDir/config $installDir/logs"
    Invoke-RemoteCommand -Command $createDirCmd

    # Copy the compiled executable from build directory to installation directory
    Write-SafeLog -Message "Copying executable from build directory to installation directory..." -Level "Info"
    $copyExeCmd = "sudo cp $buildDir/build/bin/database-service $installDir/bin/"
    Invoke-RemoteCommand -Command $copyExeCmd

    # Copy configuration files
    Write-SafeLog -Message "Copying configuration files..." -Level "Info"
    $copyConfigCmd = "sudo cp $buildDir/source/config/config.json $installDir/config/config.json"
    Invoke-RemoteCommand -Command $copyConfigCmd

    # Create required directories
    Write-SafeLog -Message "Creating required directories..." -Level "Info"
    $createDirsCmd = @"
sudo mkdir -p /var/log/database-service && \
sudo mkdir -p $installDir/schemas && \
sudo chown -R database-service:database-service /var/log/database-service && \
sudo chmod 755 /var/log/database-service
"@
    Invoke-RemoteCommand -Command $createDirsCmd

    # Copy schema files from deployment files (these should have been copied during build)
    Write-SafeLog -Message "Copying schema files..." -Level "Info"
    $copySchemaCmd = @"
if [ -d $buildDir/deployment_files/schemas ]; then
    sudo cp $buildDir/deployment_files/schemas/*.sql $installDir/schemas/ 2>/dev/null || true
else
    echo "No schema files found in build directory"
fi
"@
    Invoke-RemoteCommand -Command $copySchemaCmd

    # Check if the service is already installed
    $serviceName = $Config.service.name
    if ([string]::IsNullOrEmpty($serviceName)) {
        Write-SafeLog -Message "Service name not found in configuration." -Level "Error"
        Wait-ForUser
        return
    }
    Write-SafeLog -Message "Service name: $serviceName" -Level "Info"

    $checkServiceCmd = "systemctl list-unit-files | grep $serviceName.service | wc -l"
    $serviceExists = Invoke-RemoteCommand -Command $checkServiceCmd -Silent
    if ([string]::IsNullOrEmpty($serviceExists)) { $serviceExists = "0" }

    if ($serviceExists -gt 0) {
        Write-SafeLog -Message "Service $serviceName is already installed." -Level "Warning"
        $reinstall = Read-Host "Do you want to reinstall the service? (y/n)"

        if ($reinstall -ne "y") {
            Write-SafeLog -Message "Installation cancelled." -Level "Warning"
            return
        }

        # Stop the service before reinstalling
        Write-SafeLog -Message "Stopping existing service..." -Level "Info"
        $stopCmd = "sudo systemctl stop $serviceName"
        Invoke-RemoteCommand -Command $stopCmd
    }

    # Create the service user and group if they don't exist
    $serviceUser = $Config.service.user
    $serviceGroup = $Config.service.group

    if ([string]::IsNullOrEmpty($serviceUser)) {
        Write-SafeLog -Message "Service user not found in configuration." -Level "Error"
        Wait-ForUser
        return
    }
    if ([string]::IsNullOrEmpty($serviceGroup)) {
        Write-SafeLog -Message "Service group not found in configuration." -Level "Error"
        Wait-ForUser
        return
    }

    Write-SafeLog -Message "Service user: $serviceUser, Service group: $serviceGroup" -Level "Info"
    Write-SafeLog -Message "Checking if service user and group exist..." -Level "Info"

    # Check if the group exists
    $checkGroupCmd = "getent group $serviceGroup > /dev/null 2>&1 && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $groupExists = Invoke-RemoteCommand -Command $checkGroupCmd -Silent
    if ([string]::IsNullOrEmpty($groupExists)) { $groupExists = "NOT_EXISTS" }

    if ($groupExists -ne "EXISTS") {
        Write-Log -Message "Creating service group: $serviceGroup" -Level "Info" -Component "Service"
        $createGroupCmd = "sudo groupadd $serviceGroup"
        Invoke-RemoteCommand -Command $createGroupCmd
    } else {
        Write-Log -Message "Service group $serviceGroup already exists." -Level "Info" -Component "Service"
    }

    # Check if the user exists
    $checkUserCmd = "id -u $serviceUser > /dev/null 2>&1 && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $userExists = Invoke-RemoteCommand -Command $checkUserCmd -Silent
    if ([string]::IsNullOrEmpty($userExists)) { $userExists = "NOT_EXISTS" }

    if ($userExists -ne "EXISTS") {
        Write-Log -Message "Creating service user: $serviceUser" -Level "Info" -Component "Service"
        $createUserCmd = "sudo useradd -r -g $serviceGroup -d $installDir -s /bin/false $serviceUser"
        Invoke-RemoteCommand -Command $createUserCmd
    } else {
        Write-Log -Message "Service user $serviceUser already exists." -Level "Info" -Component "Service"
    }

    # Set permissions on the installation directory
    Write-Log -Message "Setting permissions on installation directory..." -Level "Info" -Component "Service"
    $setPermCmd = "sudo chown -R ${serviceUser}:${serviceGroup} $installDir"
    Invoke-RemoteCommand -Command $setPermCmd

    # Make the executable file executable
    $chmodCmd = "sudo chmod +x $installDir/bin/database-service"
    Invoke-RemoteCommand -Command $chmodCmd

    # Create the systemd service file
    Write-Log -Message "Creating systemd service file..." -Level "Info" -Component "Service"

    $serviceDescription = $Config.service.description
    if ([string]::IsNullOrEmpty($serviceDescription)) {
        $serviceDescription = "Database Service"
        Write-Log -Message "Service description not found in configuration. Using default: $serviceDescription" -Level "Warning" -Component "Service"
    }
    Write-Log -Message "Service description: $serviceDescription" -Level "Info" -Component "Service"
    $serviceFile = @"
[Unit]
Description=$serviceDescription
After=network.target postgresql.service

[Service]
Type=simple
User=$serviceUser
Group=$serviceGroup
WorkingDirectory=$installDir
ExecStart=$installDir/bin/database-service --config $installDir/config/config.json
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
"@

    # Write the service file directly to the server
    Write-Log -Message "Installing systemd service file..." -Level "Info" -Component "Service"
    $createServiceCmd = @"
sudo tee /etc/systemd/system/$serviceName.service > /dev/null << 'EOF'
$serviceFile
EOF
"@
    Invoke-RemoteCommand -Command $createServiceCmd

    # Reload systemd and enable the service
    Write-Log -Message "Enabling service..." -Level "Info" -Component "Service"
    $enableServiceCmd = @"
sudo systemctl daemon-reload && \
sudo systemctl enable $serviceName
"@
    Invoke-RemoteCommand -Command $enableServiceCmd

    # Verify the service was installed
    $checkServiceCmd = "systemctl list-unit-files | grep $serviceName.service | wc -l"
    $serviceExists = Invoke-RemoteCommand -Command $checkServiceCmd -Silent
    if ([string]::IsNullOrEmpty($serviceExists)) { $serviceExists = "0" }

    if ($serviceExists -gt 0) {
        Write-Log -Message "Service $serviceName installed successfully!" -Level "Info" -Component "Service"
        Write-Log -Message "Executable installed at: $installDir/bin/database-service" -Level "Info" -Component "Service"
        Write-Log -Message "Configuration file: $installDir/config/config.json" -Level "Info" -Component "Service"
        Write-Log -Message "Service file: /etc/systemd/system/$serviceName.service" -Level "Info" -Component "Service"
    } else {
        Write-Log -Message "Failed to install service $serviceName." -Level "Error" -Component "Service"
    }

    Wait-ForUser
}

# Export the main function
Export-ModuleMember -Function Install-Service
