function e(e,t){for(var n=0;n<t.length;n++){const a=t[n];if("string"!=typeof a&&!Array.isArray(a))for(const t in a)if("default"!==t&&!(t in e)){const n=Object.getOwnPropertyDescriptor(a,t);n&&Object.defineProperty(e,t,n.get?n:{enumerable:!0,get:()=>a[t]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}function t(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var n={exports:{}},a={},r={exports:{}},l={},i=Symbol.for("react.element"),s=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),d=Symbol.for("react.provider"),f=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),g=Symbol.iterator;var y={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},x=Object.assign,b={};function j(e,t,n){this.props=e,this.context=t,this.refs=b,this.updater=n||y}function w(){}function S(e,t,n){this.props=e,this.context=t,this.refs=b,this.updater=n||y}j.prototype.isReactComponent={},j.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},j.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},w.prototype=j.prototype;var k=S.prototype=new w;k.constructor=S,x(k,j.prototype),k.isPureReactComponent=!0;var N=Array.isArray,_=Object.prototype.hasOwnProperty,C={current:null},E={key:!0,ref:!0,__self:!0,__source:!0};function P(e,t,n){var a,r={},l=null,s=null;if(null!=t)for(a in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(l=""+t.key),t)_.call(t,a)&&!E.hasOwnProperty(a)&&(r[a]=t[a]);var o=arguments.length-2;if(1===o)r.children=n;else if(1<o){for(var c=Array(o),u=0;u<o;u++)c[u]=arguments[u+2];r.children=c}if(e&&e.defaultProps)for(a in o=e.defaultProps)void 0===r[a]&&(r[a]=o[a]);return{$$typeof:i,type:e,key:l,ref:s,props:r,_owner:C.current}}function T(e){return"object"==typeof e&&null!==e&&e.$$typeof===i}var L=/\/+/g;function R(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function I(e,t,n,a,r){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var o=!1;if(null===e)o=!0;else switch(l){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case i:case s:o=!0}}if(o)return r=r(o=e),e=""===a?"."+R(o,0):a,N(r)?(n="",null!=e&&(n=e.replace(L,"$&/")+"/"),I(r,t,n,"",function(e){return e})):null!=r&&(T(r)&&(r=function(e,t){return{$$typeof:i,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(r,n+(!r.key||o&&o.key===r.key?"":(""+r.key).replace(L,"$&/")+"/")+e)),t.push(r)),1;if(o=0,a=""===a?".":a+":",N(e))for(var c=0;c<e.length;c++){var u=a+R(l=e[c],c);o+=I(l,t,n,u,r)}else if(u=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=g&&e[g]||e["@@iterator"])?e:null}(e),"function"==typeof u)for(e=u.call(e),c=0;!(l=e.next()).done;)o+=I(l=l.value,t,n,u=a+R(l,c++),r);else if("object"===l)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function D(e,t,n){if(null==e)return e;var a=[],r=0;return I(e,a,"","",function(e){return t.call(n,e,r++)}),a}function F(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var z={current:null},A={transition:null},O={ReactCurrentDispatcher:z,ReactCurrentBatchConfig:A,ReactCurrentOwner:C};function M(){throw Error("act(...) is not supported in production builds of React.")}l.Children={map:D,forEach:function(e,t,n){D(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return D(e,function(){t++}),t},toArray:function(e){return D(e,function(e){return e})||[]},only:function(e){if(!T(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},l.Component=j,l.Fragment=o,l.Profiler=u,l.PureComponent=S,l.StrictMode=c,l.Suspense=p,l.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=O,l.act=M,l.cloneElement=function(e,t,n){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=x({},e.props),r=e.key,l=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(l=t.ref,s=C.current),void 0!==t.key&&(r=""+t.key),e.type&&e.type.defaultProps)var o=e.type.defaultProps;for(c in t)_.call(t,c)&&!E.hasOwnProperty(c)&&(a[c]=void 0===t[c]&&void 0!==o?o[c]:t[c])}var c=arguments.length-2;if(1===c)a.children=n;else if(1<c){o=Array(c);for(var u=0;u<c;u++)o[u]=arguments[u+2];a.children=o}return{$$typeof:i,type:e.type,key:r,ref:l,props:a,_owner:s}},l.createContext=function(e){return(e={$$typeof:f,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:d,_context:e},e.Consumer=e},l.createElement=P,l.createFactory=function(e){var t=P.bind(null,e);return t.type=e,t},l.createRef=function(){return{current:null}},l.forwardRef=function(e){return{$$typeof:h,render:e}},l.isValidElement=T,l.lazy=function(e){return{$$typeof:v,_payload:{_status:-1,_result:e},_init:F}},l.memo=function(e,t){return{$$typeof:m,type:e,compare:void 0===t?null:t}},l.startTransition=function(e){var t=A.transition;A.transition={};try{e()}finally{A.transition=t}},l.unstable_act=M,l.useCallback=function(e,t){return z.current.useCallback(e,t)},l.useContext=function(e){return z.current.useContext(e)},l.useDebugValue=function(){},l.useDeferredValue=function(e){return z.current.useDeferredValue(e)},l.useEffect=function(e,t){return z.current.useEffect(e,t)},l.useId=function(){return z.current.useId()},l.useImperativeHandle=function(e,t,n){return z.current.useImperativeHandle(e,t,n)},l.useInsertionEffect=function(e,t){return z.current.useInsertionEffect(e,t)},l.useLayoutEffect=function(e,t){return z.current.useLayoutEffect(e,t)},l.useMemo=function(e,t){return z.current.useMemo(e,t)},l.useReducer=function(e,t,n){return z.current.useReducer(e,t,n)},l.useRef=function(e){return z.current.useRef(e)},l.useState=function(e){return z.current.useState(e)},l.useSyncExternalStore=function(e,t,n){return z.current.useSyncExternalStore(e,t,n)},l.useTransition=function(){return z.current.useTransition()},l.version="18.3.1",r.exports=l;var U=r.exports;const $=t(U),q=e({__proto__:null,default:$},[U]);
/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var B=U,Q=Symbol.for("react.element"),V=Symbol.for("react.fragment"),W=Object.prototype.hasOwnProperty,H=B.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,K={key:!0,ref:!0,__self:!0,__source:!0};function Y(e,t,n){var a,r={},l=null,i=null;for(a in void 0!==n&&(l=""+n),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(i=t.ref),t)W.call(t,a)&&!K.hasOwnProperty(a)&&(r[a]=t[a]);if(e&&e.defaultProps)for(a in t=e.defaultProps)void 0===r[a]&&(r[a]=t[a]);return{$$typeof:Q,type:e,key:l,ref:i,props:r,_owner:H.current}}a.Fragment=V,a.jsx=Y,a.jsxs=Y,n.exports=a;var G=n.exports,J={},X={exports:{}},Z={},ee={exports:{}},te={};
/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
!function(e){function t(e,t){var n=e.length;e.push(t);e:for(;0<n;){var a=n-1>>>1,l=e[a];if(!(0<r(l,t)))break e;e[a]=t,e[n]=l,n=a}}function n(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var a=0,l=e.length,i=l>>>1;a<i;){var s=2*(a+1)-1,o=e[s],c=s+1,u=e[c];if(0>r(o,n))c<l&&0>r(u,o)?(e[a]=u,e[c]=n,a=c):(e[a]=o,e[s]=n,a=s);else{if(!(c<l&&0>r(u,n)))break e;e[a]=u,e[c]=n,a=c}}}return t}function r(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var l=performance;e.unstable_now=function(){return l.now()}}else{var i=Date,s=i.now();e.unstable_now=function(){return i.now()-s}}var o=[],c=[],u=1,d=null,f=3,h=!1,p=!1,m=!1,v="function"==typeof setTimeout?setTimeout:null,g="function"==typeof clearTimeout?clearTimeout:null,y="undefined"!=typeof setImmediate?setImmediate:null;function x(e){for(var r=n(c);null!==r;){if(null===r.callback)a(c);else{if(!(r.startTime<=e))break;a(c),r.sortIndex=r.expirationTime,t(o,r)}r=n(c)}}function b(e){if(m=!1,x(e),!p)if(null!==n(o))p=!0,R(j);else{var t=n(c);null!==t&&I(b,t.startTime-e)}}function j(t,r){p=!1,m&&(m=!1,g(N),N=-1),h=!0;var l=f;try{for(x(r),d=n(o);null!==d&&(!(d.expirationTime>r)||t&&!E());){var i=d.callback;if("function"==typeof i){d.callback=null,f=d.priorityLevel;var s=i(d.expirationTime<=r);r=e.unstable_now(),"function"==typeof s?d.callback=s:d===n(o)&&a(o),x(r)}else a(o);d=n(o)}if(null!==d)var u=!0;else{var v=n(c);null!==v&&I(b,v.startTime-r),u=!1}return u}finally{d=null,f=l,h=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var w,S=!1,k=null,N=-1,_=5,C=-1;function E(){return!(e.unstable_now()-C<_)}function P(){if(null!==k){var t=e.unstable_now();C=t;var n=!0;try{n=k(!0,t)}finally{n?w():(S=!1,k=null)}}else S=!1}if("function"==typeof y)w=function(){y(P)};else if("undefined"!=typeof MessageChannel){var T=new MessageChannel,L=T.port2;T.port1.onmessage=P,w=function(){L.postMessage(null)}}else w=function(){v(P,0)};function R(e){k=e,S||(S=!0,w())}function I(t,n){N=v(function(){t(e.unstable_now())},n)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_continueExecution=function(){p||h||(p=!0,R(j))},e.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<e?Math.floor(1e3/e):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(o)},e.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},e.unstable_scheduleCallback=function(a,r,l){var i=e.unstable_now();switch("object"==typeof l&&null!==l?l="number"==typeof(l=l.delay)&&0<l?i+l:i:l=i,a){case 1:var s=-1;break;case 2:s=250;break;case 5:s=**********;break;case 4:s=1e4;break;default:s=5e3}return a={id:u++,callback:r,priorityLevel:a,startTime:l,expirationTime:s=l+s,sortIndex:-1},l>i?(a.sortIndex=l,t(c,a),null===n(o)&&a===n(c)&&(m?(g(N),N=-1):m=!0,I(b,l-i))):(a.sortIndex=s,t(o,a),p||h||(p=!0,R(j))),a},e.unstable_shouldYield=E,e.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}}(te),ee.exports=te;var ne=ee.exports,ae=U,re=ne;
/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function le(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ie=new Set,se={};function oe(e,t){ce(e,t),ce(e+"Capture",t)}function ce(e,t){for(se[e]=t,e=0;e<t.length;e++)ie.add(t[e])}var ue=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),de=Object.prototype.hasOwnProperty,fe=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,he={},pe={};function me(e,t,n,a,r,l,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=a,this.attributeNamespace=r,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=i}var ve={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ve[e]=new me(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ve[t]=new me(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){ve[e]=new me(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ve[e]=new me(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ve[e]=new me(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){ve[e]=new me(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){ve[e]=new me(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){ve[e]=new me(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){ve[e]=new me(e,5,!1,e.toLowerCase(),null,!1,!1)});var ge=/[\-:]([a-z])/g;function ye(e){return e[1].toUpperCase()}function xe(e,t,n,a){var r=ve.hasOwnProperty(t)?ve[t]:null;(null!==r?0!==r.type:a||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,a){if(null==t||function(e,t,n,a){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!a&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,a))return!0;if(a)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,r,a)&&(n=null),a||null===r?function(e){return!!de.call(pe,e)||!de.call(he,e)&&(fe.test(e)?pe[e]=!0:(he[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):r.mustUseProperty?e[r.propertyName]=null===n?3!==r.type&&"":n:(t=r.attributeName,a=r.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(r=r.type)||4===r&&!0===n?"":""+n,a?e.setAttributeNS(a,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ge,ye);ve[t]=new me(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ge,ye);ve[t]=new me(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ge,ye);ve[t]=new me(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){ve[e]=new me(e,1,!1,e.toLowerCase(),null,!1,!1)}),ve.xlinkHref=new me("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){ve[e]=new me(e,1,!1,e.toLowerCase(),null,!0,!0)});var be=ae.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,je=Symbol.for("react.element"),we=Symbol.for("react.portal"),Se=Symbol.for("react.fragment"),ke=Symbol.for("react.strict_mode"),Ne=Symbol.for("react.profiler"),_e=Symbol.for("react.provider"),Ce=Symbol.for("react.context"),Ee=Symbol.for("react.forward_ref"),Pe=Symbol.for("react.suspense"),Te=Symbol.for("react.suspense_list"),Le=Symbol.for("react.memo"),Re=Symbol.for("react.lazy"),Ie=Symbol.for("react.offscreen"),De=Symbol.iterator;function Fe(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=De&&e[De]||e["@@iterator"])?e:null}var ze,Ae=Object.assign;function Oe(e){if(void 0===ze)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);ze=t&&t[1]||""}return"\n"+ze+e}var Me=!1;function Ue(e,t){if(!e||Me)return"";Me=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var a=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){a=c}e.call(t.prototype)}else{try{throw Error()}catch(c){a=c}e()}}catch(c){if(c&&a&&"string"==typeof c.stack){for(var r=c.stack.split("\n"),l=a.stack.split("\n"),i=r.length-1,s=l.length-1;1<=i&&0<=s&&r[i]!==l[s];)s--;for(;1<=i&&0<=s;i--,s--)if(r[i]!==l[s]){if(1!==i||1!==s)do{if(i--,0>--s||r[i]!==l[s]){var o="\n"+r[i].replace(" at new "," at ");return e.displayName&&o.includes("<anonymous>")&&(o=o.replace("<anonymous>",e.displayName)),o}}while(1<=i&&0<=s);break}}}finally{Me=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Oe(e):""}function $e(e){switch(e.tag){case 5:return Oe(e.type);case 16:return Oe("Lazy");case 13:return Oe("Suspense");case 19:return Oe("SuspenseList");case 0:case 2:case 15:return e=Ue(e.type,!1);case 11:return e=Ue(e.type.render,!1);case 1:return e=Ue(e.type,!0);default:return""}}function qe(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case Se:return"Fragment";case we:return"Portal";case Ne:return"Profiler";case ke:return"StrictMode";case Pe:return"Suspense";case Te:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case Ce:return(e.displayName||"Context")+".Consumer";case _e:return(e._context.displayName||"Context")+".Provider";case Ee:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case Le:return null!==(t=e.displayName||null)?t:qe(e.type)||"Memo";case Re:t=e._payload,e=e._init;try{return qe(e(t))}catch(n){}}return null}function Be(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return qe(t);case 8:return t===ke?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t}return null}function Qe(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function Ve(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function We(e){e._valueTracker||(e._valueTracker=function(e){var t=Ve(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var r=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return r.call(this)},set:function(e){a=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return a},setValue:function(e){a=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function He(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),a="";return e&&(a=Ve(e)?e.checked?"true":"false":e.value),(e=a)!==n&&(t.setValue(e),!0)}function Ke(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Ye(e,t){var n=t.checked;return Ae({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Ge(e,t){var n=null==t.defaultValue?"":t.defaultValue,a=null!=t.checked?t.checked:t.defaultChecked;n=Qe(null!=t.value?t.value:n),e._wrapperState={initialChecked:a,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Je(e,t){null!=(t=t.checked)&&xe(e,"checked",t,!1)}function Xe(e,t){Je(e,t);var n=Qe(t.value),a=t.type;if(null!=n)"number"===a?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===a||"reset"===a)return void e.removeAttribute("value");t.hasOwnProperty("value")?et(e,t.type,n):t.hasOwnProperty("defaultValue")&&et(e,t.type,Qe(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Ze(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var a=t.type;if(!("submit"!==a&&"reset"!==a||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function et(e,t,n){"number"===t&&Ke(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var tt=Array.isArray;function nt(e,t,n,a){if(e=e.options,t){t={};for(var r=0;r<n.length;r++)t["$"+n[r]]=!0;for(n=0;n<e.length;n++)r=t.hasOwnProperty("$"+e[n].value),e[n].selected!==r&&(e[n].selected=r),r&&a&&(e[n].defaultSelected=!0)}else{for(n=""+Qe(n),t=null,r=0;r<e.length;r++){if(e[r].value===n)return e[r].selected=!0,void(a&&(e[r].defaultSelected=!0));null!==t||e[r].disabled||(t=e[r])}null!==t&&(t.selected=!0)}}function at(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(le(91));return Ae({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function rt(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(le(92));if(tt(n)){if(1<n.length)throw Error(le(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:Qe(n)}}function lt(e,t){var n=Qe(t.value),a=Qe(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=a&&(e.defaultValue=""+a)}function it(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function st(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ot(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?st(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ct,ut,dt=(ut=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ct=ct||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ct.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,a){MSApp.execUnsafeLocalFunction(function(){return ut(e,t)})}:ut);function ft(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var ht={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pt=["Webkit","ms","Moz","O"];function mt(e,t,n){return null==t||"boolean"==typeof t||""===t?"":n||"number"!=typeof t||0===t||ht.hasOwnProperty(e)&&ht[e]?(""+t).trim():t+"px"}function vt(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var a=0===n.indexOf("--"),r=mt(n,t[n],a);"float"===n&&(n="cssFloat"),a?e.setProperty(n,r):e[n]=r}}Object.keys(ht).forEach(function(e){pt.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),ht[t]=ht[e]})});var gt=Ae({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function yt(e,t){if(t){if(gt[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(le(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(le(60));if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(le(61))}if(null!=t.style&&"object"!=typeof t.style)throw Error(le(62))}}function xt(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var bt=null;function jt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var wt=null,St=null,kt=null;function Nt(e){if(e=xl(e)){if("function"!=typeof wt)throw Error(le(280));var t=e.stateNode;t&&(t=jl(t),wt(e.stateNode,e.type,t))}}function _t(e){St?kt?kt.push(e):kt=[e]:St=e}function Ct(){if(St){var e=St,t=kt;if(kt=St=null,Nt(e),t)for(e=0;e<t.length;e++)Nt(t[e])}}function Et(e,t){return e(t)}function Pt(){}var Tt=!1;function Lt(e,t,n){if(Tt)return e(t,n);Tt=!0;try{return Et(e,t,n)}finally{Tt=!1,(null!==St||null!==kt)&&(Pt(),Ct())}}function Rt(e,t){var n=e.stateNode;if(null===n)return null;var a=jl(n);if(null===a)return null;n=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(a=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!a;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(le(231,t,typeof n));return n}var It=!1;if(ue)try{var Dt={};Object.defineProperty(Dt,"passive",{get:function(){It=!0}}),window.addEventListener("test",Dt,Dt),window.removeEventListener("test",Dt,Dt)}catch(ut){It=!1}function Ft(e,t,n,a,r,l,i,s,o){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var zt=!1,At=null,Ot=!1,Mt=null,Ut={onError:function(e){zt=!0,At=e}};function $t(e,t,n,a,r,l,i,s,o){zt=!1,At=null,Ft.apply(Ut,arguments)}function qt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Bt(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Qt(e){if(qt(e)!==e)throw Error(le(188))}function Vt(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=qt(e)))throw Error(le(188));return t!==e?null:e}for(var n=e,a=t;;){var r=n.return;if(null===r)break;var l=r.alternate;if(null===l){if(null!==(a=r.return)){n=a;continue}break}if(r.child===l.child){for(l=r.child;l;){if(l===n)return Qt(r),e;if(l===a)return Qt(r),t;l=l.sibling}throw Error(le(188))}if(n.return!==a.return)n=r,a=l;else{for(var i=!1,s=r.child;s;){if(s===n){i=!0,n=r,a=l;break}if(s===a){i=!0,a=r,n=l;break}s=s.sibling}if(!i){for(s=l.child;s;){if(s===n){i=!0,n=l,a=r;break}if(s===a){i=!0,a=l,n=r;break}s=s.sibling}if(!i)throw Error(le(189))}}if(n.alternate!==a)throw Error(le(190))}if(3!==n.tag)throw Error(le(188));return n.stateNode.current===n?e:t}(e))?Wt(e):null}function Wt(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Wt(e);if(null!==t)return t;e=e.sibling}return null}var Ht=re.unstable_scheduleCallback,Kt=re.unstable_cancelCallback,Yt=re.unstable_shouldYield,Gt=re.unstable_requestPaint,Jt=re.unstable_now,Xt=re.unstable_getCurrentPriorityLevel,Zt=re.unstable_ImmediatePriority,en=re.unstable_UserBlockingPriority,tn=re.unstable_NormalPriority,nn=re.unstable_LowPriority,an=re.unstable_IdlePriority,rn=null,ln=null;var sn=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(on(e)/cn|0)|0},on=Math.log,cn=Math.LN2;var un=64,dn=4194304;function fn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function hn(e,t){var n=e.pendingLanes;if(0===n)return 0;var a=0,r=e.suspendedLanes,l=e.pingedLanes,i=268435455&n;if(0!==i){var s=i&~r;0!==s?a=fn(s):0!==(l&=i)&&(a=fn(l))}else 0!==(i=n&~r)?a=fn(i):0!==l&&(a=fn(l));if(0===a)return 0;if(0!==t&&t!==a&&0===(t&r)&&((r=a&-a)>=(l=t&-t)||16===r&&4194240&l))return t;if(4&a&&(a|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=a;0<t;)r=1<<(n=31-sn(t)),a|=e[n],t&=~r;return a}function pn(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function mn(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function vn(){var e=un;return!(4194240&(un<<=1))&&(un=64),e}function gn(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function yn(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-sn(t)]=n}function xn(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var a=31-sn(n),r=1<<a;r&t|e[a]&t&&(e[a]|=t),n&=~r}}var bn=0;function jn(e){return 1<(e&=-e)?4<e?268435455&e?16:536870912:4:1}var wn,Sn,kn,Nn,_n,Cn=!1,En=[],Pn=null,Tn=null,Ln=null,Rn=new Map,In=new Map,Dn=[],Fn="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function zn(e,t){switch(e){case"focusin":case"focusout":Pn=null;break;case"dragenter":case"dragleave":Tn=null;break;case"mouseover":case"mouseout":Ln=null;break;case"pointerover":case"pointerout":Rn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":In.delete(t.pointerId)}}function An(e,t,n,a,r,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:a,nativeEvent:l,targetContainers:[r]},null!==t&&(null!==(t=xl(t))&&Sn(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,null!==r&&-1===t.indexOf(r)&&t.push(r),e)}function On(e){var t=yl(e.target);if(null!==t){var n=qt(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Bt(n)))return e.blockedOn=t,void _n(e.priority,function(){kn(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Mn(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Gn(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=xl(n))&&Sn(t),e.blockedOn=n,!1;var a=new(n=e.nativeEvent).constructor(n.type,n);bt=a,n.target.dispatchEvent(a),bt=null,t.shift()}return!0}function Un(e,t,n){Mn(e)&&n.delete(t)}function $n(){Cn=!1,null!==Pn&&Mn(Pn)&&(Pn=null),null!==Tn&&Mn(Tn)&&(Tn=null),null!==Ln&&Mn(Ln)&&(Ln=null),Rn.forEach(Un),In.forEach(Un)}function qn(e,t){e.blockedOn===t&&(e.blockedOn=null,Cn||(Cn=!0,re.unstable_scheduleCallback(re.unstable_NormalPriority,$n)))}function Bn(e){function t(t){return qn(t,e)}if(0<En.length){qn(En[0],e);for(var n=1;n<En.length;n++){var a=En[n];a.blockedOn===e&&(a.blockedOn=null)}}for(null!==Pn&&qn(Pn,e),null!==Tn&&qn(Tn,e),null!==Ln&&qn(Ln,e),Rn.forEach(t),In.forEach(t),n=0;n<Dn.length;n++)(a=Dn[n]).blockedOn===e&&(a.blockedOn=null);for(;0<Dn.length&&null===(n=Dn[0]).blockedOn;)On(n),null===n.blockedOn&&Dn.shift()}var Qn=be.ReactCurrentBatchConfig,Vn=!0;function Wn(e,t,n,a){var r=bn,l=Qn.transition;Qn.transition=null;try{bn=1,Kn(e,t,n,a)}finally{bn=r,Qn.transition=l}}function Hn(e,t,n,a){var r=bn,l=Qn.transition;Qn.transition=null;try{bn=4,Kn(e,t,n,a)}finally{bn=r,Qn.transition=l}}function Kn(e,t,n,a){if(Vn){var r=Gn(e,t,n,a);if(null===r)Qr(e,t,a,Yn,n),zn(e,a);else if(function(e,t,n,a,r){switch(t){case"focusin":return Pn=An(Pn,e,t,n,a,r),!0;case"dragenter":return Tn=An(Tn,e,t,n,a,r),!0;case"mouseover":return Ln=An(Ln,e,t,n,a,r),!0;case"pointerover":var l=r.pointerId;return Rn.set(l,An(Rn.get(l)||null,e,t,n,a,r)),!0;case"gotpointercapture":return l=r.pointerId,In.set(l,An(In.get(l)||null,e,t,n,a,r)),!0}return!1}(r,e,t,n,a))a.stopPropagation();else if(zn(e,a),4&t&&-1<Fn.indexOf(e)){for(;null!==r;){var l=xl(r);if(null!==l&&wn(l),null===(l=Gn(e,t,n,a))&&Qr(e,t,a,Yn,n),l===r)break;r=l}null!==r&&a.stopPropagation()}else Qr(e,t,a,null,n)}}var Yn=null;function Gn(e,t,n,a){if(Yn=null,null!==(e=yl(e=jt(a))))if(null===(t=qt(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Bt(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Yn=e,null}function Jn(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Xt()){case Zt:return 1;case en:return 4;case tn:case nn:return 16;case an:return 536870912;default:return 16}default:return 16}}var Xn=null,Zn=null,ea=null;function ta(){if(ea)return ea;var e,t,n=Zn,a=n.length,r="value"in Xn?Xn.value:Xn.textContent,l=r.length;for(e=0;e<a&&n[e]===r[e];e++);var i=a-e;for(t=1;t<=i&&n[a-t]===r[l-t];t++);return ea=r.slice(e,1<t?1-t:void 0)}function na(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function aa(){return!0}function ra(){return!1}function la(e){function t(t,n,a,r,l){for(var i in this._reactName=t,this._targetInst=a,this.type=n,this.nativeEvent=r,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(r):r[i]);return this.isDefaultPrevented=(null!=r.defaultPrevented?r.defaultPrevented:!1===r.returnValue)?aa:ra,this.isPropagationStopped=ra,this}return Ae(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=aa)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=aa)},persist:function(){},isPersistent:aa}),t}var ia,sa,oa,ca={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ua=la(ca),da=Ae({},ca,{view:0,detail:0}),fa=la(da),ha=Ae({},da,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Na,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==oa&&(oa&&"mousemove"===e.type?(ia=e.screenX-oa.screenX,sa=e.screenY-oa.screenY):sa=ia=0,oa=e),ia)},movementY:function(e){return"movementY"in e?e.movementY:sa}}),pa=la(ha),ma=la(Ae({},ha,{dataTransfer:0})),va=la(Ae({},da,{relatedTarget:0})),ga=la(Ae({},ca,{animationName:0,elapsedTime:0,pseudoElement:0})),ya=Ae({},ca,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),xa=la(ya),ba=la(Ae({},ca,{data:0})),ja={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},wa={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sa={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ka(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Sa[e])&&!!t[e]}function Na(){return ka}var _a=Ae({},da,{key:function(e){if(e.key){var t=ja[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=na(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?wa[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Na,charCode:function(e){return"keypress"===e.type?na(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?na(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Ca=la(_a),Ea=la(Ae({},ha,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Pa=la(Ae({},da,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Na})),Ta=la(Ae({},ca,{propertyName:0,elapsedTime:0,pseudoElement:0})),La=Ae({},ha,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ra=la(La),Ia=[9,13,27,32],Da=ue&&"CompositionEvent"in window,Fa=null;ue&&"documentMode"in document&&(Fa=document.documentMode);var za=ue&&"TextEvent"in window&&!Fa,Aa=ue&&(!Da||Fa&&8<Fa&&11>=Fa),Oa=String.fromCharCode(32),Ma=!1;function Ua(e,t){switch(e){case"keyup":return-1!==Ia.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function $a(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var qa=!1;var Ba={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Qa(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Ba[e.type]:"textarea"===t}function Va(e,t,n,a){_t(a),0<(t=Wr(t,"onChange")).length&&(n=new ua("onChange","change",null,n,a),e.push({event:n,listeners:t}))}var Wa=null,Ha=null;function Ka(e){Or(e,0)}function Ya(e){if(He(bl(e)))return e}function Ga(e,t){if("change"===e)return t}var Ja=!1;if(ue){var Xa;if(ue){var Za="oninput"in document;if(!Za){var er=document.createElement("div");er.setAttribute("oninput","return;"),Za="function"==typeof er.oninput}Xa=Za}else Xa=!1;Ja=Xa&&(!document.documentMode||9<document.documentMode)}function tr(){Wa&&(Wa.detachEvent("onpropertychange",nr),Ha=Wa=null)}function nr(e){if("value"===e.propertyName&&Ya(Ha)){var t=[];Va(t,Ha,e,jt(e)),Lt(Ka,t)}}function ar(e,t,n){"focusin"===e?(tr(),Ha=n,(Wa=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function rr(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Ya(Ha)}function lr(e,t){if("click"===e)return Ya(t)}function ir(e,t){if("input"===e||"change"===e)return Ya(t)}var sr="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function or(e,t){if(sr(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),a=Object.keys(t);if(n.length!==a.length)return!1;for(a=0;a<n.length;a++){var r=n[a];if(!de.call(t,r)||!sr(e[r],t[r]))return!1}return!0}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ur(e,t){var n,a=cr(e);for(e=0;a;){if(3===a.nodeType){if(n=e+a.textContent.length,e<=t&&n>=t)return{node:a,offset:t-e};e=n}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=cr(a)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=Ke();t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(a){n=!1}if(!n)break;t=Ke((e=t.contentWindow).document)}return t}function hr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function pr(e){var t=fr(),n=e.focusedElem,a=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==a&&hr(n))if(t=a.start,void 0===(e=a.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var r=n.textContent.length,l=Math.min(a.start,r);a=void 0===a.end?l:Math.min(a.end,r),!e.extend&&l>a&&(r=a,a=l,l=r),r=ur(n,l);var i=ur(n,a);r&&i&&(1!==e.rangeCount||e.anchorNode!==r.node||e.anchorOffset!==r.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(r.node,r.offset),e.removeAllRanges(),l>a?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"==typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=ue&&"documentMode"in document&&11>=document.documentMode,vr=null,gr=null,yr=null,xr=!1;function br(e,t,n){var a=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;xr||null==vr||vr!==Ke(a)||("selectionStart"in(a=vr)&&hr(a)?a={start:a.selectionStart,end:a.selectionEnd}:a={anchorNode:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset},yr&&or(yr,a)||(yr=a,0<(a=Wr(gr,"onSelect")).length&&(t=new ua("onSelect","select",null,t,n),e.push({event:t,listeners:a}),t.target=vr)))}function jr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var wr={animationend:jr("Animation","AnimationEnd"),animationiteration:jr("Animation","AnimationIteration"),animationstart:jr("Animation","AnimationStart"),transitionend:jr("Transition","TransitionEnd")},Sr={},kr={};function Nr(e){if(Sr[e])return Sr[e];if(!wr[e])return e;var t,n=wr[e];for(t in n)if(n.hasOwnProperty(t)&&t in kr)return Sr[e]=n[t];return e}ue&&(kr=document.createElement("div").style,"AnimationEvent"in window||(delete wr.animationend.animation,delete wr.animationiteration.animation,delete wr.animationstart.animation),"TransitionEvent"in window||delete wr.transitionend.transition);var _r=Nr("animationend"),Cr=Nr("animationiteration"),Er=Nr("animationstart"),Pr=Nr("transitionend"),Tr=new Map,Lr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Rr(e,t){Tr.set(e,t),oe(t,[e])}for(var Ir=0;Ir<Lr.length;Ir++){var Dr=Lr[Ir];Rr(Dr.toLowerCase(),"on"+(Dr[0].toUpperCase()+Dr.slice(1)))}Rr(_r,"onAnimationEnd"),Rr(Cr,"onAnimationIteration"),Rr(Er,"onAnimationStart"),Rr("dblclick","onDoubleClick"),Rr("focusin","onFocus"),Rr("focusout","onBlur"),Rr(Pr,"onTransitionEnd"),ce("onMouseEnter",["mouseout","mouseover"]),ce("onMouseLeave",["mouseout","mouseover"]),ce("onPointerEnter",["pointerout","pointerover"]),ce("onPointerLeave",["pointerout","pointerover"]),oe("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),oe("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),oe("onBeforeInput",["compositionend","keypress","textInput","paste"]),oe("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),oe("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),oe("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Fr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),zr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Fr));function Ar(e,t,n){var a=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,a,r,l,i,s,o){if($t.apply(this,arguments),zt){if(!zt)throw Error(le(198));var c=At;zt=!1,At=null,Ot||(Ot=!0,Mt=c)}}(a,t,void 0,e),e.currentTarget=null}function Or(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var a=e[n],r=a.event;a=a.listeners;e:{var l=void 0;if(t)for(var i=a.length-1;0<=i;i--){var s=a[i],o=s.instance,c=s.currentTarget;if(s=s.listener,o!==l&&r.isPropagationStopped())break e;Ar(r,s,c),l=o}else for(i=0;i<a.length;i++){if(o=(s=a[i]).instance,c=s.currentTarget,s=s.listener,o!==l&&r.isPropagationStopped())break e;Ar(r,s,c),l=o}}}if(Ot)throw e=Mt,Ot=!1,Mt=null,e}function Mr(e,t){var n=t[ml];void 0===n&&(n=t[ml]=new Set);var a=e+"__bubble";n.has(a)||(Br(t,e,2,!1),n.add(a))}function Ur(e,t,n){var a=0;t&&(a|=4),Br(n,e,a,t)}var $r="_reactListening"+Math.random().toString(36).slice(2);function qr(e){if(!e[$r]){e[$r]=!0,ie.forEach(function(t){"selectionchange"!==t&&(zr.has(t)||Ur(t,!1,e),Ur(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[$r]||(t[$r]=!0,Ur("selectionchange",!1,t))}}function Br(e,t,n,a){switch(Jn(t)){case 1:var r=Wn;break;case 4:r=Hn;break;default:r=Kn}n=r.bind(null,t,n,e),r=void 0,!It||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(r=!0),a?void 0!==r?e.addEventListener(t,n,{capture:!0,passive:r}):e.addEventListener(t,n,!0):void 0!==r?e.addEventListener(t,n,{passive:r}):e.addEventListener(t,n,!1)}function Qr(e,t,n,a,r){var l=a;if(!(1&t||2&t||null===a))e:for(;;){if(null===a)return;var i=a.tag;if(3===i||4===i){var s=a.stateNode.containerInfo;if(s===r||8===s.nodeType&&s.parentNode===r)break;if(4===i)for(i=a.return;null!==i;){var o=i.tag;if((3===o||4===o)&&((o=i.stateNode.containerInfo)===r||8===o.nodeType&&o.parentNode===r))return;i=i.return}for(;null!==s;){if(null===(i=yl(s)))return;if(5===(o=i.tag)||6===o){a=l=i;continue e}s=s.parentNode}}a=a.return}Lt(function(){var a=l,r=jt(n),i=[];e:{var s=Tr.get(e);if(void 0!==s){var o=ua,c=e;switch(e){case"keypress":if(0===na(n))break e;case"keydown":case"keyup":o=Ca;break;case"focusin":c="focus",o=va;break;case"focusout":c="blur",o=va;break;case"beforeblur":case"afterblur":o=va;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":o=pa;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":o=ma;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":o=Pa;break;case _r:case Cr:case Er:o=ga;break;case Pr:o=Ta;break;case"scroll":o=fa;break;case"wheel":o=Ra;break;case"copy":case"cut":case"paste":o=xa;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":o=Ea}var u=!!(4&t),d=!u&&"scroll"===e,f=u?null!==s?s+"Capture":null:s;u=[];for(var h,p=a;null!==p;){var m=(h=p).stateNode;if(5===h.tag&&null!==m&&(h=m,null!==f&&(null!=(m=Rt(p,f))&&u.push(Vr(p,m,h)))),d)break;p=p.return}0<u.length&&(s=new o(s,c,null,n,r),i.push({event:s,listeners:u}))}}if(!(7&t)){if(o="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===bt||!(c=n.relatedTarget||n.fromElement)||!yl(c)&&!c[pl])&&(o||s)&&(s=r.window===r?r:(s=r.ownerDocument)?s.defaultView||s.parentWindow:window,o?(o=a,null!==(c=(c=n.relatedTarget||n.toElement)?yl(c):null)&&(c!==(d=qt(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(o=null,c=a),o!==c)){if(u=pa,m="onMouseLeave",f="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(u=Ea,m="onPointerLeave",f="onPointerEnter",p="pointer"),d=null==o?s:bl(o),h=null==c?s:bl(c),(s=new u(m,p+"leave",o,n,r)).target=d,s.relatedTarget=h,m=null,yl(r)===a&&((u=new u(f,p+"enter",c,n,r)).target=h,u.relatedTarget=d,m=u),d=m,o&&c)e:{for(f=c,p=0,h=u=o;h;h=Hr(h))p++;for(h=0,m=f;m;m=Hr(m))h++;for(;0<p-h;)u=Hr(u),p--;for(;0<h-p;)f=Hr(f),h--;for(;p--;){if(u===f||null!==f&&u===f.alternate)break e;u=Hr(u),f=Hr(f)}u=null}else u=null;null!==o&&Kr(i,s,o,u,!1),null!==c&&null!==d&&Kr(i,d,c,u,!0)}if("select"===(o=(s=a?bl(a):window).nodeName&&s.nodeName.toLowerCase())||"input"===o&&"file"===s.type)var v=Ga;else if(Qa(s))if(Ja)v=ir;else{v=rr;var g=ar}else(o=s.nodeName)&&"input"===o.toLowerCase()&&("checkbox"===s.type||"radio"===s.type)&&(v=lr);switch(v&&(v=v(e,a))?Va(i,v,n,r):(g&&g(e,s,a),"focusout"===e&&(g=s._wrapperState)&&g.controlled&&"number"===s.type&&et(s,"number",s.value)),g=a?bl(a):window,e){case"focusin":(Qa(g)||"true"===g.contentEditable)&&(vr=g,gr=a,yr=null);break;case"focusout":yr=gr=vr=null;break;case"mousedown":xr=!0;break;case"contextmenu":case"mouseup":case"dragend":xr=!1,br(i,n,r);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":br(i,n,r)}var y;if(Da)e:{switch(e){case"compositionstart":var x="onCompositionStart";break e;case"compositionend":x="onCompositionEnd";break e;case"compositionupdate":x="onCompositionUpdate";break e}x=void 0}else qa?Ua(e,n)&&(x="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(x="onCompositionStart");x&&(Aa&&"ko"!==n.locale&&(qa||"onCompositionStart"!==x?"onCompositionEnd"===x&&qa&&(y=ta()):(Zn="value"in(Xn=r)?Xn.value:Xn.textContent,qa=!0)),0<(g=Wr(a,x)).length&&(x=new ba(x,e,null,n,r),i.push({event:x,listeners:g}),y?x.data=y:null!==(y=$a(n))&&(x.data=y))),(y=za?function(e,t){switch(e){case"compositionend":return $a(t);case"keypress":return 32!==t.which?null:(Ma=!0,Oa);case"textInput":return(e=t.data)===Oa&&Ma?null:e;default:return null}}(e,n):function(e,t){if(qa)return"compositionend"===e||!Da&&Ua(e,t)?(e=ta(),ea=Zn=Xn=null,qa=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Aa&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(a=Wr(a,"onBeforeInput")).length&&(r=new ba("onBeforeInput","beforeinput",null,n,r),i.push({event:r,listeners:a}),r.data=y))}Or(i,t)})}function Vr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Wr(e,t){for(var n=t+"Capture",a=[];null!==e;){var r=e,l=r.stateNode;5===r.tag&&null!==l&&(r=l,null!=(l=Rt(e,n))&&a.unshift(Vr(e,l,r)),null!=(l=Rt(e,t))&&a.push(Vr(e,l,r))),e=e.return}return a}function Hr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,a,r){for(var l=t._reactName,i=[];null!==n&&n!==a;){var s=n,o=s.alternate,c=s.stateNode;if(null!==o&&o===a)break;5===s.tag&&null!==c&&(s=c,r?null!=(o=Rt(n,l))&&i.unshift(Vr(n,o,s)):r||null!=(o=Rt(n,l))&&i.push(Vr(n,o,s))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Yr=/\r\n?/g,Gr=/\u0000|\uFFFD/g;function Jr(e){return("string"==typeof e?e:""+e).replace(Yr,"\n").replace(Gr,"")}function Xr(e,t,n){if(t=Jr(t),Jr(e)!==t&&n)throw Error(le(425))}function Zr(){}var el=null,tl=null;function nl(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var al="function"==typeof setTimeout?setTimeout:void 0,rl="function"==typeof clearTimeout?clearTimeout:void 0,ll="function"==typeof Promise?Promise:void 0,il="function"==typeof queueMicrotask?queueMicrotask:void 0!==ll?function(e){return ll.resolve(null).then(e).catch(sl)}:al;function sl(e){setTimeout(function(){throw e})}function ol(e,t){var n=t,a=0;do{var r=n.nextSibling;if(e.removeChild(n),r&&8===r.nodeType)if("/$"===(n=r.data)){if(0===a)return e.removeChild(r),void Bn(t);a--}else"$"!==n&&"$?"!==n&&"$!"!==n||a++;n=r}while(n);Bn(t)}function cl(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ul(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var dl=Math.random().toString(36).slice(2),fl="__reactFiber$"+dl,hl="__reactProps$"+dl,pl="__reactContainer$"+dl,ml="__reactEvents$"+dl,vl="__reactListeners$"+dl,gl="__reactHandles$"+dl;function yl(e){var t=e[fl];if(t)return t;for(var n=e.parentNode;n;){if(t=n[pl]||n[fl]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ul(e);null!==e;){if(n=e[fl])return n;e=ul(e)}return t}n=(e=n).parentNode}return null}function xl(e){return!(e=e[fl]||e[pl])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function bl(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(le(33))}function jl(e){return e[hl]||null}var wl=[],Sl=-1;function kl(e){return{current:e}}function Nl(e){0>Sl||(e.current=wl[Sl],wl[Sl]=null,Sl--)}function _l(e,t){Sl++,wl[Sl]=e.current,e.current=t}var Cl={},El=kl(Cl),Pl=kl(!1),Tl=Cl;function Ll(e,t){var n=e.type.contextTypes;if(!n)return Cl;var a=e.stateNode;if(a&&a.__reactInternalMemoizedUnmaskedChildContext===t)return a.__reactInternalMemoizedMaskedChildContext;var r,l={};for(r in n)l[r]=t[r];return a&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Rl(e){return null!=(e=e.childContextTypes)}function Il(){Nl(Pl),Nl(El)}function Dl(e,t,n){if(El.current!==Cl)throw Error(le(168));_l(El,t),_l(Pl,n)}function Fl(e,t,n){var a=e.stateNode;if(t=t.childContextTypes,"function"!=typeof a.getChildContext)return n;for(var r in a=a.getChildContext())if(!(r in t))throw Error(le(108,Be(e)||"Unknown",r));return Ae({},n,a)}function zl(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Cl,Tl=El.current,_l(El,e),_l(Pl,Pl.current),!0}function Al(e,t,n){var a=e.stateNode;if(!a)throw Error(le(169));n?(e=Fl(e,t,Tl),a.__reactInternalMemoizedMergedChildContext=e,Nl(Pl),Nl(El),_l(El,e)):Nl(Pl),_l(Pl,n)}var Ol=null,Ml=!1,Ul=!1;function $l(e){null===Ol?Ol=[e]:Ol.push(e)}function ql(){if(!Ul&&null!==Ol){Ul=!0;var e=0,t=bn;try{var n=Ol;for(bn=1;e<n.length;e++){var a=n[e];do{a=a(!0)}while(null!==a)}Ol=null,Ml=!1}catch(r){throw null!==Ol&&(Ol=Ol.slice(e+1)),Ht(Zt,ql),r}finally{bn=t,Ul=!1}}return null}var Bl=[],Ql=0,Vl=null,Wl=0,Hl=[],Kl=0,Yl=null,Gl=1,Jl="";function Xl(e,t){Bl[Ql++]=Wl,Bl[Ql++]=Vl,Vl=e,Wl=t}function Zl(e,t,n){Hl[Kl++]=Gl,Hl[Kl++]=Jl,Hl[Kl++]=Yl,Yl=e;var a=Gl;e=Jl;var r=32-sn(a)-1;a&=~(1<<r),n+=1;var l=32-sn(t)+r;if(30<l){var i=r-r%5;l=(a&(1<<i)-1).toString(32),a>>=i,r-=i,Gl=1<<32-sn(t)+r|n<<r|a,Jl=l+e}else Gl=1<<l|n<<r|a,Jl=e}function ei(e){null!==e.return&&(Xl(e,1),Zl(e,1,0))}function ti(e){for(;e===Vl;)Vl=Bl[--Ql],Bl[Ql]=null,Wl=Bl[--Ql],Bl[Ql]=null;for(;e===Yl;)Yl=Hl[--Kl],Hl[Kl]=null,Jl=Hl[--Kl],Hl[Kl]=null,Gl=Hl[--Kl],Hl[Kl]=null}var ni=null,ai=null,ri=!1,li=null;function ii(e,t){var n=Lu(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function si(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ni=e,ai=cl(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ni=e,ai=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Yl?{id:Gl,overflow:Jl}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Lu(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ni=e,ai=null,!0);default:return!1}}function oi(e){return!(!(1&e.mode)||128&e.flags)}function ci(e){if(ri){var t=ai;if(t){var n=t;if(!si(e,t)){if(oi(e))throw Error(le(418));t=cl(n.nextSibling);var a=ni;t&&si(e,t)?ii(a,n):(e.flags=-4097&e.flags|2,ri=!1,ni=e)}}else{if(oi(e))throw Error(le(418));e.flags=-4097&e.flags|2,ri=!1,ni=e}}}function ui(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ni=e}function di(e){if(e!==ni)return!1;if(!ri)return ui(e),ri=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!nl(e.type,e.memoizedProps)),t&&(t=ai)){if(oi(e))throw fi(),Error(le(418));for(;t;)ii(e,t),t=cl(t.nextSibling)}if(ui(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(le(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ai=cl(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ai=null}}else ai=ni?cl(e.stateNode.nextSibling):null;return!0}function fi(){for(var e=ai;e;)e=cl(e.nextSibling)}function hi(){ai=ni=null,ri=!1}function pi(e){null===li?li=[e]:li.push(e)}var mi=be.ReactCurrentBatchConfig;function vi(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(le(309));var a=n.stateNode}if(!a)throw Error(le(147,e));var r=a,l=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===l?t.ref:((t=function(e){var t=r.refs;null===e?delete t[l]:t[l]=e})._stringRef=l,t)}if("string"!=typeof e)throw Error(le(284));if(!n._owner)throw Error(le(290,e))}return e}function gi(e,t){throw e=Object.prototype.toString.call(t),Error(le(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function yi(e){return(0,e._init)(e._payload)}function xi(e){function t(t,n){if(e){var a=t.deletions;null===a?(t.deletions=[n],t.flags|=16):a.push(n)}}function n(n,a){if(!e)return null;for(;null!==a;)t(n,a),a=a.sibling;return null}function a(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function r(e,t){return(e=Iu(e,t)).index=0,e.sibling=null,e}function l(t,n,a){return t.index=a,e?null!==(a=t.alternate)?(a=a.index)<n?(t.flags|=2,n):a:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,a){return null===t||6!==t.tag?((t=Au(n,e.mode,a)).return=e,t):((t=r(t,n)).return=e,t)}function o(e,t,n,a){var l=n.type;return l===Se?u(e,t,n.props.children,a,n.key):null!==t&&(t.elementType===l||"object"==typeof l&&null!==l&&l.$$typeof===Re&&yi(l)===t.type)?((a=r(t,n.props)).ref=vi(e,t,n),a.return=e,a):((a=Du(n.type,n.key,n.props,null,e.mode,a)).ref=vi(e,t,n),a.return=e,a)}function c(e,t,n,a){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Ou(n,e.mode,a)).return=e,t):((t=r(t,n.children||[])).return=e,t)}function u(e,t,n,a,l){return null===t||7!==t.tag?((t=Fu(n,e.mode,a,l)).return=e,t):((t=r(t,n)).return=e,t)}function d(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t)return(t=Au(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case je:return(n=Du(t.type,t.key,t.props,null,e.mode,n)).ref=vi(e,null,t),n.return=e,n;case we:return(t=Ou(t,e.mode,n)).return=e,t;case Re:return d(e,(0,t._init)(t._payload),n)}if(tt(t)||Fe(t))return(t=Fu(t,e.mode,n,null)).return=e,t;gi(e,t)}return null}function f(e,t,n,a){var r=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n)return null!==r?null:s(e,t,""+n,a);if("object"==typeof n&&null!==n){switch(n.$$typeof){case je:return n.key===r?o(e,t,n,a):null;case we:return n.key===r?c(e,t,n,a):null;case Re:return f(e,t,(r=n._init)(n._payload),a)}if(tt(n)||Fe(n))return null!==r?null:u(e,t,n,a,null);gi(e,n)}return null}function h(e,t,n,a,r){if("string"==typeof a&&""!==a||"number"==typeof a)return s(t,e=e.get(n)||null,""+a,r);if("object"==typeof a&&null!==a){switch(a.$$typeof){case je:return o(t,e=e.get(null===a.key?n:a.key)||null,a,r);case we:return c(t,e=e.get(null===a.key?n:a.key)||null,a,r);case Re:return h(e,t,n,(0,a._init)(a._payload),r)}if(tt(a)||Fe(a))return u(t,e=e.get(n)||null,a,r,null);gi(t,a)}return null}return function s(o,c,u,p){if("object"==typeof u&&null!==u&&u.type===Se&&null===u.key&&(u=u.props.children),"object"==typeof u&&null!==u){switch(u.$$typeof){case je:e:{for(var m=u.key,v=c;null!==v;){if(v.key===m){if((m=u.type)===Se){if(7===v.tag){n(o,v.sibling),(c=r(v,u.props.children)).return=o,o=c;break e}}else if(v.elementType===m||"object"==typeof m&&null!==m&&m.$$typeof===Re&&yi(m)===v.type){n(o,v.sibling),(c=r(v,u.props)).ref=vi(o,v,u),c.return=o,o=c;break e}n(o,v);break}t(o,v),v=v.sibling}u.type===Se?((c=Fu(u.props.children,o.mode,p,u.key)).return=o,o=c):((p=Du(u.type,u.key,u.props,null,o.mode,p)).ref=vi(o,c,u),p.return=o,o=p)}return i(o);case we:e:{for(v=u.key;null!==c;){if(c.key===v){if(4===c.tag&&c.stateNode.containerInfo===u.containerInfo&&c.stateNode.implementation===u.implementation){n(o,c.sibling),(c=r(c,u.children||[])).return=o,o=c;break e}n(o,c);break}t(o,c),c=c.sibling}(c=Ou(u,o.mode,p)).return=o,o=c}return i(o);case Re:return s(o,c,(v=u._init)(u._payload),p)}if(tt(u))return function(r,i,s,o){for(var c=null,u=null,p=i,m=i=0,v=null;null!==p&&m<s.length;m++){p.index>m?(v=p,p=null):v=p.sibling;var g=f(r,p,s[m],o);if(null===g){null===p&&(p=v);break}e&&p&&null===g.alternate&&t(r,p),i=l(g,i,m),null===u?c=g:u.sibling=g,u=g,p=v}if(m===s.length)return n(r,p),ri&&Xl(r,m),c;if(null===p){for(;m<s.length;m++)null!==(p=d(r,s[m],o))&&(i=l(p,i,m),null===u?c=p:u.sibling=p,u=p);return ri&&Xl(r,m),c}for(p=a(r,p);m<s.length;m++)null!==(v=h(p,r,m,s[m],o))&&(e&&null!==v.alternate&&p.delete(null===v.key?m:v.key),i=l(v,i,m),null===u?c=v:u.sibling=v,u=v);return e&&p.forEach(function(e){return t(r,e)}),ri&&Xl(r,m),c}(o,c,u,p);if(Fe(u))return function(r,i,s,o){var c=Fe(s);if("function"!=typeof c)throw Error(le(150));if(null==(s=c.call(s)))throw Error(le(151));for(var u=c=null,p=i,m=i=0,v=null,g=s.next();null!==p&&!g.done;m++,g=s.next()){p.index>m?(v=p,p=null):v=p.sibling;var y=f(r,p,g.value,o);if(null===y){null===p&&(p=v);break}e&&p&&null===y.alternate&&t(r,p),i=l(y,i,m),null===u?c=y:u.sibling=y,u=y,p=v}if(g.done)return n(r,p),ri&&Xl(r,m),c;if(null===p){for(;!g.done;m++,g=s.next())null!==(g=d(r,g.value,o))&&(i=l(g,i,m),null===u?c=g:u.sibling=g,u=g);return ri&&Xl(r,m),c}for(p=a(r,p);!g.done;m++,g=s.next())null!==(g=h(p,r,m,g.value,o))&&(e&&null!==g.alternate&&p.delete(null===g.key?m:g.key),i=l(g,i,m),null===u?c=g:u.sibling=g,u=g);return e&&p.forEach(function(e){return t(r,e)}),ri&&Xl(r,m),c}(o,c,u,p);gi(o,u)}return"string"==typeof u&&""!==u||"number"==typeof u?(u=""+u,null!==c&&6===c.tag?(n(o,c.sibling),(c=r(c,u)).return=o,o=c):(n(o,c),(c=Au(u,o.mode,p)).return=o,o=c),i(o)):n(o,c)}}var bi=xi(!0),ji=xi(!1),wi=kl(null),Si=null,ki=null,Ni=null;function _i(){Ni=ki=Si=null}function Ci(e){var t=wi.current;Nl(wi),e._currentValue=t}function Ei(e,t,n){for(;null!==e;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==a&&(a.childLanes|=t)):null!==a&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===n)break;e=e.return}}function Pi(e,t){Si=e,Ni=ki=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(xo=!0),e.firstContext=null)}function Ti(e){var t=e._currentValue;if(Ni!==e)if(e={context:e,memoizedValue:t,next:null},null===ki){if(null===Si)throw Error(le(308));ki=e,Si.dependencies={lanes:0,firstContext:e}}else ki=ki.next=e;return t}var Li=null;function Ri(e){null===Li?Li=[e]:Li.push(e)}function Ii(e,t,n,a){var r=t.interleaved;return null===r?(n.next=n,Ri(t)):(n.next=r.next,r.next=n),t.interleaved=n,Di(e,a)}function Di(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Fi=!1;function zi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ai(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Oi(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Mi(e,t,n){var a=e.updateQueue;if(null===a)return null;if(a=a.shared,2&Ec){var r=a.pending;return null===r?t.next=t:(t.next=r.next,r.next=t),a.pending=t,Di(e,n)}return null===(r=a.interleaved)?(t.next=t,Ri(a)):(t.next=r.next,r.next=t),a.interleaved=t,Di(e,n)}function Ui(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194240&n)){var a=t.lanes;n|=a&=e.pendingLanes,t.lanes=n,xn(e,n)}}function $i(e,t){var n=e.updateQueue,a=e.alternate;if(null!==a&&n===(a=a.updateQueue)){var r=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===l?r=l=i:l=l.next=i,n=n.next}while(null!==n);null===l?r=l=t:l=l.next=t}else r=l=t;return n={baseState:a.baseState,firstBaseUpdate:r,lastBaseUpdate:l,shared:a.shared,effects:a.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function qi(e,t,n,a){var r=e.updateQueue;Fi=!1;var l=r.firstBaseUpdate,i=r.lastBaseUpdate,s=r.shared.pending;if(null!==s){r.shared.pending=null;var o=s,c=o.next;o.next=null,null===i?l=c:i.next=c,i=o;var u=e.alternate;null!==u&&((s=(u=u.updateQueue).lastBaseUpdate)!==i&&(null===s?u.firstBaseUpdate=c:s.next=c,u.lastBaseUpdate=o))}if(null!==l){var d=r.baseState;for(i=0,u=c=o=null,s=l;;){var f=s.lane,h=s.eventTime;if((a&f)===f){null!==u&&(u=u.next={eventTime:h,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var p=e,m=s;switch(f=t,h=n,m.tag){case 1:if("function"==typeof(p=m.payload)){d=p.call(h,d,f);break e}d=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null==(f="function"==typeof(p=m.payload)?p.call(h,d,f):p))break e;d=Ae({},d,f);break e;case 2:Fi=!0}}null!==s.callback&&0!==s.lane&&(e.flags|=64,null===(f=r.effects)?r.effects=[s]:f.push(s))}else h={eventTime:h,lane:f,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===u?(c=u=h,o=d):u=u.next=h,i|=f;if(null===(s=s.next)){if(null===(s=r.shared.pending))break;s=(f=s).next,f.next=null,r.lastBaseUpdate=f,r.shared.pending=null}}if(null===u&&(o=d),r.baseState=o,r.firstBaseUpdate=c,r.lastBaseUpdate=u,null!==(t=r.shared.interleaved)){r=t;do{i|=r.lane,r=r.next}while(r!==t)}else null===l&&(r.shared.lanes=0);zc|=i,e.lanes=i,e.memoizedState=d}}function Bi(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var a=e[t],r=a.callback;if(null!==r){if(a.callback=null,a=n,"function"!=typeof r)throw Error(le(191,r));r.call(a)}}}var Qi={},Vi=kl(Qi),Wi=kl(Qi),Hi=kl(Qi);function Ki(e){if(e===Qi)throw Error(le(174));return e}function Yi(e,t){switch(_l(Hi,t),_l(Wi,e),_l(Vi,Qi),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ot(null,"");break;default:t=ot(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Nl(Vi),_l(Vi,t)}function Gi(){Nl(Vi),Nl(Wi),Nl(Hi)}function Ji(e){Ki(Hi.current);var t=Ki(Vi.current),n=ot(t,e.type);t!==n&&(_l(Wi,e),_l(Vi,n))}function Xi(e){Wi.current===e&&(Nl(Vi),Nl(Wi))}var Zi=kl(0);function es(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ts=[];function ns(){for(var e=0;e<ts.length;e++)ts[e]._workInProgressVersionPrimary=null;ts.length=0}var as=be.ReactCurrentDispatcher,rs=be.ReactCurrentBatchConfig,ls=0,is=null,ss=null,os=null,cs=!1,us=!1,ds=0,fs=0;function hs(){throw Error(le(321))}function ps(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!sr(e[n],t[n]))return!1;return!0}function ms(e,t,n,a,r,l){if(ls=l,is=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,as.current=null===e||null===e.memoizedState?Xs:Zs,e=n(a,r),us){l=0;do{if(us=!1,ds=0,25<=l)throw Error(le(301));l+=1,os=ss=null,t.updateQueue=null,as.current=eo,e=n(a,r)}while(us)}if(as.current=Js,t=null!==ss&&null!==ss.next,ls=0,os=ss=is=null,cs=!1,t)throw Error(le(300));return e}function vs(){var e=0!==ds;return ds=0,e}function gs(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===os?is.memoizedState=os=e:os=os.next=e,os}function ys(){if(null===ss){var e=is.alternate;e=null!==e?e.memoizedState:null}else e=ss.next;var t=null===os?is.memoizedState:os.next;if(null!==t)os=t,ss=e;else{if(null===e)throw Error(le(310));e={memoizedState:(ss=e).memoizedState,baseState:ss.baseState,baseQueue:ss.baseQueue,queue:ss.queue,next:null},null===os?is.memoizedState=os=e:os=os.next=e}return os}function xs(e,t){return"function"==typeof t?t(e):t}function bs(e){var t=ys(),n=t.queue;if(null===n)throw Error(le(311));n.lastRenderedReducer=e;var a=ss,r=a.baseQueue,l=n.pending;if(null!==l){if(null!==r){var i=r.next;r.next=l.next,l.next=i}a.baseQueue=r=l,n.pending=null}if(null!==r){l=r.next,a=a.baseState;var s=i=null,o=null,c=l;do{var u=c.lane;if((ls&u)===u)null!==o&&(o=o.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),a=c.hasEagerState?c.eagerState:e(a,c.action);else{var d={lane:u,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===o?(s=o=d,i=a):o=o.next=d,is.lanes|=u,zc|=u}c=c.next}while(null!==c&&c!==l);null===o?i=a:o.next=s,sr(a,t.memoizedState)||(xo=!0),t.memoizedState=a,t.baseState=i,t.baseQueue=o,n.lastRenderedState=a}if(null!==(e=n.interleaved)){r=e;do{l=r.lane,is.lanes|=l,zc|=l,r=r.next}while(r!==e)}else null===r&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function js(e){var t=ys(),n=t.queue;if(null===n)throw Error(le(311));n.lastRenderedReducer=e;var a=n.dispatch,r=n.pending,l=t.memoizedState;if(null!==r){n.pending=null;var i=r=r.next;do{l=e(l,i.action),i=i.next}while(i!==r);sr(l,t.memoizedState)||(xo=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),n.lastRenderedState=l}return[l,a]}function ws(){}function Ss(e,t){var n=is,a=ys(),r=t(),l=!sr(a.memoizedState,r);if(l&&(a.memoizedState=r,xo=!0),a=a.queue,Fs(_s.bind(null,n,a,e),[e]),a.getSnapshot!==t||l||null!==os&&1&os.memoizedState.tag){if(n.flags|=2048,Ts(9,Ns.bind(null,n,a,r,t),void 0,null),null===Pc)throw Error(le(349));30&ls||ks(n,t,r)}return r}function ks(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=is.updateQueue)?(t={lastEffect:null,stores:null},is.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ns(e,t,n,a){t.value=n,t.getSnapshot=a,Cs(t)&&Es(e)}function _s(e,t,n){return n(function(){Cs(t)&&Es(e)})}function Cs(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!sr(e,n)}catch(a){return!0}}function Es(e){var t=Di(e,1);null!==t&&nu(t,e,1,-1)}function Ps(e){var t=gs();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xs,lastRenderedState:e},t.queue=e,e=e.dispatch=Hs.bind(null,is,e),[t.memoizedState,e]}function Ts(e,t,n,a){return e={tag:e,create:t,destroy:n,deps:a,next:null},null===(t=is.updateQueue)?(t={lastEffect:null,stores:null},is.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(a=n.next,n.next=e,e.next=a,t.lastEffect=e),e}function Ls(){return ys().memoizedState}function Rs(e,t,n,a){var r=gs();is.flags|=e,r.memoizedState=Ts(1|t,n,void 0,void 0===a?null:a)}function Is(e,t,n,a){var r=ys();a=void 0===a?null:a;var l=void 0;if(null!==ss){var i=ss.memoizedState;if(l=i.destroy,null!==a&&ps(a,i.deps))return void(r.memoizedState=Ts(t,n,l,a))}is.flags|=e,r.memoizedState=Ts(1|t,n,l,a)}function Ds(e,t){return Rs(8390656,8,e,t)}function Fs(e,t){return Is(2048,8,e,t)}function zs(e,t){return Is(4,2,e,t)}function As(e,t){return Is(4,4,e,t)}function Os(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Ms(e,t,n){return n=null!=n?n.concat([e]):null,Is(4,4,Os.bind(null,t,e),n)}function Us(){}function $s(e,t){var n=ys();t=void 0===t?null:t;var a=n.memoizedState;return null!==a&&null!==t&&ps(t,a[1])?a[0]:(n.memoizedState=[e,t],e)}function qs(e,t){var n=ys();t=void 0===t?null:t;var a=n.memoizedState;return null!==a&&null!==t&&ps(t,a[1])?a[0]:(e=e(),n.memoizedState=[e,t],e)}function Bs(e,t,n){return 21&ls?(sr(n,t)||(n=vn(),is.lanes|=n,zc|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,xo=!0),e.memoizedState=n)}function Qs(e,t){var n=bn;bn=0!==n&&4>n?n:4,e(!0);var a=rs.transition;rs.transition={};try{e(!1),t()}finally{bn=n,rs.transition=a}}function Vs(){return ys().memoizedState}function Ws(e,t,n){var a=tu(e);if(n={lane:a,action:n,hasEagerState:!1,eagerState:null,next:null},Ks(e))Ys(t,n);else if(null!==(n=Ii(e,t,n,a))){nu(n,e,a,eu()),Gs(n,t,a)}}function Hs(e,t,n){var a=tu(e),r={lane:a,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ks(e))Ys(t,r);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var i=t.lastRenderedState,s=l(i,n);if(r.hasEagerState=!0,r.eagerState=s,sr(s,i)){var o=t.interleaved;return null===o?(r.next=r,Ri(t)):(r.next=o.next,o.next=r),void(t.interleaved=r)}}catch(c){}null!==(n=Ii(e,t,r,a))&&(nu(n,e,a,r=eu()),Gs(n,t,a))}}function Ks(e){var t=e.alternate;return e===is||null!==t&&t===is}function Ys(e,t){us=cs=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Gs(e,t,n){if(4194240&n){var a=t.lanes;n|=a&=e.pendingLanes,t.lanes=n,xn(e,n)}}var Js={readContext:Ti,useCallback:hs,useContext:hs,useEffect:hs,useImperativeHandle:hs,useInsertionEffect:hs,useLayoutEffect:hs,useMemo:hs,useReducer:hs,useRef:hs,useState:hs,useDebugValue:hs,useDeferredValue:hs,useTransition:hs,useMutableSource:hs,useSyncExternalStore:hs,useId:hs,unstable_isNewReconciler:!1},Xs={readContext:Ti,useCallback:function(e,t){return gs().memoizedState=[e,void 0===t?null:t],e},useContext:Ti,useEffect:Ds,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,Rs(4194308,4,Os.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Rs(4194308,4,e,t)},useInsertionEffect:function(e,t){return Rs(4,2,e,t)},useMemo:function(e,t){var n=gs();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var a=gs();return t=void 0!==n?n(t):t,a.memoizedState=a.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},a.queue=e,e=e.dispatch=Ws.bind(null,is,e),[a.memoizedState,e]},useRef:function(e){return e={current:e},gs().memoizedState=e},useState:Ps,useDebugValue:Us,useDeferredValue:function(e){return gs().memoizedState=e},useTransition:function(){var e=Ps(!1),t=e[0];return e=Qs.bind(null,e[1]),gs().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var a=is,r=gs();if(ri){if(void 0===n)throw Error(le(407));n=n()}else{if(n=t(),null===Pc)throw Error(le(349));30&ls||ks(a,t,n)}r.memoizedState=n;var l={value:n,getSnapshot:t};return r.queue=l,Ds(_s.bind(null,a,l,e),[e]),a.flags|=2048,Ts(9,Ns.bind(null,a,l,n,t),void 0,null),n},useId:function(){var e=gs(),t=Pc.identifierPrefix;if(ri){var n=Jl;t=":"+t+"R"+(n=(Gl&~(1<<32-sn(Gl)-1)).toString(32)+n),0<(n=ds++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=fs++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Zs={readContext:Ti,useCallback:$s,useContext:Ti,useEffect:Fs,useImperativeHandle:Ms,useInsertionEffect:zs,useLayoutEffect:As,useMemo:qs,useReducer:bs,useRef:Ls,useState:function(){return bs(xs)},useDebugValue:Us,useDeferredValue:function(e){return Bs(ys(),ss.memoizedState,e)},useTransition:function(){return[bs(xs)[0],ys().memoizedState]},useMutableSource:ws,useSyncExternalStore:Ss,useId:Vs,unstable_isNewReconciler:!1},eo={readContext:Ti,useCallback:$s,useContext:Ti,useEffect:Fs,useImperativeHandle:Ms,useInsertionEffect:zs,useLayoutEffect:As,useMemo:qs,useReducer:js,useRef:Ls,useState:function(){return js(xs)},useDebugValue:Us,useDeferredValue:function(e){var t=ys();return null===ss?t.memoizedState=e:Bs(t,ss.memoizedState,e)},useTransition:function(){return[js(xs)[0],ys().memoizedState]},useMutableSource:ws,useSyncExternalStore:Ss,useId:Vs,unstable_isNewReconciler:!1};function to(e,t){if(e&&e.defaultProps){for(var n in t=Ae({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function no(e,t,n,a){n=null==(n=n(a,t=e.memoizedState))?t:Ae({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ao={isMounted:function(e){return!!(e=e._reactInternals)&&qt(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var a=eu(),r=tu(e),l=Oi(a,r);l.payload=t,null!=n&&(l.callback=n),null!==(t=Mi(e,l,r))&&(nu(t,e,r,a),Ui(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var a=eu(),r=tu(e),l=Oi(a,r);l.tag=1,l.payload=t,null!=n&&(l.callback=n),null!==(t=Mi(e,l,r))&&(nu(t,e,r,a),Ui(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),a=tu(e),r=Oi(n,a);r.tag=2,null!=t&&(r.callback=t),null!==(t=Mi(e,r,a))&&(nu(t,e,a,n),Ui(t,e,a))}};function ro(e,t,n,a,r,l,i){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(a,l,i):!t.prototype||!t.prototype.isPureReactComponent||(!or(n,a)||!or(r,l))}function lo(e,t,n){var a=!1,r=Cl,l=t.contextType;return"object"==typeof l&&null!==l?l=Ti(l):(r=Rl(t)?Tl:El.current,l=(a=null!=(a=t.contextTypes))?Ll(e,r):Cl),t=new t(n,l),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ao,e.stateNode=t,t._reactInternals=e,a&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=r,e.__reactInternalMemoizedMaskedChildContext=l),t}function io(e,t,n,a){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,a),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,a),t.state!==e&&ao.enqueueReplaceState(t,t.state,null)}function so(e,t,n,a){var r=e.stateNode;r.props=n,r.state=e.memoizedState,r.refs={},zi(e);var l=t.contextType;"object"==typeof l&&null!==l?r.context=Ti(l):(l=Rl(t)?Tl:El.current,r.context=Ll(e,l)),r.state=e.memoizedState,"function"==typeof(l=t.getDerivedStateFromProps)&&(no(e,t,l,n),r.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof r.getSnapshotBeforeUpdate||"function"!=typeof r.UNSAFE_componentWillMount&&"function"!=typeof r.componentWillMount||(t=r.state,"function"==typeof r.componentWillMount&&r.componentWillMount(),"function"==typeof r.UNSAFE_componentWillMount&&r.UNSAFE_componentWillMount(),t!==r.state&&ao.enqueueReplaceState(r,r.state,null),qi(e,n,r,a),r.state=e.memoizedState),"function"==typeof r.componentDidMount&&(e.flags|=4194308)}function oo(e,t){try{var n="",a=t;do{n+=$e(a),a=a.return}while(a);var r=n}catch(l){r="\nError generating stack: "+l.message+"\n"+l.stack}return{value:e,source:t,stack:r,digest:null}}function co(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function uo(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var fo="function"==typeof WeakMap?WeakMap:Map;function ho(e,t,n){(n=Oi(-1,n)).tag=3,n.payload={element:null};var a=t.value;return n.callback=function(){Qc||(Qc=!0,Vc=a),uo(0,t)},n}function po(e,t,n){(n=Oi(-1,n)).tag=3;var a=e.type.getDerivedStateFromError;if("function"==typeof a){var r=t.value;n.payload=function(){return a(r)},n.callback=function(){uo(0,t)}}var l=e.stateNode;return null!==l&&"function"==typeof l.componentDidCatch&&(n.callback=function(){uo(0,t),"function"!=typeof a&&(null===Wc?Wc=new Set([this]):Wc.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function mo(e,t,n){var a=e.pingCache;if(null===a){a=e.pingCache=new fo;var r=new Set;a.set(t,r)}else void 0===(r=a.get(t))&&(r=new Set,a.set(t,r));r.has(n)||(r.add(n),e=Nu.bind(null,e,t,n),t.then(e,e))}function vo(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function go(e,t,n,a,r){return 1&e.mode?(e.flags|=65536,e.lanes=r,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Oi(-1,1)).tag=2,Mi(n,t,1))),n.lanes|=1),e)}var yo=be.ReactCurrentOwner,xo=!1;function bo(e,t,n,a){t.child=null===e?ji(t,null,n,a):bi(t,e.child,n,a)}function jo(e,t,n,a,r){n=n.render;var l=t.ref;return Pi(t,r),a=ms(e,t,n,a,l,r),n=vs(),null===e||xo?(ri&&n&&ei(t),t.flags|=1,bo(e,t,a,r),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~r,Qo(e,t,r))}function wo(e,t,n,a,r){if(null===e){var l=n.type;return"function"!=typeof l||Ru(l)||void 0!==l.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Du(n.type,null,a,t,t.mode,r)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,So(e,t,l,a,r))}if(l=e.child,0===(e.lanes&r)){var i=l.memoizedProps;if((n=null!==(n=n.compare)?n:or)(i,a)&&e.ref===t.ref)return Qo(e,t,r)}return t.flags|=1,(e=Iu(l,a)).ref=t.ref,e.return=t,t.child=e}function So(e,t,n,a,r){if(null!==e){var l=e.memoizedProps;if(or(l,a)&&e.ref===t.ref){if(xo=!1,t.pendingProps=a=l,0===(e.lanes&r))return t.lanes=e.lanes,Qo(e,t,r);131072&e.flags&&(xo=!0)}}return _o(e,t,n,a,r)}function ko(e,t,n){var a=t.pendingProps,r=a.children,l=null!==e?e.memoizedState:null;if("hidden"===a.mode)if(1&t.mode){if(!(1073741824&n))return e=null!==l?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,_l(Ic,Rc),Rc|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},a=null!==l?l.baseLanes:n,_l(Ic,Rc),Rc|=a}else t.memoizedState={baseLanes:0,cachePool:null,transitions:null},_l(Ic,Rc),Rc|=n;else null!==l?(a=l.baseLanes|n,t.memoizedState=null):a=n,_l(Ic,Rc),Rc|=a;return bo(e,t,r,n),t.child}function No(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function _o(e,t,n,a,r){var l=Rl(n)?Tl:El.current;return l=Ll(t,l),Pi(t,r),n=ms(e,t,n,a,l,r),a=vs(),null===e||xo?(ri&&a&&ei(t),t.flags|=1,bo(e,t,n,r),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~r,Qo(e,t,r))}function Co(e,t,n,a,r){if(Rl(n)){var l=!0;zl(t)}else l=!1;if(Pi(t,r),null===t.stateNode)Bo(e,t),lo(t,n,a),so(t,n,a,r),a=!0;else if(null===e){var i=t.stateNode,s=t.memoizedProps;i.props=s;var o=i.context,c=n.contextType;"object"==typeof c&&null!==c?c=Ti(c):c=Ll(t,c=Rl(n)?Tl:El.current);var u=n.getDerivedStateFromProps,d="function"==typeof u||"function"==typeof i.getSnapshotBeforeUpdate;d||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(s!==a||o!==c)&&io(t,i,a,c),Fi=!1;var f=t.memoizedState;i.state=f,qi(t,a,i,r),o=t.memoizedState,s!==a||f!==o||Pl.current||Fi?("function"==typeof u&&(no(t,n,u,a),o=t.memoizedState),(s=Fi||ro(t,n,s,a,f,o,c))?(d||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||("function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"==typeof i.componentDidMount&&(t.flags|=4194308)):("function"==typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=o),i.props=a,i.state=o,i.context=c,a=s):("function"==typeof i.componentDidMount&&(t.flags|=4194308),a=!1)}else{i=t.stateNode,Ai(e,t),s=t.memoizedProps,c=t.type===t.elementType?s:to(t.type,s),i.props=c,d=t.pendingProps,f=i.context,"object"==typeof(o=n.contextType)&&null!==o?o=Ti(o):o=Ll(t,o=Rl(n)?Tl:El.current);var h=n.getDerivedStateFromProps;(u="function"==typeof h||"function"==typeof i.getSnapshotBeforeUpdate)||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(s!==d||f!==o)&&io(t,i,a,o),Fi=!1,f=t.memoizedState,i.state=f,qi(t,a,i,r);var p=t.memoizedState;s!==d||f!==p||Pl.current||Fi?("function"==typeof h&&(no(t,n,h,a),p=t.memoizedState),(c=Fi||ro(t,n,c,a,f,p,o)||!1)?(u||"function"!=typeof i.UNSAFE_componentWillUpdate&&"function"!=typeof i.componentWillUpdate||("function"==typeof i.componentWillUpdate&&i.componentWillUpdate(a,p,o),"function"==typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(a,p,o)),"function"==typeof i.componentDidUpdate&&(t.flags|=4),"function"==typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof i.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=p),i.props=a,i.state=p,i.context=o,a=c):("function"!=typeof i.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),a=!1)}return Eo(e,t,n,a,l,r)}function Eo(e,t,n,a,r,l){No(e,t);var i=!!(128&t.flags);if(!a&&!i)return r&&Al(t,n,!1),Qo(e,t,l);a=t.stateNode,yo.current=t;var s=i&&"function"!=typeof n.getDerivedStateFromError?null:a.render();return t.flags|=1,null!==e&&i?(t.child=bi(t,e.child,null,l),t.child=bi(t,null,s,l)):bo(e,t,s,l),t.memoizedState=a.state,r&&Al(t,n,!0),t.child}function Po(e){var t=e.stateNode;t.pendingContext?Dl(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Dl(0,t.context,!1),Yi(e,t.containerInfo)}function To(e,t,n,a,r){return hi(),pi(r),t.flags|=256,bo(e,t,n,a),t.child}var Lo,Ro,Io,Do,Fo={dehydrated:null,treeContext:null,retryLane:0};function zo(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ao(e,t,n){var a,r=t.pendingProps,l=Zi.current,i=!1,s=!!(128&t.flags);if((a=s)||(a=(null===e||null!==e.memoizedState)&&!!(2&l)),a?(i=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(l|=1),_l(Zi,1&l),null===e)return ci(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(1&t.mode?"$!"===e.data?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},1&r||null===i?i=zu(s,r,0,null):(i.childLanes=0,i.pendingProps=s),e=Fu(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=zo(n),t.memoizedState=Fo,e):Oo(t,s));if(null!==(l=e.memoizedState)&&null!==(a=l.dehydrated))return function(e,t,n,a,r,l,i){if(n)return 256&t.flags?(t.flags&=-257,Mo(e,t,i,a=co(Error(le(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(l=a.fallback,r=t.mode,a=zu({mode:"visible",children:a.children},r,0,null),(l=Fu(l,r,i,null)).flags|=2,a.return=t,l.return=t,a.sibling=l,t.child=a,1&t.mode&&bi(t,e.child,null,i),t.child.memoizedState=zo(i),t.memoizedState=Fo,l);if(!(1&t.mode))return Mo(e,t,i,null);if("$!"===r.data){if(a=r.nextSibling&&r.nextSibling.dataset)var s=a.dgst;return a=s,Mo(e,t,i,a=co(l=Error(le(419)),a,void 0))}if(s=0!==(i&e.childLanes),xo||s){if(null!==(a=Pc)){switch(i&-i){case 4:r=2;break;case 16:r=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:r=32;break;case 536870912:r=268435456;break;default:r=0}0!==(r=0!==(r&(a.suspendedLanes|i))?0:r)&&r!==l.retryLane&&(l.retryLane=r,Di(e,r),nu(a,e,r,-1))}return mu(),Mo(e,t,i,a=co(Error(le(421))))}return"$?"===r.data?(t.flags|=128,t.child=e.child,t=Cu.bind(null,e),r._reactRetry=t,null):(e=l.treeContext,ai=cl(r.nextSibling),ni=t,ri=!0,li=null,null!==e&&(Hl[Kl++]=Gl,Hl[Kl++]=Jl,Hl[Kl++]=Yl,Gl=e.id,Jl=e.overflow,Yl=t),t=Oo(t,a.children),t.flags|=4096,t)}(e,t,s,r,a,l,n);if(i){i=r.fallback,s=t.mode,a=(l=e.child).sibling;var o={mode:"hidden",children:r.children};return 1&s||t.child===l?(r=Iu(l,o)).subtreeFlags=14680064&l.subtreeFlags:((r=t.child).childLanes=0,r.pendingProps=o,t.deletions=null),null!==a?i=Iu(a,i):(i=Fu(i,s,n,null)).flags|=2,i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=null===(s=e.child.memoizedState)?zo(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Fo,r}return e=(i=e.child).sibling,r=Iu(i,{mode:"visible",children:r.children}),!(1&t.mode)&&(r.lanes=n),r.return=t,r.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Oo(e,t){return(t=zu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Mo(e,t,n,a){return null!==a&&pi(a),bi(t,e.child,null,n),(e=Oo(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Uo(e,t,n){e.lanes|=t;var a=e.alternate;null!==a&&(a.lanes|=t),Ei(e.return,t,n)}function $o(e,t,n,a,r){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:r}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=a,l.tail=n,l.tailMode=r)}function qo(e,t,n){var a=t.pendingProps,r=a.revealOrder,l=a.tail;if(bo(e,t,a.children,n),2&(a=Zi.current))a=1&a|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Uo(e,n,t);else if(19===e.tag)Uo(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}if(_l(Zi,a),1&t.mode)switch(r){case"forwards":for(n=t.child,r=null;null!==n;)null!==(e=n.alternate)&&null===es(e)&&(r=n),n=n.sibling;null===(n=r)?(r=t.child,t.child=null):(r=n.sibling,n.sibling=null),$o(t,!1,r,n,l);break;case"backwards":for(n=null,r=t.child,t.child=null;null!==r;){if(null!==(e=r.alternate)&&null===es(e)){t.child=r;break}e=r.sibling,r.sibling=n,n=r,r=e}$o(t,!0,n,null,l);break;case"together":$o(t,!1,null,null,void 0);break;default:t.memoizedState=null}else t.memoizedState=null;return t.child}function Bo(e,t){!(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Qo(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),zc|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(le(153));if(null!==t.child){for(n=Iu(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Iu(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Vo(e,t){if(!ri)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var a=null;null!==n;)null!==n.alternate&&(a=n),n=n.sibling;null===a?t||null===e.tail?e.tail=null:e.tail.sibling=null:a.sibling=null}}function Wo(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,a=0;if(t)for(var r=e.child;null!==r;)n|=r.lanes|r.childLanes,a|=14680064&r.subtreeFlags,a|=14680064&r.flags,r.return=e,r=r.sibling;else for(r=e.child;null!==r;)n|=r.lanes|r.childLanes,a|=r.subtreeFlags,a|=r.flags,r.return=e,r=r.sibling;return e.subtreeFlags|=a,e.childLanes=n,t}function Ho(e,t,n){var a=t.pendingProps;switch(ti(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Wo(t),null;case 1:case 17:return Rl(t.type)&&Il(),Wo(t),null;case 3:return a=t.stateNode,Gi(),Nl(Pl),Nl(El),ns(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),null!==e&&null!==e.child||(di(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,null!==li&&(iu(li),li=null))),Ro(e,t),Wo(t),null;case 5:Xi(t);var r=Ki(Hi.current);if(n=t.type,null!==e&&null!=t.stateNode)Io(e,t,n,a,r),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!a){if(null===t.stateNode)throw Error(le(166));return Wo(t),null}if(e=Ki(Vi.current),di(t)){a=t.stateNode,n=t.type;var l=t.memoizedProps;switch(a[fl]=t,a[hl]=l,e=!!(1&t.mode),n){case"dialog":Mr("cancel",a),Mr("close",a);break;case"iframe":case"object":case"embed":Mr("load",a);break;case"video":case"audio":for(r=0;r<Fr.length;r++)Mr(Fr[r],a);break;case"source":Mr("error",a);break;case"img":case"image":case"link":Mr("error",a),Mr("load",a);break;case"details":Mr("toggle",a);break;case"input":Ge(a,l),Mr("invalid",a);break;case"select":a._wrapperState={wasMultiple:!!l.multiple},Mr("invalid",a);break;case"textarea":rt(a,l),Mr("invalid",a)}for(var i in yt(n,l),r=null,l)if(l.hasOwnProperty(i)){var s=l[i];"children"===i?"string"==typeof s?a.textContent!==s&&(!0!==l.suppressHydrationWarning&&Xr(a.textContent,s,e),r=["children",s]):"number"==typeof s&&a.textContent!==""+s&&(!0!==l.suppressHydrationWarning&&Xr(a.textContent,s,e),r=["children",""+s]):se.hasOwnProperty(i)&&null!=s&&"onScroll"===i&&Mr("scroll",a)}switch(n){case"input":We(a),Ze(a,l,!0);break;case"textarea":We(a),it(a);break;case"select":case"option":break;default:"function"==typeof l.onClick&&(a.onclick=Zr)}a=r,t.updateQueue=a,null!==a&&(t.flags|=4)}else{i=9===r.nodeType?r:r.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=st(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=i.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"==typeof a.is?e=i.createElement(n,{is:a.is}):(e=i.createElement(n),"select"===n&&(i=e,a.multiple?i.multiple=!0:a.size&&(i.size=a.size))):e=i.createElementNS(e,n),e[fl]=t,e[hl]=a,Lo(e,t,!1,!1),t.stateNode=e;e:{switch(i=xt(n,a),n){case"dialog":Mr("cancel",e),Mr("close",e),r=a;break;case"iframe":case"object":case"embed":Mr("load",e),r=a;break;case"video":case"audio":for(r=0;r<Fr.length;r++)Mr(Fr[r],e);r=a;break;case"source":Mr("error",e),r=a;break;case"img":case"image":case"link":Mr("error",e),Mr("load",e),r=a;break;case"details":Mr("toggle",e),r=a;break;case"input":Ge(e,a),r=Ye(e,a),Mr("invalid",e);break;case"option":default:r=a;break;case"select":e._wrapperState={wasMultiple:!!a.multiple},r=Ae({},a,{value:void 0}),Mr("invalid",e);break;case"textarea":rt(e,a),r=at(e,a),Mr("invalid",e)}for(l in yt(n,r),s=r)if(s.hasOwnProperty(l)){var o=s[l];"style"===l?vt(e,o):"dangerouslySetInnerHTML"===l?null!=(o=o?o.__html:void 0)&&dt(e,o):"children"===l?"string"==typeof o?("textarea"!==n||""!==o)&&ft(e,o):"number"==typeof o&&ft(e,""+o):"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&"autoFocus"!==l&&(se.hasOwnProperty(l)?null!=o&&"onScroll"===l&&Mr("scroll",e):null!=o&&xe(e,l,o,i))}switch(n){case"input":We(e),Ze(e,a,!1);break;case"textarea":We(e),it(e);break;case"option":null!=a.value&&e.setAttribute("value",""+Qe(a.value));break;case"select":e.multiple=!!a.multiple,null!=(l=a.value)?nt(e,!!a.multiple,l,!1):null!=a.defaultValue&&nt(e,!!a.multiple,a.defaultValue,!0);break;default:"function"==typeof r.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":a=!!a.autoFocus;break e;case"img":a=!0;break e;default:a=!1}}a&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Wo(t),null;case 6:if(e&&null!=t.stateNode)Do(e,t,e.memoizedProps,a);else{if("string"!=typeof a&&null===t.stateNode)throw Error(le(166));if(n=Ki(Hi.current),Ki(Vi.current),di(t)){if(a=t.stateNode,n=t.memoizedProps,a[fl]=t,(l=a.nodeValue!==n)&&null!==(e=ni))switch(e.tag){case 3:Xr(a.nodeValue,n,!!(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Xr(a.nodeValue,n,!!(1&e.mode))}l&&(t.flags|=4)}else(a=(9===n.nodeType?n:n.ownerDocument).createTextNode(a))[fl]=t,t.stateNode=a}return Wo(t),null;case 13:if(Nl(Zi),a=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ri&&null!==ai&&1&t.mode&&!(128&t.flags))fi(),hi(),t.flags|=98560,l=!1;else if(l=di(t),null!==a&&null!==a.dehydrated){if(null===e){if(!l)throw Error(le(318));if(!(l=null!==(l=t.memoizedState)?l.dehydrated:null))throw Error(le(317));l[fl]=t}else hi(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Wo(t),l=!1}else null!==li&&(iu(li),li=null),l=!0;if(!l)return 65536&t.flags?t:null}return 128&t.flags?(t.lanes=n,t):((a=null!==a)!==(null!==e&&null!==e.memoizedState)&&a&&(t.child.flags|=8192,1&t.mode&&(null===e||1&Zi.current?0===Dc&&(Dc=3):mu())),null!==t.updateQueue&&(t.flags|=4),Wo(t),null);case 4:return Gi(),Ro(e,t),null===e&&qr(t.stateNode.containerInfo),Wo(t),null;case 10:return Ci(t.type._context),Wo(t),null;case 19:if(Nl(Zi),null===(l=t.memoizedState))return Wo(t),null;if(a=!!(128&t.flags),null===(i=l.rendering))if(a)Vo(l,!1);else{if(0!==Dc||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(i=es(e))){for(t.flags|=128,Vo(l,!1),null!==(a=i.updateQueue)&&(t.updateQueue=a,t.flags|=4),t.subtreeFlags=0,a=n,n=t.child;null!==n;)e=a,(l=n).flags&=14680066,null===(i=l.alternate)?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=i.childLanes,l.lanes=i.lanes,l.child=i.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=i.memoizedProps,l.memoizedState=i.memoizedState,l.updateQueue=i.updateQueue,l.type=i.type,e=i.dependencies,l.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return _l(Zi,1&Zi.current|2),t.child}e=e.sibling}null!==l.tail&&Jt()>qc&&(t.flags|=128,a=!0,Vo(l,!1),t.lanes=4194304)}else{if(!a)if(null!==(e=es(i))){if(t.flags|=128,a=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Vo(l,!0),null===l.tail&&"hidden"===l.tailMode&&!i.alternate&&!ri)return Wo(t),null}else 2*Jt()-l.renderingStartTime>qc&&1073741824!==n&&(t.flags|=128,a=!0,Vo(l,!1),t.lanes=4194304);l.isBackwards?(i.sibling=t.child,t.child=i):(null!==(n=l.last)?n.sibling=i:t.child=i,l.last=i)}return null!==l.tail?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=Jt(),t.sibling=null,n=Zi.current,_l(Zi,a?1&n|2:1&n),t):(Wo(t),null);case 22:case 23:return du(),a=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==a&&(t.flags|=8192),a&&1&t.mode?!!(1073741824&Rc)&&(Wo(t),6&t.subtreeFlags&&(t.flags|=8192)):Wo(t),null;case 24:case 25:return null}throw Error(le(156,t.tag))}function Ko(e,t){switch(ti(t),t.tag){case 1:return Rl(t.type)&&Il(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Gi(),Nl(Pl),Nl(El),ns(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Xi(t),null;case 13:if(Nl(Zi),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(le(340));hi()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Nl(Zi),null;case 4:return Gi(),null;case 10:return Ci(t.type._context),null;case 22:case 23:return du(),null;default:return null}}Lo=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ro=function(){},Io=function(e,t,n,a){var r=e.memoizedProps;if(r!==a){e=t.stateNode,Ki(Vi.current);var l,i=null;switch(n){case"input":r=Ye(e,r),a=Ye(e,a),i=[];break;case"select":r=Ae({},r,{value:void 0}),a=Ae({},a,{value:void 0}),i=[];break;case"textarea":r=at(e,r),a=at(e,a),i=[];break;default:"function"!=typeof r.onClick&&"function"==typeof a.onClick&&(e.onclick=Zr)}for(c in yt(n,a),n=null,r)if(!a.hasOwnProperty(c)&&r.hasOwnProperty(c)&&null!=r[c])if("style"===c){var s=r[c];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(se.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in a){var o=a[c];if(s=null!=r?r[c]:void 0,a.hasOwnProperty(c)&&o!==s&&(null!=o||null!=s))if("style"===c)if(s){for(l in s)!s.hasOwnProperty(l)||o&&o.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in o)o.hasOwnProperty(l)&&s[l]!==o[l]&&(n||(n={}),n[l]=o[l])}else n||(i||(i=[]),i.push(c,n)),n=o;else"dangerouslySetInnerHTML"===c?(o=o?o.__html:void 0,s=s?s.__html:void 0,null!=o&&s!==o&&(i=i||[]).push(c,o)):"children"===c?"string"!=typeof o&&"number"!=typeof o||(i=i||[]).push(c,""+o):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(se.hasOwnProperty(c)?(null!=o&&"onScroll"===c&&Mr("scroll",e),i||s===o||(i=[])):(i=i||[]).push(c,o))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}},Do=function(e,t,n,a){n!==a&&(t.flags|=4)};var Yo=!1,Go=!1,Jo="function"==typeof WeakSet?WeakSet:Set,Xo=null;function Zo(e,t){var n=e.ref;if(null!==n)if("function"==typeof n)try{n(null)}catch(a){ku(e,t,a)}else n.current=null}function ec(e,t,n){try{n()}catch(a){ku(e,t,a)}}var tc=!1;function nc(e,t,n){var a=t.updateQueue;if(null!==(a=null!==a?a.lastEffect:null)){var r=a=a.next;do{if((r.tag&e)===e){var l=r.destroy;r.destroy=void 0,void 0!==l&&ec(t,n,l)}r=r.next}while(r!==a)}}function ac(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var a=n.create;n.destroy=a()}n=n.next}while(n!==t)}}function rc(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"==typeof t?t(e):t.current=e}}function lc(e){var t=e.alternate;null!==t&&(e.alternate=null,lc(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fl],delete t[hl],delete t[ml],delete t[vl],delete t[gl])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ic(e){return 5===e.tag||3===e.tag||4===e.tag}function sc(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ic(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function oc(e,t,n){var a=e.tag;if(5===a||6===a)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Zr));else if(4!==a&&null!==(e=e.child))for(oc(e,t,n),e=e.sibling;null!==e;)oc(e,t,n),e=e.sibling}function cc(e,t,n){var a=e.tag;if(5===a||6===a)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==a&&null!==(e=e.child))for(cc(e,t,n),e=e.sibling;null!==e;)cc(e,t,n),e=e.sibling}var uc=null,dc=!1;function fc(e,t,n){for(n=n.child;null!==n;)hc(e,t,n),n=n.sibling}function hc(e,t,n){if(ln&&"function"==typeof ln.onCommitFiberUnmount)try{ln.onCommitFiberUnmount(rn,n)}catch(s){}switch(n.tag){case 5:Go||Zo(n,t);case 6:var a=uc,r=dc;uc=null,fc(e,t,n),dc=r,null!==(uc=a)&&(dc?(e=uc,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):uc.removeChild(n.stateNode));break;case 18:null!==uc&&(dc?(e=uc,n=n.stateNode,8===e.nodeType?ol(e.parentNode,n):1===e.nodeType&&ol(e,n),Bn(e)):ol(uc,n.stateNode));break;case 4:a=uc,r=dc,uc=n.stateNode.containerInfo,dc=!0,fc(e,t,n),uc=a,dc=r;break;case 0:case 11:case 14:case 15:if(!Go&&(null!==(a=n.updateQueue)&&null!==(a=a.lastEffect))){r=a=a.next;do{var l=r,i=l.destroy;l=l.tag,void 0!==i&&(2&l||4&l)&&ec(n,t,i),r=r.next}while(r!==a)}fc(e,t,n);break;case 1:if(!Go&&(Zo(n,t),"function"==typeof(a=n.stateNode).componentWillUnmount))try{a.props=n.memoizedProps,a.state=n.memoizedState,a.componentWillUnmount()}catch(s){ku(n,t,s)}fc(e,t,n);break;case 21:fc(e,t,n);break;case 22:1&n.mode?(Go=(a=Go)||null!==n.memoizedState,fc(e,t,n),Go=a):fc(e,t,n);break;default:fc(e,t,n)}}function pc(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Jo),t.forEach(function(t){var a=Eu.bind(null,e,t);n.has(t)||(n.add(t),t.then(a,a))})}}function mc(e,t){var n=t.deletions;if(null!==n)for(var a=0;a<n.length;a++){var r=n[a];try{var l=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 5:uc=s.stateNode,dc=!1;break e;case 3:case 4:uc=s.stateNode.containerInfo,dc=!0;break e}s=s.return}if(null===uc)throw Error(le(160));hc(l,i,r),uc=null,dc=!1;var o=r.alternate;null!==o&&(o.return=null),r.return=null}catch(c){ku(r,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)vc(t,e),t=t.sibling}function vc(e,t){var n=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(mc(t,e),gc(e),4&a){try{nc(3,e,e.return),ac(3,e)}catch(m){ku(e,e.return,m)}try{nc(5,e,e.return)}catch(m){ku(e,e.return,m)}}break;case 1:mc(t,e),gc(e),512&a&&null!==n&&Zo(n,n.return);break;case 5:if(mc(t,e),gc(e),512&a&&null!==n&&Zo(n,n.return),32&e.flags){var r=e.stateNode;try{ft(r,"")}catch(m){ku(e,e.return,m)}}if(4&a&&null!=(r=e.stateNode)){var l=e.memoizedProps,i=null!==n?n.memoizedProps:l,s=e.type,o=e.updateQueue;if(e.updateQueue=null,null!==o)try{"input"===s&&"radio"===l.type&&null!=l.name&&Je(r,l),xt(s,i);var c=xt(s,l);for(i=0;i<o.length;i+=2){var u=o[i],d=o[i+1];"style"===u?vt(r,d):"dangerouslySetInnerHTML"===u?dt(r,d):"children"===u?ft(r,d):xe(r,u,d,c)}switch(s){case"input":Xe(r,l);break;case"textarea":lt(r,l);break;case"select":var f=r._wrapperState.wasMultiple;r._wrapperState.wasMultiple=!!l.multiple;var h=l.value;null!=h?nt(r,!!l.multiple,h,!1):f!==!!l.multiple&&(null!=l.defaultValue?nt(r,!!l.multiple,l.defaultValue,!0):nt(r,!!l.multiple,l.multiple?[]:"",!1))}r[hl]=l}catch(m){ku(e,e.return,m)}}break;case 6:if(mc(t,e),gc(e),4&a){if(null===e.stateNode)throw Error(le(162));r=e.stateNode,l=e.memoizedProps;try{r.nodeValue=l}catch(m){ku(e,e.return,m)}}break;case 3:if(mc(t,e),gc(e),4&a&&null!==n&&n.memoizedState.isDehydrated)try{Bn(t.containerInfo)}catch(m){ku(e,e.return,m)}break;case 4:default:mc(t,e),gc(e);break;case 13:mc(t,e),gc(e),8192&(r=e.child).flags&&(l=null!==r.memoizedState,r.stateNode.isHidden=l,!l||null!==r.alternate&&null!==r.alternate.memoizedState||($c=Jt())),4&a&&pc(e);break;case 22:if(u=null!==n&&null!==n.memoizedState,1&e.mode?(Go=(c=Go)||u,mc(t,e),Go=c):mc(t,e),gc(e),8192&a){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!u&&1&e.mode)for(Xo=e,u=e.child;null!==u;){for(d=Xo=u;null!==Xo;){switch(h=(f=Xo).child,f.tag){case 0:case 11:case 14:case 15:nc(4,f,f.return);break;case 1:Zo(f,f.return);var p=f.stateNode;if("function"==typeof p.componentWillUnmount){a=f,n=f.return;try{t=a,p.props=t.memoizedProps,p.state=t.memoizedState,p.componentWillUnmount()}catch(m){ku(a,n,m)}}break;case 5:Zo(f,f.return);break;case 22:if(null!==f.memoizedState){jc(d);continue}}null!==h?(h.return=f,Xo=h):jc(d)}u=u.sibling}e:for(u=null,d=e;;){if(5===d.tag){if(null===u){u=d;try{r=d.stateNode,c?"function"==typeof(l=r.style).setProperty?l.setProperty("display","none","important"):l.display="none":(s=d.stateNode,i=null!=(o=d.memoizedProps.style)&&o.hasOwnProperty("display")?o.display:null,s.style.display=mt("display",i))}catch(m){ku(e,e.return,m)}}}else if(6===d.tag){if(null===u)try{d.stateNode.nodeValue=c?"":d.memoizedProps}catch(m){ku(e,e.return,m)}}else if((22!==d.tag&&23!==d.tag||null===d.memoizedState||d===e)&&null!==d.child){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;null===d.sibling;){if(null===d.return||d.return===e)break e;u===d&&(u=null),d=d.return}u===d&&(u=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:mc(t,e),gc(e),4&a&&pc(e);case 21:}}function gc(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(ic(n)){var a=n;break e}n=n.return}throw Error(le(160))}switch(a.tag){case 5:var r=a.stateNode;32&a.flags&&(ft(r,""),a.flags&=-33),cc(e,sc(e),r);break;case 3:case 4:var l=a.stateNode.containerInfo;oc(e,sc(e),l);break;default:throw Error(le(161))}}catch(i){ku(e,e.return,i)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function yc(e,t,n){Xo=e,xc(e)}function xc(e,t,n){for(var a=!!(1&e.mode);null!==Xo;){var r=Xo,l=r.child;if(22===r.tag&&a){var i=null!==r.memoizedState||Yo;if(!i){var s=r.alternate,o=null!==s&&null!==s.memoizedState||Go;s=Yo;var c=Go;if(Yo=i,(Go=o)&&!c)for(Xo=r;null!==Xo;)o=(i=Xo).child,22===i.tag&&null!==i.memoizedState?wc(r):null!==o?(o.return=i,Xo=o):wc(r);for(;null!==l;)Xo=l,xc(l),l=l.sibling;Xo=r,Yo=s,Go=c}bc(e)}else 8772&r.subtreeFlags&&null!==l?(l.return=r,Xo=l):bc(e)}}function bc(e){for(;null!==Xo;){var t=Xo;if(8772&t.flags){var n=t.alternate;try{if(8772&t.flags)switch(t.tag){case 0:case 11:case 15:Go||ac(5,t);break;case 1:var a=t.stateNode;if(4&t.flags&&!Go)if(null===n)a.componentDidMount();else{var r=t.elementType===t.type?n.memoizedProps:to(t.type,n.memoizedProps);a.componentDidUpdate(r,n.memoizedState,a.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;null!==l&&Bi(t,l,a);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Bi(t,i,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var o=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":o.autoFocus&&n.focus();break;case"img":o.src&&(n.src=o.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var u=c.memoizedState;if(null!==u){var d=u.dehydrated;null!==d&&Bn(d)}}}break;default:throw Error(le(163))}Go||512&t.flags&&rc(t)}catch(f){ku(t,t.return,f)}}if(t===e){Xo=null;break}if(null!==(n=t.sibling)){n.return=t.return,Xo=n;break}Xo=t.return}}function jc(e){for(;null!==Xo;){var t=Xo;if(t===e){Xo=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Xo=n;break}Xo=t.return}}function wc(e){for(;null!==Xo;){var t=Xo;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ac(4,t)}catch(o){ku(t,n,o)}break;case 1:var a=t.stateNode;if("function"==typeof a.componentDidMount){var r=t.return;try{a.componentDidMount()}catch(o){ku(t,r,o)}}var l=t.return;try{rc(t)}catch(o){ku(t,l,o)}break;case 5:var i=t.return;try{rc(t)}catch(o){ku(t,i,o)}}}catch(o){ku(t,t.return,o)}if(t===e){Xo=null;break}var s=t.sibling;if(null!==s){s.return=t.return,Xo=s;break}Xo=t.return}}var Sc,kc=Math.ceil,Nc=be.ReactCurrentDispatcher,_c=be.ReactCurrentOwner,Cc=be.ReactCurrentBatchConfig,Ec=0,Pc=null,Tc=null,Lc=0,Rc=0,Ic=kl(0),Dc=0,Fc=null,zc=0,Ac=0,Oc=0,Mc=null,Uc=null,$c=0,qc=1/0,Bc=null,Qc=!1,Vc=null,Wc=null,Hc=!1,Kc=null,Yc=0,Gc=0,Jc=null,Xc=-1,Zc=0;function eu(){return 6&Ec?Jt():-1!==Xc?Xc:Xc=Jt()}function tu(e){return 1&e.mode?2&Ec&&0!==Lc?Lc&-Lc:null!==mi.transition?(0===Zc&&(Zc=vn()),Zc):0!==(e=bn)?e:e=void 0===(e=window.event)?16:Jn(e.type):1}function nu(e,t,n,a){if(50<Gc)throw Gc=0,Jc=null,Error(le(185));yn(e,n,a),2&Ec&&e===Pc||(e===Pc&&(!(2&Ec)&&(Ac|=n),4===Dc&&su(e,Lc)),au(e,a),1===n&&0===Ec&&!(1&t.mode)&&(qc=Jt()+500,Ml&&ql()))}function au(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,a=e.pingedLanes,r=e.expirationTimes,l=e.pendingLanes;0<l;){var i=31-sn(l),s=1<<i,o=r[i];-1===o?0!==(s&n)&&0===(s&a)||(r[i]=pn(s,t)):o<=t&&(e.expiredLanes|=s),l&=~s}}(e,t);var a=hn(e,e===Pc?Lc:0);if(0===a)null!==n&&Kt(n),e.callbackNode=null,e.callbackPriority=0;else if(t=a&-a,e.callbackPriority!==t){if(null!=n&&Kt(n),1===t)0===e.tag?function(e){Ml=!0,$l(e)}(ou.bind(null,e)):$l(ou.bind(null,e)),il(function(){!(6&Ec)&&ql()}),n=null;else{switch(jn(a)){case 1:n=Zt;break;case 4:n=en;break;case 16:default:n=tn;break;case 536870912:n=an}n=Pu(n,ru.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ru(e,t){if(Xc=-1,Zc=0,6&Ec)throw Error(le(327));var n=e.callbackNode;if(wu()&&e.callbackNode!==n)return null;var a=hn(e,e===Pc?Lc:0);if(0===a)return null;if(30&a||0!==(a&e.expiredLanes)||t)t=vu(e,a);else{t=a;var r=Ec;Ec|=2;var l=pu();for(Pc===e&&Lc===t||(Bc=null,qc=Jt()+500,fu(e,t));;)try{yu();break}catch(s){hu(e,s)}_i(),Nc.current=l,Ec=r,null!==Tc?t=0:(Pc=null,Lc=0,t=Dc)}if(0!==t){if(2===t&&(0!==(r=mn(e))&&(a=r,t=lu(e,r))),1===t)throw n=Fc,fu(e,0),su(e,a),au(e,Jt()),n;if(6===t)su(e,a);else{if(r=e.current.alternate,!(30&a||function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var a=0;a<n.length;a++){var r=n[a],l=r.getSnapshot;r=r.value;try{if(!sr(l(),r))return!1}catch(i){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(r)||(t=vu(e,a),2===t&&(l=mn(e),0!==l&&(a=l,t=lu(e,l))),1!==t)))throw n=Fc,fu(e,0),su(e,a),au(e,Jt()),n;switch(e.finishedWork=r,e.finishedLanes=a,t){case 0:case 1:throw Error(le(345));case 2:case 5:ju(e,Uc,Bc);break;case 3:if(su(e,a),(130023424&a)===a&&10<(t=$c+500-Jt())){if(0!==hn(e,0))break;if(((r=e.suspendedLanes)&a)!==a){eu(),e.pingedLanes|=e.suspendedLanes&r;break}e.timeoutHandle=al(ju.bind(null,e,Uc,Bc),t);break}ju(e,Uc,Bc);break;case 4:if(su(e,a),(4194240&a)===a)break;for(t=e.eventTimes,r=-1;0<a;){var i=31-sn(a);l=1<<i,(i=t[i])>r&&(r=i),a&=~l}if(a=r,10<(a=(120>(a=Jt()-a)?120:480>a?480:1080>a?1080:1920>a?1920:3e3>a?3e3:4320>a?4320:1960*kc(a/1960))-a)){e.timeoutHandle=al(ju.bind(null,e,Uc,Bc),a);break}ju(e,Uc,Bc);break;default:throw Error(le(329))}}}return au(e,Jt()),e.callbackNode===n?ru.bind(null,e):null}function lu(e,t){var n=Mc;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=vu(e,t))&&(t=Uc,Uc=n,null!==t&&iu(t)),e}function iu(e){null===Uc?Uc=e:Uc.push.apply(Uc,e)}function su(e,t){for(t&=~Oc,t&=~Ac,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-sn(t),a=1<<n;e[n]=-1,t&=~a}}function ou(e){if(6&Ec)throw Error(le(327));wu();var t=hn(e,0);if(!(1&t))return au(e,Jt()),null;var n=vu(e,t);if(0!==e.tag&&2===n){var a=mn(e);0!==a&&(t=a,n=lu(e,a))}if(1===n)throw n=Fc,fu(e,0),su(e,t),au(e,Jt()),n;if(6===n)throw Error(le(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,ju(e,Uc,Bc),au(e,Jt()),null}function cu(e,t){var n=Ec;Ec|=1;try{return e(t)}finally{0===(Ec=n)&&(qc=Jt()+500,Ml&&ql())}}function uu(e){null!==Kc&&0===Kc.tag&&!(6&Ec)&&wu();var t=Ec;Ec|=1;var n=Cc.transition,a=bn;try{if(Cc.transition=null,bn=1,e)return e()}finally{bn=a,Cc.transition=n,!(6&(Ec=t))&&ql()}}function du(){Rc=Ic.current,Nl(Ic)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,rl(n)),null!==Tc)for(n=Tc.return;null!==n;){var a=n;switch(ti(a),a.tag){case 1:null!=(a=a.type.childContextTypes)&&Il();break;case 3:Gi(),Nl(Pl),Nl(El),ns();break;case 5:Xi(a);break;case 4:Gi();break;case 13:case 19:Nl(Zi);break;case 10:Ci(a.type._context);break;case 22:case 23:du()}n=n.return}if(Pc=e,Tc=e=Iu(e.current,null),Lc=Rc=t,Dc=0,Fc=null,Oc=Ac=zc=0,Uc=Mc=null,null!==Li){for(t=0;t<Li.length;t++)if(null!==(a=(n=Li[t]).interleaved)){n.interleaved=null;var r=a.next,l=n.pending;if(null!==l){var i=l.next;l.next=r,a.next=i}n.pending=a}Li=null}return e}function hu(e,t){for(;;){var n=Tc;try{if(_i(),as.current=Js,cs){for(var a=is.memoizedState;null!==a;){var r=a.queue;null!==r&&(r.pending=null),a=a.next}cs=!1}if(ls=0,os=ss=is=null,us=!1,ds=0,_c.current=null,null===n||null===n.return){Dc=1,Fc=t,Tc=null;break}e:{var l=e,i=n.return,s=n,o=t;if(t=Lc,s.flags|=32768,null!==o&&"object"==typeof o&&"function"==typeof o.then){var c=o,u=s,d=u.tag;if(!(1&u.mode||0!==d&&11!==d&&15!==d)){var f=u.alternate;f?(u.updateQueue=f.updateQueue,u.memoizedState=f.memoizedState,u.lanes=f.lanes):(u.updateQueue=null,u.memoizedState=null)}var h=vo(i);if(null!==h){h.flags&=-257,go(h,i,s,0,t),1&h.mode&&mo(l,c,t),o=c;var p=(t=h).updateQueue;if(null===p){var m=new Set;m.add(o),t.updateQueue=m}else p.add(o);break e}if(!(1&t)){mo(l,c,t),mu();break e}o=Error(le(426))}else if(ri&&1&s.mode){var v=vo(i);if(null!==v){!(65536&v.flags)&&(v.flags|=256),go(v,i,s,0,t),pi(oo(o,s));break e}}l=o=oo(o,s),4!==Dc&&(Dc=2),null===Mc?Mc=[l]:Mc.push(l),l=i;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t,$i(l,ho(0,o,t));break e;case 1:s=o;var g=l.type,y=l.stateNode;if(!(128&l.flags||"function"!=typeof g.getDerivedStateFromError&&(null===y||"function"!=typeof y.componentDidCatch||null!==Wc&&Wc.has(y)))){l.flags|=65536,t&=-t,l.lanes|=t,$i(l,po(l,s,t));break e}}l=l.return}while(null!==l)}bu(n)}catch(x){t=x,Tc===n&&null!==n&&(Tc=n=n.return);continue}break}}function pu(){var e=Nc.current;return Nc.current=Js,null===e?Js:e}function mu(){0!==Dc&&3!==Dc&&2!==Dc||(Dc=4),null===Pc||!(268435455&zc)&&!(268435455&Ac)||su(Pc,Lc)}function vu(e,t){var n=Ec;Ec|=2;var a=pu();for(Pc===e&&Lc===t||(Bc=null,fu(e,t));;)try{gu();break}catch(r){hu(e,r)}if(_i(),Ec=n,Nc.current=a,null!==Tc)throw Error(le(261));return Pc=null,Lc=0,Dc}function gu(){for(;null!==Tc;)xu(Tc)}function yu(){for(;null!==Tc&&!Yt();)xu(Tc)}function xu(e){var t=Sc(e.alternate,e,Rc);e.memoizedProps=e.pendingProps,null===t?bu(e):Tc=t,_c.current=null}function bu(e){var t=e;do{var n=t.alternate;if(e=t.return,32768&t.flags){if(null!==(n=Ko(n,t)))return n.flags&=32767,void(Tc=n);if(null===e)return Dc=6,void(Tc=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}else if(null!==(n=Ho(n,t,Rc)))return void(Tc=n);if(null!==(t=t.sibling))return void(Tc=t);Tc=t=e}while(null!==t);0===Dc&&(Dc=5)}function ju(e,t,n){var a=bn,r=Cc.transition;try{Cc.transition=null,bn=1,function(e,t,n,a){do{wu()}while(null!==Kc);if(6&Ec)throw Error(le(327));n=e.finishedWork;var r=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(le(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var a=e.eventTimes;for(e=e.expirationTimes;0<n;){var r=31-sn(n),l=1<<r;t[r]=0,a[r]=-1,e[r]=-1,n&=~l}}(e,l),e===Pc&&(Tc=Pc=null,Lc=0),!(2064&n.subtreeFlags)&&!(2064&n.flags)||Hc||(Hc=!0,Pu(tn,function(){return wu(),null})),l=!!(15990&n.flags),!!(15990&n.subtreeFlags)||l){l=Cc.transition,Cc.transition=null;var i=bn;bn=1;var s=Ec;Ec|=4,_c.current=null,function(e,t){if(el=Vn,hr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var a=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(a&&0!==a.rangeCount){n=a.anchorNode;var r=a.anchorOffset,l=a.focusNode;a=a.focusOffset;try{n.nodeType,l.nodeType}catch(b){n=null;break e}var i=0,s=-1,o=-1,c=0,u=0,d=e,f=null;t:for(;;){for(var h;d!==n||0!==r&&3!==d.nodeType||(s=i+r),d!==l||0!==a&&3!==d.nodeType||(o=i+a),3===d.nodeType&&(i+=d.nodeValue.length),null!==(h=d.firstChild);)f=d,d=h;for(;;){if(d===e)break t;if(f===n&&++c===r&&(s=i),f===l&&++u===a&&(o=i),null!==(h=d.nextSibling))break;f=(d=f).parentNode}d=h}n=-1===s||-1===o?null:{start:s,end:o}}else n=null}n=n||{start:0,end:0}}else n=null;for(tl={focusedElem:e,selectionRange:n},Vn=!1,Xo=t;null!==Xo;)if(e=(t=Xo).child,1028&t.subtreeFlags&&null!==e)e.return=t,Xo=e;else for(;null!==Xo;){t=Xo;try{var p=t.alternate;if(1024&t.flags)switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==p){var m=p.memoizedProps,v=p.memoizedState,g=t.stateNode,y=g.getSnapshotBeforeUpdate(t.elementType===t.type?m:to(t.type,m),v);g.__reactInternalSnapshotBeforeUpdate=y}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(le(163))}}catch(b){ku(t,t.return,b)}if(null!==(e=t.sibling)){e.return=t.return,Xo=e;break}Xo=t.return}p=tc,tc=!1}(e,n),vc(n,e),pr(tl),Vn=!!el,tl=el=null,e.current=n,yc(n),Gt(),Ec=s,bn=i,Cc.transition=l}else e.current=n;if(Hc&&(Hc=!1,Kc=e,Yc=r),l=e.pendingLanes,0===l&&(Wc=null),function(e){if(ln&&"function"==typeof ln.onCommitFiberRoot)try{ln.onCommitFiberRoot(rn,e,void 0,!(128&~e.current.flags))}catch(t){}}(n.stateNode),au(e,Jt()),null!==t)for(a=e.onRecoverableError,n=0;n<t.length;n++)r=t[n],a(r.value,{componentStack:r.stack,digest:r.digest});if(Qc)throw Qc=!1,e=Vc,Vc=null,e;!!(1&Yc)&&0!==e.tag&&wu(),l=e.pendingLanes,1&l?e===Jc?Gc++:(Gc=0,Jc=e):Gc=0,ql()}(e,t,n,a)}finally{Cc.transition=r,bn=a}return null}function wu(){if(null!==Kc){var e=jn(Yc),t=Cc.transition,n=bn;try{if(Cc.transition=null,bn=16>e?16:e,null===Kc)var a=!1;else{if(e=Kc,Kc=null,Yc=0,6&Ec)throw Error(le(331));var r=Ec;for(Ec|=4,Xo=e.current;null!==Xo;){var l=Xo,i=l.child;if(16&Xo.flags){var s=l.deletions;if(null!==s){for(var o=0;o<s.length;o++){var c=s[o];for(Xo=c;null!==Xo;){var u=Xo;switch(u.tag){case 0:case 11:case 15:nc(8,u,l)}var d=u.child;if(null!==d)d.return=u,Xo=d;else for(;null!==Xo;){var f=(u=Xo).sibling,h=u.return;if(lc(u),u===c){Xo=null;break}if(null!==f){f.return=h,Xo=f;break}Xo=h}}}var p=l.alternate;if(null!==p){var m=p.child;if(null!==m){p.child=null;do{var v=m.sibling;m.sibling=null,m=v}while(null!==m)}}Xo=l}}if(2064&l.subtreeFlags&&null!==i)i.return=l,Xo=i;else e:for(;null!==Xo;){if(2048&(l=Xo).flags)switch(l.tag){case 0:case 11:case 15:nc(9,l,l.return)}var g=l.sibling;if(null!==g){g.return=l.return,Xo=g;break e}Xo=l.return}}var y=e.current;for(Xo=y;null!==Xo;){var x=(i=Xo).child;if(2064&i.subtreeFlags&&null!==x)x.return=i,Xo=x;else e:for(i=y;null!==Xo;){if(2048&(s=Xo).flags)try{switch(s.tag){case 0:case 11:case 15:ac(9,s)}}catch(j){ku(s,s.return,j)}if(s===i){Xo=null;break e}var b=s.sibling;if(null!==b){b.return=s.return,Xo=b;break e}Xo=s.return}}if(Ec=r,ql(),ln&&"function"==typeof ln.onPostCommitFiberRoot)try{ln.onPostCommitFiberRoot(rn,e)}catch(j){}a=!0}return a}finally{bn=n,Cc.transition=t}}return!1}function Su(e,t,n){e=Mi(e,t=ho(0,t=oo(n,t),1),1),t=eu(),null!==e&&(yn(e,1,t),au(e,t))}function ku(e,t,n){if(3===e.tag)Su(e,e,n);else for(;null!==t;){if(3===t.tag){Su(t,e,n);break}if(1===t.tag){var a=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof a.componentDidCatch&&(null===Wc||!Wc.has(a))){t=Mi(t,e=po(t,e=oo(n,e),1),1),e=eu(),null!==t&&(yn(t,1,e),au(t,e));break}}t=t.return}}function Nu(e,t,n){var a=e.pingCache;null!==a&&a.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,Pc===e&&(Lc&n)===n&&(4===Dc||3===Dc&&(130023424&Lc)===Lc&&500>Jt()-$c?fu(e,0):Oc|=n),au(e,t)}function _u(e,t){0===t&&(1&e.mode?(t=dn,!(130023424&(dn<<=1))&&(dn=4194304)):t=1);var n=eu();null!==(e=Di(e,t))&&(yn(e,t,n),au(e,n))}function Cu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),_u(e,n)}function Eu(e,t){var n=0;switch(e.tag){case 13:var a=e.stateNode,r=e.memoizedState;null!==r&&(n=r.retryLane);break;case 19:a=e.stateNode;break;default:throw Error(le(314))}null!==a&&a.delete(t),_u(e,n)}function Pu(e,t){return Ht(e,t)}function Tu(e,t,n,a){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Lu(e,t,n,a){return new Tu(e,t,n,a)}function Ru(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Iu(e,t){var n=e.alternate;return null===n?((n=Lu(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Du(e,t,n,a,r,l){var i=2;if(a=e,"function"==typeof e)Ru(e)&&(i=1);else if("string"==typeof e)i=5;else e:switch(e){case Se:return Fu(n.children,r,l,t);case ke:i=8,r|=8;break;case Ne:return(e=Lu(12,n,t,2|r)).elementType=Ne,e.lanes=l,e;case Pe:return(e=Lu(13,n,t,r)).elementType=Pe,e.lanes=l,e;case Te:return(e=Lu(19,n,t,r)).elementType=Te,e.lanes=l,e;case Ie:return zu(n,r,l,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case _e:i=10;break e;case Ce:i=9;break e;case Ee:i=11;break e;case Le:i=14;break e;case Re:i=16,a=null;break e}throw Error(le(130,null==e?e:typeof e,""))}return(t=Lu(i,n,t,r)).elementType=e,t.type=a,t.lanes=l,t}function Fu(e,t,n,a){return(e=Lu(7,e,a,t)).lanes=n,e}function zu(e,t,n,a){return(e=Lu(22,e,a,t)).elementType=Ie,e.lanes=n,e.stateNode={isHidden:!1},e}function Au(e,t,n){return(e=Lu(6,e,null,t)).lanes=n,e}function Ou(e,t,n){return(t=Lu(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Mu(e,t,n,a,r){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gn(0),this.expirationTimes=gn(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gn(0),this.identifierPrefix=a,this.onRecoverableError=r,this.mutableSourceEagerHydrationData=null}function Uu(e,t,n,a,r,l,i,s,o){return e=new Mu(e,t,n,s,o),1===t?(t=1,!0===l&&(t|=8)):t=0,l=Lu(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:a,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},zi(l),e}function $u(e){if(!e)return Cl;e:{if(qt(e=e._reactInternals)!==e||1!==e.tag)throw Error(le(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Rl(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(le(171))}if(1===e.tag){var n=e.type;if(Rl(n))return Fl(e,n,t)}return t}function qu(e,t,n,a,r,l,i,s,o){return(e=Uu(n,a,!0,e,0,l,0,s,o)).context=$u(null),n=e.current,(l=Oi(a=eu(),r=tu(n))).callback=null!=t?t:null,Mi(n,l,r),e.current.lanes=r,yn(e,r,a),au(e,a),e}function Bu(e,t,n,a){var r=t.current,l=eu(),i=tu(r);return n=$u(n),null===t.context?t.context=n:t.pendingContext=n,(t=Oi(l,i)).payload={element:e},null!==(a=void 0===a?null:a)&&(t.callback=a),null!==(e=Mi(r,t,i))&&(nu(e,r,i,l),Ui(e,r,i)),i}function Qu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Vu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Wu(e,t){Vu(e,t),(e=e.alternate)&&Vu(e,t)}Sc=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Pl.current)xo=!0;else{if(0===(e.lanes&n)&&!(128&t.flags))return xo=!1,function(e,t,n){switch(t.tag){case 3:Po(t),hi();break;case 5:Ji(t);break;case 1:Rl(t.type)&&zl(t);break;case 4:Yi(t,t.stateNode.containerInfo);break;case 10:var a=t.type._context,r=t.memoizedProps.value;_l(wi,a._currentValue),a._currentValue=r;break;case 13:if(null!==(a=t.memoizedState))return null!==a.dehydrated?(_l(Zi,1&Zi.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Ao(e,t,n):(_l(Zi,1&Zi.current),null!==(e=Qo(e,t,n))?e.sibling:null);_l(Zi,1&Zi.current);break;case 19:if(a=0!==(n&t.childLanes),128&e.flags){if(a)return qo(e,t,n);t.flags|=128}if(null!==(r=t.memoizedState)&&(r.rendering=null,r.tail=null,r.lastEffect=null),_l(Zi,Zi.current),a)break;return null;case 22:case 23:return t.lanes=0,ko(e,t,n)}return Qo(e,t,n)}(e,t,n);xo=!!(131072&e.flags)}else xo=!1,ri&&1048576&t.flags&&Zl(t,Wl,t.index);switch(t.lanes=0,t.tag){case 2:var a=t.type;Bo(e,t),e=t.pendingProps;var r=Ll(t,El.current);Pi(t,n),r=ms(null,t,a,e,r,n);var l=vs();return t.flags|=1,"object"==typeof r&&null!==r&&"function"==typeof r.render&&void 0===r.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Rl(a)?(l=!0,zl(t)):l=!1,t.memoizedState=null!==r.state&&void 0!==r.state?r.state:null,zi(t),r.updater=ao,t.stateNode=r,r._reactInternals=t,so(t,a,e,n),t=Eo(null,t,a,!0,l,n)):(t.tag=0,ri&&l&&ei(t),bo(null,t,r,n),t=t.child),t;case 16:a=t.elementType;e:{switch(Bo(e,t),e=t.pendingProps,a=(r=a._init)(a._payload),t.type=a,r=t.tag=function(e){if("function"==typeof e)return Ru(e)?1:0;if(null!=e){if((e=e.$$typeof)===Ee)return 11;if(e===Le)return 14}return 2}(a),e=to(a,e),r){case 0:t=_o(null,t,a,e,n);break e;case 1:t=Co(null,t,a,e,n);break e;case 11:t=jo(null,t,a,e,n);break e;case 14:t=wo(null,t,a,to(a.type,e),n);break e}throw Error(le(306,a,""))}return t;case 0:return a=t.type,r=t.pendingProps,_o(e,t,a,r=t.elementType===a?r:to(a,r),n);case 1:return a=t.type,r=t.pendingProps,Co(e,t,a,r=t.elementType===a?r:to(a,r),n);case 3:e:{if(Po(t),null===e)throw Error(le(387));a=t.pendingProps,r=(l=t.memoizedState).element,Ai(e,t),qi(t,a,null,n);var i=t.memoizedState;if(a=i.element,l.isDehydrated){if(l={element:a,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=To(e,t,a,n,r=oo(Error(le(423)),t));break e}if(a!==r){t=To(e,t,a,n,r=oo(Error(le(424)),t));break e}for(ai=cl(t.stateNode.containerInfo.firstChild),ni=t,ri=!0,li=null,n=ji(t,null,a,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(hi(),a===r){t=Qo(e,t,n);break e}bo(e,t,a,n)}t=t.child}return t;case 5:return Ji(t),null===e&&ci(t),a=t.type,r=t.pendingProps,l=null!==e?e.memoizedProps:null,i=r.children,nl(a,r)?i=null:null!==l&&nl(a,l)&&(t.flags|=32),No(e,t),bo(e,t,i,n),t.child;case 6:return null===e&&ci(t),null;case 13:return Ao(e,t,n);case 4:return Yi(t,t.stateNode.containerInfo),a=t.pendingProps,null===e?t.child=bi(t,null,a,n):bo(e,t,a,n),t.child;case 11:return a=t.type,r=t.pendingProps,jo(e,t,a,r=t.elementType===a?r:to(a,r),n);case 7:return bo(e,t,t.pendingProps,n),t.child;case 8:case 12:return bo(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(a=t.type._context,r=t.pendingProps,l=t.memoizedProps,i=r.value,_l(wi,a._currentValue),a._currentValue=i,null!==l)if(sr(l.value,i)){if(l.children===r.children&&!Pl.current){t=Qo(e,t,n);break e}}else for(null!==(l=t.child)&&(l.return=t);null!==l;){var s=l.dependencies;if(null!==s){i=l.child;for(var o=s.firstContext;null!==o;){if(o.context===a){if(1===l.tag){(o=Oi(-1,n&-n)).tag=2;var c=l.updateQueue;if(null!==c){var u=(c=c.shared).pending;null===u?o.next=o:(o.next=u.next,u.next=o),c.pending=o}}l.lanes|=n,null!==(o=l.alternate)&&(o.lanes|=n),Ei(l.return,n,t),s.lanes|=n;break}o=o.next}}else if(10===l.tag)i=l.type===t.type?null:l.child;else if(18===l.tag){if(null===(i=l.return))throw Error(le(341));i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),Ei(i,n,t),i=l.sibling}else i=l.child;if(null!==i)i.return=l;else for(i=l;null!==i;){if(i===t){i=null;break}if(null!==(l=i.sibling)){l.return=i.return,i=l;break}i=i.return}l=i}bo(e,t,r.children,n),t=t.child}return t;case 9:return r=t.type,a=t.pendingProps.children,Pi(t,n),a=a(r=Ti(r)),t.flags|=1,bo(e,t,a,n),t.child;case 14:return r=to(a=t.type,t.pendingProps),wo(e,t,a,r=to(a.type,r),n);case 15:return So(e,t,t.type,t.pendingProps,n);case 17:return a=t.type,r=t.pendingProps,r=t.elementType===a?r:to(a,r),Bo(e,t),t.tag=1,Rl(a)?(e=!0,zl(t)):e=!1,Pi(t,n),lo(t,a,r),so(t,a,r,n),Eo(null,t,a,!0,e,n);case 19:return qo(e,t,n);case 22:return ko(e,t,n)}throw Error(le(156,t.tag))};var Hu="function"==typeof reportError?reportError:function(e){console.error(e)};function Ku(e){this._internalRoot=e}function Yu(e){this._internalRoot=e}function Gu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Ju(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Xu(){}function Zu(e,t,n,a,r){var l=n._reactRootContainer;if(l){var i=l;if("function"==typeof r){var s=r;r=function(){var e=Qu(i);s.call(e)}}Bu(t,i,e,r)}else i=function(e,t,n,a,r){if(r){if("function"==typeof a){var l=a;a=function(){var e=Qu(i);l.call(e)}}var i=qu(t,a,e,0,null,!1,0,"",Xu);return e._reactRootContainer=i,e[pl]=i.current,qr(8===e.nodeType?e.parentNode:e),uu(),i}for(;r=e.lastChild;)e.removeChild(r);if("function"==typeof a){var s=a;a=function(){var e=Qu(o);s.call(e)}}var o=Uu(e,0,!1,null,0,!1,0,"",Xu);return e._reactRootContainer=o,e[pl]=o.current,qr(8===e.nodeType?e.parentNode:e),uu(function(){Bu(t,o,n,a)}),o}(n,t,e,r,a);return Qu(i)}Yu.prototype.render=Ku.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(le(409));Bu(e,t,null,null)},Yu.prototype.unmount=Ku.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;uu(function(){Bu(null,e,null,null)}),t[pl]=null}},Yu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Nn();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Dn.length&&0!==t&&t<Dn[n].priority;n++);Dn.splice(n,0,e),0===n&&On(e)}},wn=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=fn(t.pendingLanes);0!==n&&(xn(t,1|n),au(t,Jt()),!(6&Ec)&&(qc=Jt()+500,ql()))}break;case 13:uu(function(){var t=Di(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}}),Wu(e,1)}},Sn=function(e){if(13===e.tag){var t=Di(e,134217728);if(null!==t)nu(t,e,134217728,eu());Wu(e,134217728)}},kn=function(e){if(13===e.tag){var t=tu(e),n=Di(e,t);if(null!==n)nu(n,e,t,eu());Wu(e,t)}},Nn=function(){return bn},_n=function(e,t){var n=bn;try{return bn=e,t()}finally{bn=n}},wt=function(e,t,n){switch(t){case"input":if(Xe(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var a=n[t];if(a!==e&&a.form===e.form){var r=jl(a);if(!r)throw Error(le(90));He(a),Xe(a,r)}}}break;case"textarea":lt(e,n);break;case"select":null!=(t=n.value)&&nt(e,!!n.multiple,t,!1)}},Et=cu,Pt=uu;var ed={usingClientEntryPoint:!1,Events:[xl,bl,jl,_t,Ct,cu]},td={findFiberByHostInstance:yl,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nd={bundleType:td.bundleType,version:td.version,rendererPackageName:td.rendererPackageName,rendererConfig:td.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:be.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Vt(e))?null:e.stateNode},findFiberByHostInstance:td.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ad=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ad.isDisabled&&ad.supportsFiber)try{rn=ad.inject(nd),ln=ad}catch(ut){}}Z.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ed,Z.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Gu(t))throw Error(le(200));return function(e,t,n){var a=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:we,key:null==a?null:""+a,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},Z.createRoot=function(e,t){if(!Gu(e))throw Error(le(299));var n=!1,a="",r=Hu;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(a=t.identifierPrefix),void 0!==t.onRecoverableError&&(r=t.onRecoverableError)),t=Uu(e,1,!1,null,0,n,0,a,r),e[pl]=t.current,qr(8===e.nodeType?e.parentNode:e),new Ku(t)},Z.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(le(188));throw e=Object.keys(e).join(","),Error(le(268,e))}return e=null===(e=Vt(t))?null:e.stateNode},Z.flushSync=function(e){return uu(e)},Z.hydrate=function(e,t,n){if(!Ju(t))throw Error(le(200));return Zu(null,e,t,!0,n)},Z.hydrateRoot=function(e,t,n){if(!Gu(e))throw Error(le(405));var a=null!=n&&n.hydratedSources||null,r=!1,l="",i=Hu;if(null!=n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(l=n.identifierPrefix),void 0!==n.onRecoverableError&&(i=n.onRecoverableError)),t=qu(t,null,e,1,null!=n?n:null,r,0,l,i),e[pl]=t.current,qr(e),a)for(e=0;e<a.length;e++)r=(r=(n=a[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,r]:t.mutableSourceEagerHydrationData.push(n,r);return new Yu(t)},Z.render=function(e,t,n){if(!Ju(t))throw Error(le(200));return Zu(null,e,t,!1,n)},Z.unmountComponentAtNode=function(e){if(!Ju(e))throw Error(le(40));return!!e._reactRootContainer&&(uu(function(){Zu(null,null,e,!1,function(){e._reactRootContainer=null,e[pl]=null})}),!0)},Z.unstable_batchedUpdates=cu,Z.unstable_renderSubtreeIntoContainer=function(e,t,n,a){if(!Ju(n))throw Error(le(200));if(null==e||void 0===e._reactInternals)throw Error(le(38));return Zu(e,t,n,!1,a)},Z.version="18.3.1-next-f1338f8080-20240426",function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),X.exports=Z;var rd,ld,id=X.exports;
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function sd(){return sd=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},sd.apply(this,arguments)}J.createRoot=id.createRoot,J.hydrateRoot=id.hydrateRoot,(ld=rd||(rd={})).Pop="POP",ld.Push="PUSH",ld.Replace="REPLACE";const od="popstate";function cd(e){return void 0===e&&(e={}),function(e,t,n,a){void 0===a&&(a={});let{window:r=document.defaultView,v5Compat:l=!1}=a,i=r.history,s=rd.Pop,o=null,c=u();null==c&&(c=0,i.replaceState(sd({},i.state,{idx:c}),""));function u(){return(i.state||{idx:null}).idx}function d(){s=rd.Pop;let e=u(),t=null==e?null:e-c;c=e,o&&o({action:s,location:m.location,delta:t})}function f(e,t){s=rd.Push;let a=hd(m.location,e,t);n&&n(a,e),c=u()+1;let d=fd(a,c),f=m.createHref(a);try{i.pushState(d,"",f)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;r.location.assign(f)}l&&o&&o({action:s,location:m.location,delta:1})}function h(e,t){s=rd.Replace;let a=hd(m.location,e,t);n&&n(a,e),c=u();let r=fd(a,c),d=m.createHref(a);i.replaceState(r,"",d),l&&o&&o({action:s,location:m.location,delta:0})}function p(e){let t="null"!==r.location.origin?r.location.origin:r.location.href,n="string"==typeof e?e:pd(e);return n=n.replace(/ $/,"%20"),ud(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}let m={get action(){return s},get location(){return e(r,i)},listen(e){if(o)throw new Error("A history only accepts one active listener");return r.addEventListener(od,d),o=e,()=>{r.removeEventListener(od,d),o=null}},createHref:e=>t(r,e),createURL:p,encodeLocation(e){let t=p(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:f,replace:h,go:e=>i.go(e)};return m}(function(e,t){let{pathname:n,search:a,hash:r}=e.location;return hd("",{pathname:n,search:a,hash:r},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"==typeof t?t:pd(t)},null,e)}function ud(e,t){if(!1===e||null==e)throw new Error(t)}function dd(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function fd(e,t){return{usr:e.state,key:e.key,idx:t}}function hd(e,t,n,a){return void 0===n&&(n=null),sd({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?md(t):t,{state:n,key:t&&t.key||a||Math.random().toString(36).substr(2,8)})}function pd(e){let{pathname:t="/",search:n="",hash:a=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),a&&"#"!==a&&(t+="#"===a.charAt(0)?a:"#"+a),t}function md(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let a=e.indexOf("?");a>=0&&(t.search=e.substr(a),e=e.substr(0,a)),e&&(t.pathname=e)}return t}var vd,gd;function yd(e,t,n){return void 0===n&&(n="/"),function(e,t,n,a){let r="string"==typeof t?md(t):t,l=Rd(r.pathname||"/",n);if(null==l)return null;let i=xd(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(i);let s=null;for(let o=0;null==s&&o<i.length;++o){let e=Ld(l);s=Pd(i[o],e,a)}return s}(e,t,n,!1)}function xd(e,t,n,a){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===a&&(a="");let r=(e,r,l)=>{let i={relativePath:void 0===l?e.path||"":l,caseSensitive:!0===e.caseSensitive,childrenIndex:r,route:e};i.relativePath.startsWith("/")&&(ud(i.relativePath.startsWith(a),'Absolute route path "'+i.relativePath+'" nested under path "'+a+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),i.relativePath=i.relativePath.slice(a.length));let s=zd([a,i.relativePath]),o=n.concat(i);e.children&&e.children.length>0&&(ud(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+s+'".'),xd(e.children,t,o,s)),(null!=e.path||e.index)&&t.push({path:s,score:Ed(s,e.index),routesMeta:o})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let a of bd(e.path))r(e,t,a);else r(e,t)}),t}function bd(e){let t=e.split("/");if(0===t.length)return[];let[n,...a]=t,r=n.endsWith("?"),l=n.replace(/\?$/,"");if(0===a.length)return r?[l,""]:[l];let i=bd(a.join("/")),s=[];return s.push(...i.map(e=>""===e?l:[l,e].join("/"))),r&&s.push(...i),s.map(t=>e.startsWith("/")&&""===t?"/":t)}(gd=vd||(vd={})).data="data",gd.deferred="deferred",gd.redirect="redirect",gd.error="error";const jd=/^:[\w-]+$/,wd=3,Sd=2,kd=1,Nd=10,_d=-2,Cd=e=>"*"===e;function Ed(e,t){let n=e.split("/"),a=n.length;return n.some(Cd)&&(a+=_d),t&&(a+=Sd),n.filter(e=>!Cd(e)).reduce((e,t)=>e+(jd.test(t)?wd:""===t?kd:Nd),a)}function Pd(e,t,n){void 0===n&&(n=!1);let{routesMeta:a}=e,r={},l="/",i=[];for(let s=0;s<a.length;++s){let e=a[s],o=s===a.length-1,c="/"===l?t:t.slice(l.length)||"/",u=Td({path:e.relativePath,caseSensitive:e.caseSensitive,end:o},c),d=e.route;if(!u&&o&&n&&!a[a.length-1].route.index&&(u=Td({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},c)),!u)return null;Object.assign(r,u.params),i.push({params:r,pathname:zd([l,u.pathname]),pathnameBase:Ad(zd([l,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(l=zd([l,u.pathnameBase]))}return i}function Td(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,a]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);dd("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let a=[],r="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(a.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(a.push({paramName:"*"}),r+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?r+="\\/*$":""!==e&&"/"!==e&&(r+="(?:(?=\\/|$))");let l=new RegExp(r,t?void 0:"i");return[l,a]}(e.path,e.caseSensitive,e.end),r=t.match(n);if(!r)return null;let l=r[0],i=l.replace(/(.)\/+$/,"$1"),s=r.slice(1);return{params:a.reduce((e,t,n)=>{let{paramName:a,isOptional:r}=t;if("*"===a){let e=s[n]||"";i=l.slice(0,l.length-e.length).replace(/(.)\/+$/,"$1")}const o=s[n];return e[a]=r&&!o?void 0:(o||"").replace(/%2F/g,"/"),e},{}),pathname:l,pathnameBase:i,pattern:e}}function Ld(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return dd(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function Rd(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,a=e.charAt(n);return a&&"/"!==a?null:e.slice(n)||"/"}function Id(e,t,n,a){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(a)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function Dd(e,t){let n=function(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}(e);return t?n.map((e,t)=>t===n.length-1?e.pathname:e.pathnameBase):n.map(e=>e.pathnameBase)}function Fd(e,t,n,a){let r;void 0===a&&(a=!1),"string"==typeof e?r=md(e):(r=sd({},e),ud(!r.pathname||!r.pathname.includes("?"),Id("?","pathname","search",r)),ud(!r.pathname||!r.pathname.includes("#"),Id("#","pathname","hash",r)),ud(!r.search||!r.search.includes("#"),Id("#","search","hash",r)));let l,i=""===e||""===r.pathname,s=i?"/":r.pathname;if(null==s)l=n;else{let e=t.length-1;if(!a&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;r.pathname=t.join("/")}l=e>=0?t[e]:"/"}let o=function(e,t){void 0===t&&(t="/");let{pathname:n,search:a="",hash:r=""}="string"==typeof e?md(e):e,l=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:l,search:Od(a),hash:Md(r)}}(r,l),c=s&&"/"!==s&&s.endsWith("/"),u=(i||"."===s)&&n.endsWith("/");return o.pathname.endsWith("/")||!c&&!u||(o.pathname+="/"),o}const zd=e=>e.join("/").replace(/\/\/+/g,"/"),Ad=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Od=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",Md=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";const Ud=["post","put","patch","delete"];new Set(Ud);const $d=["get",...Ud];
/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function qd(){return qd=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},qd.apply(this,arguments)}new Set($d);const Bd=U.createContext(null),Qd=U.createContext(null),Vd=U.createContext(null),Wd=U.createContext(null),Hd=U.createContext({outlet:null,matches:[],isDataRoute:!1}),Kd=U.createContext(null);function Yd(){return null!=U.useContext(Wd)}function Gd(){return Yd()||ud(!1),U.useContext(Wd).location}function Jd(e){U.useContext(Vd).static||U.useLayoutEffect(e)}function Xd(){let{isDataRoute:e}=U.useContext(Hd);return e?function(){let{router:e}=function(){let e=U.useContext(Bd);return e||ud(!1),e}(lf.UseNavigateStable),t=of(sf.UseNavigateStable),n=U.useRef(!1);return Jd(()=>{n.current=!0}),U.useCallback(function(a,r){void 0===r&&(r={}),n.current&&("number"==typeof a?e.navigate(a):e.navigate(a,qd({fromRouteId:t},r)))},[e,t])}():function(){Yd()||ud(!1);let e=U.useContext(Bd),{basename:t,future:n,navigator:a}=U.useContext(Vd),{matches:r}=U.useContext(Hd),{pathname:l}=Gd(),i=JSON.stringify(Dd(r,n.v7_relativeSplatPath)),s=U.useRef(!1);return Jd(()=>{s.current=!0}),U.useCallback(function(n,r){if(void 0===r&&(r={}),!s.current)return;if("number"==typeof n)return void a.go(n);let o=Fd(n,JSON.parse(i),l,"path"===r.relative);null==e&&"/"!==t&&(o.pathname="/"===o.pathname?t:zd([t,o.pathname])),(r.replace?a.replace:a.push)(o,r.state,r)},[t,a,i,l,e])}()}function Zd(e,t){let{relative:n}=void 0===t?{}:t,{future:a}=U.useContext(Vd),{matches:r}=U.useContext(Hd),{pathname:l}=Gd(),i=JSON.stringify(Dd(r,a.v7_relativeSplatPath));return U.useMemo(()=>Fd(e,JSON.parse(i),l,"path"===n),[e,i,l,n])}function ef(e,t){return function(e,t,n,a){Yd()||ud(!1);let{navigator:r}=U.useContext(Vd),{matches:l}=U.useContext(Hd),i=l[l.length-1],s=i?i.params:{};!i||i.pathname;let o=i?i.pathnameBase:"/";i&&i.route;let c,u=Gd();if(t){var d;let e="string"==typeof t?md(t):t;"/"===o||(null==(d=e.pathname)?void 0:d.startsWith(o))||ud(!1),c=e}else c=u;let f=c.pathname||"/",h=f;if("/"!==o){let e=o.replace(/^\//,"").split("/");h="/"+f.replace(/^\//,"").split("/").slice(e.length).join("/")}let p=yd(e,{pathname:h}),m=function(e,t,n,a){var r;void 0===t&&(t=[]);void 0===n&&(n=null);void 0===a&&(a=null);if(null==e){var l;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(l=a)&&l.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let i=e,s=null==(r=n)?void 0:r.errors;if(null!=s){let e=i.findIndex(e=>e.route.id&&void 0!==(null==s?void 0:s[e.route.id]));e>=0||ud(!1),i=i.slice(0,Math.min(i.length,e+1))}let o=!1,c=-1;if(n&&a&&a.v7_partialHydration)for(let u=0;u<i.length;u++){let e=i[u];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=u),e.route.id){let{loaderData:t,errors:a}=n,r=e.route.loader&&void 0===t[e.route.id]&&(!a||void 0===a[e.route.id]);if(e.route.lazy||r){o=!0,i=c>=0?i.slice(0,c+1):[i[0]];break}}}return i.reduceRight((e,a,r)=>{let l,u=!1,d=null,f=null;var h;n&&(l=s&&a.route.id?s[a.route.id]:void 0,d=a.route.errorElement||nf,o&&(c<0&&0===r?(h="route-fallback",!1||cf[h]||(cf[h]=!0),u=!0,f=null):c===r&&(u=!0,f=a.route.hydrateFallbackElement||null)));let p=t.concat(i.slice(0,r+1)),m=()=>{let t;return t=l?d:u?f:a.route.Component?U.createElement(a.route.Component,null):a.route.element?a.route.element:e,U.createElement(rf,{match:a,routeContext:{outlet:e,matches:p,isDataRoute:null!=n},children:t})};return n&&(a.route.ErrorBoundary||a.route.errorElement||0===r)?U.createElement(af,{location:n.location,revalidation:n.revalidation,component:d,error:l,children:m(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):m()},null)}(p&&p.map(e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:zd([o,r.encodeLocation?r.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?o:zd([o,r.encodeLocation?r.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),l,n,a);if(t&&m)return U.createElement(Wd.Provider,{value:{location:qd({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:rd.Pop}},m);return m}(e,t)}function tf(){let e=function(){var e;let t=U.useContext(Kd),n=function(){let e=U.useContext(Qd);return e||ud(!1),e}(sf.UseRouteError),a=of(sf.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[a]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return U.createElement(U.Fragment,null,U.createElement("h2",null,"Unexpected Application Error!"),U.createElement("h3",{style:{fontStyle:"italic"}},t),n?U.createElement("pre",{style:a},n):null,null)}const nf=U.createElement(tf,null);class af extends U.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?U.createElement(Hd.Provider,{value:this.props.routeContext},U.createElement(Kd.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function rf(e){let{routeContext:t,match:n,children:a}=e,r=U.useContext(Bd);return r&&r.static&&r.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=n.route.id),U.createElement(Hd.Provider,{value:t},a)}var lf=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(lf||{}),sf=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(sf||{});function of(e){let t=function(){let e=U.useContext(Hd);return e||ud(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||ud(!1),n.route.id}const cf={};function uf(e){let{to:t,replace:n,state:a,relative:r}=e;Yd()||ud(!1);let{future:l,static:i}=U.useContext(Vd),{matches:s}=U.useContext(Hd),{pathname:o}=Gd(),c=Xd(),u=Fd(t,Dd(s,l.v7_relativeSplatPath),o,"path"===r),d=JSON.stringify(u);return U.useEffect(()=>c(JSON.parse(d),{replace:n,state:a,relative:r}),[c,d,r,n,a]),null}function df(e){ud(!1)}function ff(e){let{basename:t="/",children:n=null,location:a,navigationType:r=rd.Pop,navigator:l,static:i=!1,future:s}=e;Yd()&&ud(!1);let o=t.replace(/^\/*/,"/"),c=U.useMemo(()=>({basename:o,navigator:l,static:i,future:qd({v7_relativeSplatPath:!1},s)}),[o,s,l,i]);"string"==typeof a&&(a=md(a));let{pathname:u="/",search:d="",hash:f="",state:h=null,key:p="default"}=a,m=U.useMemo(()=>{let e=Rd(u,o);return null==e?null:{location:{pathname:e,search:d,hash:f,state:h,key:p},navigationType:r}},[o,u,d,f,h,p,r]);return null==m?null:U.createElement(Vd.Provider,{value:c},U.createElement(Wd.Provider,{children:n,value:m}))}function hf(e){let{children:t,location:n}=e;return ef(pf(t),n)}function pf(e,t){void 0===t&&(t=[]);let n=[];return U.Children.forEach(e,(e,a)=>{if(!U.isValidElement(e))return;let r=[...t,a];if(e.type===U.Fragment)return void n.push.apply(n,pf(e.props.children,r));e.type!==df&&ud(!1),e.props.index&&e.props.children&&ud(!1);let l={id:e.props.id||r.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(l.children=pf(e.props.children,r)),n.push(l)}),n}
/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function mf(){return mf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},mf.apply(this,arguments)}new Promise(()=>{});const vf=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(Ih){}const gf=q.startTransition;function yf(e){let{basename:t,children:n,future:a,window:r}=e,l=U.useRef();null==l.current&&(l.current=cd({window:r,v5Compat:!0}));let i=l.current,[s,o]=U.useState({action:i.action,location:i.location}),{v7_startTransition:c}=a||{},u=U.useCallback(e=>{c&&gf?gf(()=>o(e)):o(e)},[o,c]);return U.useLayoutEffect(()=>i.listen(u),[i,u]),U.useEffect(()=>{return null==(e=a)||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),void(t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation));var e,t},[a]),U.createElement(ff,{basename:t,children:n,location:s.location,navigationType:s.action,navigator:i,future:a})}const xf="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,bf=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,jf=U.forwardRef(function(e,t){let n,{onClick:a,relative:r,reloadDocument:l,replace:i,state:s,target:o,to:c,preventScrollReset:u,viewTransition:d}=e,f=function(e,t){if(null==e)return{};var n,a,r={},l=Object.keys(e);for(a=0;a<l.length;a++)n=l[a],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,vf),{basename:h}=U.useContext(Vd),p=!1;if("string"==typeof c&&bf.test(c)&&(n=c,xf))try{let e=new URL(window.location.href),t=c.startsWith("//")?new URL(e.protocol+c):new URL(c),n=Rd(t.pathname,h);t.origin===e.origin&&null!=n?c=n+t.search+t.hash:p=!0}catch(Ih){}let m=function(e,t){let{relative:n}=void 0===t?{}:t;Yd()||ud(!1);let{basename:a,navigator:r}=U.useContext(Vd),{hash:l,pathname:i,search:s}=Zd(e,{relative:n}),o=i;return"/"!==a&&(o="/"===i?a:zd([a,i])),r.createHref({pathname:o,search:s,hash:l})}(c,{relative:r}),v=function(e,t){let{target:n,replace:a,state:r,preventScrollReset:l,relative:i,viewTransition:s}=void 0===t?{}:t,o=Xd(),c=Gd(),u=Zd(e,{relative:i});return U.useCallback(t=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(t,n)){t.preventDefault();let n=void 0!==a?a:pd(c)===pd(u);o(e,{replace:n,state:r,preventScrollReset:l,relative:i,viewTransition:s})}},[c,o,u,a,r,n,e,l,i,s])}(c,{replace:i,state:s,target:o,preventScrollReset:u,relative:r,viewTransition:d});return U.createElement("a",mf({},f,{href:n||m,onClick:p||l?a:function(e){a&&a(e),e.defaultPrevented||v(e)},ref:t,target:o}))});var wf,Sf,kf,Nf;(Sf=wf||(wf={})).UseScrollRestoration="useScrollRestoration",Sf.UseSubmit="useSubmit",Sf.UseSubmitFetcher="useSubmitFetcher",Sf.UseFetcher="useFetcher",Sf.useViewTransitionState="useViewTransitionState",(Nf=kf||(kf={})).UseFetcher="useFetcher",Nf.UseFetchers="useFetchers",Nf.UseScrollRestoration="useScrollRestoration";const _f="https://git.chcit.org/database-api",Cf={"Content-Type":"application/json",Accept:"application/json"};async function Ef(e){if(!e.ok){const t=await e.json().catch(()=>({status:e.status,message:e.statusText}));throw{status:e.status,message:t.message||e.statusText,details:t.details}}return 204===e.status?{}:await e.json()}function Pf(){return localStorage.getItem("access_token")}function Tf(){const e=Pf();return e?{...Cf,Authorization:`Bearer ${e}`}:Cf}const Lf=async e=>{const t=await fetch(`${_f}/auth/login`,{method:"POST",headers:Cf,body:JSON.stringify(e)}),n=await Ef(t);var a,r;return n.access_token&&n.refresh_token&&(a=n.access_token,r=n.refresh_token,localStorage.setItem("access_token",a),localStorage.setItem("refresh_token",r)),n},Rf=async()=>{const e=localStorage.getItem("refresh_token");if(!e)throw new Error("No refresh token available");const t=await fetch(`${_f}/auth/refresh`,{method:"POST",headers:Cf,body:JSON.stringify({refresh_token:e})}),n=await Ef(t);return n.access_token&&localStorage.setItem("access_token",n.access_token),n},If=async()=>{if(Pf())try{await fetch(`${_f}/auth/logout`,{method:"POST",headers:Tf()})}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token")}},Df=async()=>{if(!Pf())return!1;try{return(await fetch(`${_f}/health`,{method:"GET",headers:Tf()})).ok}catch(e){return console.error("Error validating token:",e),!1}},Ff=async()=>Ef(await fetch(`${_f}/health`,{method:"GET",headers:Tf()})),zf=async e=>Ef(await fetch(`${_f}/query`,{method:"POST",headers:Tf(),body:JSON.stringify(e)})),Af=async e=>Ef(await fetch(`${_f}/execute`,{method:"POST",headers:Tf(),body:JSON.stringify(e)})),Of=async e=>Ef(await fetch(`${_f}/transaction`,{method:"POST",headers:Tf(),body:JSON.stringify(e)})),Mf=async()=>Ef(await fetch(`${_f}/databases`,{method:"GET",headers:Tf()})),Uf=async(e,t)=>Ef(await fetch(`${_f}/databases`,{method:"POST",headers:Tf(),body:JSON.stringify({database_name:e,owner:t})})),$f=async e=>Ef(await fetch(`${_f}/databases`,{method:"DELETE",headers:Tf(),body:JSON.stringify({database_name:e})})),qf=async e=>Ef(await fetch(`${_f}/credentials/store`,{method:"POST",headers:Tf(),body:JSON.stringify(e)})),Bf=async e=>Ef(await fetch(`${_f}/credentials/get`,{method:"GET",headers:{...Tf(),"X-Credential-ID":e}})),Qf=async e=>Ef(await fetch(`${_f}/credentials/remove`,{method:"DELETE",headers:{...Tf(),"X-Credential-ID":e}})),Vf=async()=>Ef(await fetch(`${_f}/credentials/list`,{method:"GET",headers:Tf()})),Wf=async e=>Ef(await fetch(`${_f}/credentials/test`,{method:"POST",headers:{...Tf(),"X-Credential-ID":e}})),Hf=async()=>Ef(await fetch(`${_f}/database/metrics`,{method:"GET",headers:Tf()})),Kf=async()=>Ef(await fetch(`${_f}/database/metrics/connection-pool`,{method:"GET",headers:Tf()})),Yf=async()=>Ef(await fetch(`${_f}/database/metrics/query-performance`,{method:"GET",headers:Tf()})),Gf=async()=>Ef(await fetch(`${_f}/settings`,{method:"GET",headers:Tf()})),Jf=async e=>Ef(await fetch(`${_f}/settings`,{method:"PUT",headers:Tf(),body:JSON.stringify(e)})),Xf=async()=>Ef(await fetch(`${_f}/applications`,{method:"GET",headers:Tf()})),Zf=async e=>Ef(await fetch(`${_f}/applications/register`,{method:"POST",headers:Tf(),body:JSON.stringify(e)})),eh=async e=>Ef(await fetch(`${_f}/applications/${e}/activate`,{method:"POST",headers:Tf()})),th=async e=>Ef(await fetch(`${_f}/applications/${e}/deactivate`,{method:"POST",headers:Tf()})),nh=async e=>Ef(await fetch(`${_f}/applications/${e}/api-key`,{method:"POST",headers:Tf()})),ah=async(e={})=>{const t=new URLSearchParams;e.fromTime&&t.append("from_time",e.fromTime),e.toTime&&t.append("to_time",e.toTime),e.eventType&&t.append("event_type",e.eventType),e.userId&&t.append("user_id",e.userId.toString()),e.applicationId&&t.append("application_id",e.applicationId.toString()),e.action&&t.append("action",e.action),e.resource&&t.append("resource",e.resource),void 0!==e.success&&t.append("success",e.success.toString()),e.limit&&t.append("limit",e.limit.toString()),e.offset&&t.append("offset",e.offset.toString());return Ef(await fetch(`${_f}/audit/trail?${t.toString()}`,{method:"GET",headers:Tf()}))},rh=async(e={})=>{const t=new URLSearchParams;e.fromTime&&t.append("from_time",e.fromTime),e.toTime&&t.append("to_time",e.toTime),e.eventType&&t.append("event_type",e.eventType),e.userId&&t.append("user_id",e.userId.toString()),e.applicationId&&t.append("application_id",e.applicationId.toString());const n=await fetch(`${_f}/audit/export?${t.toString()}`,{method:"GET",headers:Tf()});if(!n.ok)throw new Error("Failed to export audit logs");return n.blob()},lh=async()=>Ef(await fetch(`${_f}/rate-limit/stats`,{method:"GET",headers:Tf()})),ih=async e=>Ef(await fetch(`${_f}/rate-limit/client/${encodeURIComponent(e)}`,{method:"GET",headers:Tf()})),sh=async e=>Ef(await fetch(`${_f}/rate-limit/whitelist`,{method:"POST",headers:Tf(),body:JSON.stringify({client_id:e})})),oh=async(e,t,n)=>Ef(await fetch(`${_f}/rate-limit/blacklist`,{method:"POST",headers:Tf(),body:JSON.stringify({client_id:e,reason:t,duration:n})})),ch=async e=>Ef(await fetch(`${_f}/rate-limit/reset/${encodeURIComponent(e)}`,{method:"POST",headers:Tf()})),uh=U.createContext(void 0),dh=({children:e})=>{const[t,n]=U.useState(!1),[a,r]=U.useState(null),[l,i]=U.useState(!0),[s,o]=U.useState(null),[c,u]=U.useState(null),d=Xd();U.useEffect(()=>{(async()=>{if("true"===sessionStorage.getItem("dev_bypass_active"))return r({id:"1",username:"admin",role:"admin",permissions:["admin:*"]}),n(!0),i(!1),void("/"===window.location.pathname&&d("/dashboard",{replace:!0}));try{const t=localStorage.getItem("access_token"),a=localStorage.getItem("refresh_token");if(t){if(await Df())r({id:"1",username:"admin",role:"admin",permissions:["admin:*"]}),n(!0),"/"===window.location.pathname&&d("/dashboard",{replace:!0});else if(a)try{await Rf(),r({id:"1",username:"admin",role:"admin",permissions:["admin:*"]}),n(!0),"/"===window.location.pathname&&d("/dashboard",{replace:!0})}catch(e){localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),n(!1),r(null),sessionStorage.removeItem("dev_bypass_active"),"/"!==window.location.pathname&&d("/",{replace:!0})}else localStorage.removeItem("access_token"),n(!1),r(null),sessionStorage.removeItem("dev_bypass_active"),"/"!==window.location.pathname&&d("/",{replace:!0})}else n(!1),r(null),sessionStorage.removeItem("dev_bypass_active"),"/"!==window.location.pathname&&d("/",{replace:!0})}catch(t){console.error("Error checking authentication status:",t),n(!1),r(null),sessionStorage.removeItem("dev_bypass_active")}finally{i(!1)}})()},[d]),U.useEffect(()=>{t&&"/"===window.location.pathname&&d("/dashboard",{replace:!0})},[t,d]);const f=async()=>{try{const e=await Rf(),t=Date.now()+1e3*e.expires_in;return u(t),!0}catch(e){return console.error("Token refresh error:",e),n(!1),r(null),u(null),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),sessionStorage.removeItem("dev_bypass_active"),d("/"),!1}};return U.useEffect(()=>{if(!t||!c)return;const e=c-Date.now(),n=3e5;if(!(e<=n)){const t=setTimeout(()=>{f()},e-n);return()=>clearTimeout(t)}f()},[t,c]),G.jsx(uh.Provider,{value:{isAuthenticated:t,user:a,isLoading:l,login:async e=>{if(console.log("[AuthContext] login function initiated. Credentials:",e),i(!0),o(null),console.log("[AuthContext] Checking dev bypass condition..."),"admin"===e.username&&"admin"===e.password){return o("DEV_BYPASS_DEBUG_CHECK"+" (dev bypass active)"),window.__DEV_BYPASS_ACTIVE__=!0,sessionStorage.setItem("dev_bypass_active","true"),setTimeout(()=>{const e=document.createElement("div");e.innerText="⚠️ DEV LOGIN BYPASS ACTIVE: admin/admin",e.style.position="fixed",e.style.top="0",e.style.left="0",e.style.right="0",e.style.background="#ffcc00",e.style.color="#000",e.style.padding="8px",e.style.fontWeight="bold",e.style.zIndex="9999",document.body.appendChild(e)},100),console.log("[AuthContext] Dev bypass condition MET. Setting user and will navigate after state update."),r({id:"1",username:"admin",role:"admin",permissions:["admin:*"]}),n(!0),void i(!1)}console.log("[AuthContext] Dev bypass condition NOT MET. Proceeding with API login.");try{const t=await Lf(e),a=Date.now()+1e3*t.expires_in;u(a),r(t.user),n(!0),d("/dashboard")}catch(t){console.error("Login error:",t),o("Invalid username or password"),n(!1),r(null),u(null)}finally{i(!1)}},logout:async()=>{i(!0);try{await If()}catch(e){console.error("Logout error:",e)}finally{n(!1),r(null),u(null),i(!1),sessionStorage.removeItem("dev_bypass_active"),d("/")}},refreshToken:f,error:s,tokenExpiresAt:c},children:e})},fh=()=>{const e=U.useContext(uh);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e},hh=()=>{const{isAuthenticated:e,user:t,logout:n}=fh(),[a,r]=U.useState(!1);U.useEffect(()=>{t&&"admin"===t.role?r(!0):r(!1)},[t]);return G.jsxs("header",{className:"header",children:[G.jsx("div",{className:"logo",children:G.jsx(jf,{to:e?"/dashboard":"/",children:"Database Service"})}),G.jsx("nav",{className:"nav",children:e?G.jsxs(G.Fragment,{children:[G.jsx(jf,{to:"/dashboard",className:"nav-link",children:"Dashboard"}),G.jsx(jf,{to:"/applications",className:"nav-link",children:"Applications"}),G.jsx(jf,{to:"/databases",className:"nav-link",children:"Databases"}),G.jsx(jf,{to:"/query-execution",className:"nav-link",children:"Query Execution"}),G.jsx(jf,{to:"/credentials",className:"nav-link",children:"Credentials"}),G.jsx(jf,{to:"/metrics",className:"nav-link",children:"Metrics"}),G.jsx(jf,{to:"/audit-logs",className:"nav-link",children:"Audit Logs"}),G.jsx(jf,{to:"/rate-limiting",className:"nav-link",children:"Rate Limiting"}),a&&G.jsx(jf,{to:"/settings",className:"nav-link",children:"Settings"}),G.jsx("button",{onClick:async()=>{await n()},className:"logout-btn",children:"Logout"})]}):G.jsx(jf,{to:"/",className:"nav-link",children:"Login"})})]})},ph=()=>{const e=(new Date).getFullYear();return G.jsx("footer",{className:"footer",children:G.jsxs("div",{className:"footer-content",children:[G.jsxs("p",{children:["© ",e," Database Service. All rights reserved."]}),G.jsxs("div",{className:"footer-links",children:[G.jsx(jf,{to:"/privacy",className:"footer-link",children:"Privacy Policy"}),G.jsx(jf,{to:"/terms",className:"footer-link",children:"Terms of Service"}),G.jsx("a",{href:"mailto:<EMAIL>",className:"footer-link",children:"Contact"})]})]})})},mh=()=>{const[e,t]=U.useState(!0);return U.useEffect(()=>{window.__DEV_BYPASS_ACTIVE__||"true"===sessionStorage.getItem("dev_bypass_active")||t(!1)},[]),e?G.jsxs("div",{className:"dev-bypass-banner",children:[G.jsx("span",{children:"⚠️ DEV LOGIN BYPASS ACTIVE: admin/admin"}),G.jsx("button",{className:"close-btn",onClick:()=>t(!1),title:"Close banner",children:"×"})]}):null},vh=()=>G.jsxs("div",{className:"legal-page-container",children:[G.jsx("h1",{children:"Privacy Policy"}),G.jsx("p",{children:"This Privacy Policy describes how your information is collected, used, and protected when you use the Database Service application."}),G.jsx("h2",{children:"Information Collection"}),G.jsx("p",{children:"We collect only the information necessary to provide authentication and database management functionality. No personal data is sold or shared with third parties."}),G.jsx("h2",{children:"Usage of Information"}),G.jsx("p",{children:"Your information is used solely for the purpose of providing secure access to the database service and ensuring system integrity."}),G.jsx("h2",{children:"Data Security"}),G.jsx("p",{children:"We implement industry-standard security measures to protect your data from unauthorized access."}),G.jsx("h2",{children:"Contact"}),G.jsx("p",{children:"If you have any questions about this Privacy Policy, please contact your administrator."})]}),gh=()=>G.jsxs("div",{className:"legal-page-container",children:[G.jsx("h1",{children:"Terms of Service"}),G.jsx("p",{children:"By using the Database Service application, you agree to the following terms and conditions."}),G.jsx("h2",{children:"Acceptable Use"}),G.jsx("p",{children:"You agree to use the application only for authorized and legal purposes. Unauthorized access or misuse is strictly prohibited."}),G.jsx("h2",{children:"Account Responsibility"}),G.jsx("p",{children:"You are responsible for maintaining the confidentiality of your login credentials and for all activities under your account."}),G.jsx("h2",{children:"Changes to Terms"}),G.jsx("p",{children:"We reserve the right to update these Terms of Service at any time. Continued use of the service constitutes acceptance of the updated terms."}),G.jsx("h2",{children:"Contact"}),G.jsx("p",{children:"If you have any questions about these Terms of Service, please contact your administrator."})]}),yh=()=>{const[e,t]=U.useState([]),[n,a]=U.useState(!0),[r,l]=U.useState(null),[i,s]=U.useState(!1),o=async()=>{a(!0),l(null);try{const e=await Mf();t(e.databases||[])}catch(e){e instanceof Error?l(e.message):l("An unknown error occurred")}finally{a(!1),s(!1)}};U.useEffect(()=>{o()},[]);return G.jsxs("div",{className:"db-management-container",children:[G.jsx("h1",{children:"Database Management"}),G.jsxs("div",{className:"db-management-actions",children:[G.jsx("button",{onClick:()=>{s(!0),o()},disabled:i,children:i?"Refreshing...":"Refresh"}),G.jsx("button",{onClick:async()=>{const e=prompt("Enter new database name:");if(!e)return;const t=prompt("Enter database owner (optional):")||void 0;a(!0),l(null);try{await Uf(e,t),await o(),alert(`Database "${e}" created successfully!`)}catch(n){n instanceof Error?l(n.message):l("An unknown error occurred")}finally{a(!1)}},children:"Create Database"})]}),n?G.jsx("div",{className:"db-management-loading",children:"Loading databases..."}):r?G.jsx("div",{className:"db-management-error",children:r}):G.jsxs("table",{className:"db-table",children:[G.jsx("thead",{children:G.jsxs("tr",{children:[G.jsx("th",{children:"Name"}),G.jsx("th",{children:"Owner"}),G.jsx("th",{children:"Size"}),G.jsx("th",{children:"Encoding"}),G.jsx("th",{children:"Collate"}),G.jsx("th",{children:"Ctype"}),G.jsx("th",{children:"Access"}),G.jsx("th",{children:"Actions"})]})}),G.jsx("tbody",{children:e.map(e=>G.jsxs("tr",{children:[G.jsx("td",{children:e.name}),G.jsx("td",{children:e.owner}),G.jsx("td",{children:e.size}),G.jsx("td",{children:e.encoding}),G.jsx("td",{children:e.collate}),G.jsx("td",{children:e.ctype}),G.jsx("td",{children:e.access}),G.jsx("td",{children:G.jsx("button",{className:"drop-btn",onClick:()=>(async e=>{if(window.confirm(`Are you sure you want to drop database '${e}'? This action cannot be undone.`)){a(!0),l(null);try{await $f(e),await o(),alert(`Database "${e}" dropped successfully!`)}catch(t){t instanceof Error?l(t.message):l("An unknown error occurred")}finally{a(!1)}}})(e.name),children:"Drop"})})]},e.name))})]})]})},xh=()=>{const[e,t]=U.useState(""),[n,a]=U.useState(""),{login:r,isLoading:l,error:i}=fh();return G.jsx("div",{className:"login-container",children:G.jsxs("div",{className:"login-card",children:[G.jsxs("div",{className:"login-header",children:[G.jsx("h1",{children:"Database Service"}),G.jsx("p",{children:"Sign in to access your database management dashboard"})]}),G.jsxs("form",{className:"login-form",onSubmit:async t=>{t.preventDefault(),e.trim()&&n.trim()&&await r({username:e,password:n})},children:[i&&G.jsx("div",{className:"error-message",children:i}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"username",children:"Username"}),G.jsx("input",{type:"text",id:"username",value:e,onChange:e=>t(e.target.value),disabled:l})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"password",children:"Password"}),G.jsx("input",{type:"password",id:"password",value:n,onChange:e=>a(e.target.value),disabled:l})]}),G.jsx("button",{type:"submit",className:"login-button",disabled:l,children:l?"Signing in...":"Sign In"})]}),G.jsx("div",{className:"login-footer",children:G.jsx("p",{children:"Forgot your password? Contact your administrator."})})]})})},bh=({title:e,value:t,change:n,icon:a="chart-line",color:r="blue"})=>G.jsxs("div",{className:`metrics-card ${r}`,children:[G.jsx("div",{className:"metrics-icon",children:G.jsx("i",{className:`fas fa-${a}`})}),G.jsxs("div",{className:"metrics-content",children:[G.jsx("h3",{className:"metrics-title",children:e}),G.jsxs("div",{className:"metrics-value-container",children:[G.jsx("p",{className:"metrics-value",children:t}),void 0===n?null:G.jsxs("div",{className:"metrics-change "+(void 0===n?"":n>0?"positive":n<0?"negative":"neutral"),children:[G.jsx("span",{className:"change-symbol",children:void 0===n?"":n>0?"↑":n<0?"↓":"–"}),G.jsxs("span",{className:"change-value",children:[Math.abs(n),"%"]})]})]})]})]}),jh=()=>{const[e,t]=U.useState({activeConnections:0,totalConnections:0,maxConnections:0,totalQueries:0,successfulQueries:0,failedQueries:0,averageResponseTime:0,slowestQueryTime:0,cacheHitRate:0,uptimeSeconds:0,memoryUsageMB:0,cpuUsagePercent:0}),[n,a]=U.useState(null),[r,l]=U.useState([]),[i,s]=U.useState(null),[o,c]=U.useState(!0);return U.useEffect(()=>{(async()=>{c(!0);try{const n=await Ff();a(n);const r=await Hf();t({activeConnections:r.connection_pool.active_connections,totalConnections:r.connection_pool.total_connections,maxConnections:r.connection_pool.max_connections,totalQueries:r.query_performance.total_queries,successfulQueries:r.query_performance.successful_queries,failedQueries:r.query_performance.failed_queries,averageResponseTime:r.query_performance.average_response_time,slowestQueryTime:r.query_performance.slowest_query_time,cacheHitRate:r.cache_performance.cache_hit_rate,uptimeSeconds:r.system_metrics.uptime_seconds,memoryUsageMB:r.system_metrics.memory_usage_mb,cpuUsagePercent:r.system_metrics.cpu_usage_percent});try{const e=await Xf();l(e)}catch(e){console.error("Error fetching applications:",e)}try{const e=await lh();s(e)}catch(e){console.error("Error fetching rate limit stats:",e)}}catch(e){console.error("Error fetching dashboard data:",e)}finally{c(!1)}})()},[]),o?G.jsx("div",{className:"dashboard-loading",children:G.jsx("p",{children:"Loading dashboard data..."})}):G.jsxs("div",{className:"dashboard-container",children:[G.jsx("h1",{className:"dashboard-title",children:"Database Service Dashboard"}),n&&G.jsx("div",{className:"health-status",children:G.jsxs("div",{className:`health-indicator ${n.status.toLowerCase()}`,children:[G.jsx("span",{className:"health-icon",children:"healthy"===n.status?"✅":"⚠️"}),G.jsxs("span",{className:"health-text",children:["System Status: ",n.status," (v",n.version,")"]}),G.jsxs("span",{className:"health-uptime",children:["Uptime: ",Math.floor(n.uptime/3600),"h ",Math.floor(n.uptime%3600/60),"m"]})]})}),G.jsxs("div",{className:"metrics-grid",children:[G.jsx(bh,{title:"Active Connections",value:`${e.activeConnections}/${e.maxConnections}`,change:e.activeConnections/e.maxConnections*100,icon:"plug",color:"blue"}),G.jsx(bh,{title:"Total Queries",value:e.totalQueries.toLocaleString(),change:e.failedQueries>0?-e.failedQueries/e.totalQueries*100:0,icon:"database",color:"green"}),G.jsx(bh,{title:"Avg. Response Time",value:`${e.averageResponseTime.toFixed(2)} ms`,change:e.slowestQueryTime>e.averageResponseTime?-(e.slowestQueryTime-e.averageResponseTime)/e.averageResponseTime*100:0,icon:"clock",color:"purple"}),G.jsx(bh,{title:"Cache Hit Rate",value:`${(100*e.cacheHitRate).toFixed(1)}%`,change:100*e.cacheHitRate,icon:"check-circle",color:"orange"}),G.jsx(bh,{title:"Success Rate",value:`${(e.successfulQueries/e.totalQueries*100).toFixed(1)}%`,change:e.successfulQueries/e.totalQueries*100,icon:"shield-check",color:"green"}),G.jsx(bh,{title:"Memory Usage",value:`${e.memoryUsageMB.toFixed(0)} MB`,change:e.memoryUsageMB>1e3?-10:5,icon:"memory",color:"red"}),G.jsx(bh,{title:"CPU Usage",value:`${e.cpuUsagePercent.toFixed(1)}%`,change:e.cpuUsagePercent>80?-15:2,icon:"cpu",color:"yellow"}),G.jsx(bh,{title:"Failed Queries",value:e.failedQueries.toLocaleString(),change:e.failedQueries>0?-20:0,icon:"alert-triangle",color:"red"})]}),G.jsxs("div",{className:"dashboard-sections",children:[G.jsxs("div",{className:"dashboard-section",children:[G.jsxs("div",{className:"section-header",children:[G.jsx("h2",{children:"Applications"}),G.jsx(jf,{to:"/applications",className:"section-link",children:"View All"})]}),G.jsxs("div",{className:"applications-summary",children:[G.jsxs("div",{className:"summary-stat",children:[G.jsx("span",{className:"stat-number",children:r.length}),G.jsx("span",{className:"stat-label",children:"Total Applications"})]}),G.jsxs("div",{className:"summary-stat",children:[G.jsx("span",{className:"stat-number",children:r.filter(e=>e.active).length}),G.jsx("span",{className:"stat-label",children:"Active Applications"})]}),G.jsxs("div",{className:"summary-stat",children:[G.jsx("span",{className:"stat-number",children:r.filter(e=>!e.active).length}),G.jsx("span",{className:"stat-label",children:"Inactive Applications"})]})]}),r.length>0&&G.jsxs("div",{className:"recent-applications",children:[G.jsx("h3",{children:"Recent Applications"}),r.slice(0,3).map(e=>G.jsxs("div",{className:"app-item",children:[G.jsx("span",{className:"app-name",children:e.name}),G.jsx("span",{className:"app-status "+(e.active?"active":"inactive"),children:e.active?"Active":"Inactive"})]},e.id))]})]}),G.jsxs("div",{className:"dashboard-section",children:[G.jsxs("div",{className:"section-header",children:[G.jsx("h2",{children:"Rate Limiting"}),G.jsx(jf,{to:"/rate-limiting",className:"section-link",children:"View Details"})]}),i?G.jsxs("div",{className:"rate-limit-summary",children:[G.jsxs("div",{className:"summary-stat",children:[G.jsx("span",{className:"stat-number",children:i.totalRequests.toLocaleString()}),G.jsx("span",{className:"stat-label",children:"Total Requests"})]}),G.jsxs("div",{className:"summary-stat",children:[G.jsxs("span",{className:"stat-number",children:[i.successRate.toFixed(1),"%"]}),G.jsx("span",{className:"stat-label",children:"Success Rate"})]}),G.jsxs("div",{className:"summary-stat",children:[G.jsx("span",{className:"stat-number",children:i.activeClients}),G.jsx("span",{className:"stat-label",children:"Active Clients"})]}),G.jsxs("div",{className:"summary-stat",children:[G.jsx("span",{className:"stat-number",children:i.blockedRequests.toLocaleString()}),G.jsx("span",{className:"stat-label",children:"Blocked Requests"})]})]}):G.jsx("p",{children:"Loading rate limiting data..."})]}),G.jsxs("div",{className:"dashboard-section",children:[G.jsx("div",{className:"section-header",children:G.jsx("h2",{children:"Quick Actions"})}),G.jsxs("div",{className:"quick-actions",children:[G.jsxs(jf,{to:"/applications",className:"action-button",children:[G.jsx("span",{className:"action-icon",children:"📱"}),G.jsx("span",{className:"action-text",children:"Manage Applications"})]}),G.jsxs(jf,{to:"/query-execution",className:"action-button",children:[G.jsx("span",{className:"action-icon",children:"⚡"}),G.jsx("span",{className:"action-text",children:"Execute Queries"})]}),G.jsxs(jf,{to:"/audit-logs",className:"action-button",children:[G.jsx("span",{className:"action-icon",children:"📋"}),G.jsx("span",{className:"action-text",children:"View Audit Logs"})]}),G.jsxs(jf,{to:"/databases",className:"action-button",children:[G.jsx("span",{className:"action-icon",children:"🗄️"}),G.jsx("span",{className:"action-text",children:"Manage Databases"})]}),G.jsxs(jf,{to:"/rate-limiting",className:"action-button",children:[G.jsx("span",{className:"action-icon",children:"🛡️"}),G.jsx("span",{className:"action-text",children:"Rate Limiting"})]}),G.jsxs(jf,{to:"/credentials",className:"action-button",children:[G.jsx("span",{className:"action-icon",children:"🔐"}),G.jsx("span",{className:"action-text",children:"Manage Credentials"})]})]})]})]})]})},wh=()=>{const[e,t]=U.useState([]),[n,a]=U.useState(!0),[r,l]=U.useState(null),[i,s]=U.useState(!1),[o,c]=U.useState(null),[u,d]=U.useState(!1),[f,h]=U.useState(null),[p,m]=U.useState({name:"",username:"",password:"",host:"",port:5432,database:""});U.useEffect(()=>{v()},[]);const v=async()=>{a(!0),l(null);try{const e=await Vf();t(e.credentials)}catch(e){l(e.message||"Failed to fetch credentials")}finally{a(!1)}};return G.jsxs("div",{className:"credentials-container",children:[G.jsxs("div",{className:"credentials-header",children:[G.jsx("h1",{children:"Secure Database Credentials"}),G.jsx("p",{children:"Manage encrypted database credentials with AES-256 encryption"}),G.jsx("button",{className:"btn btn-primary",onClick:()=>s(!0),disabled:n,children:"Store New Credential"})]}),r&&G.jsxs("div",{className:"error-message",children:[G.jsx("p",{children:r}),G.jsx("button",{onClick:()=>l(null),children:"×"})]}),n&&G.jsx("div",{className:"loading-indicator",children:G.jsx("p",{children:"Loading..."})}),i&&G.jsx("div",{className:"modal-overlay",children:G.jsxs("div",{className:"modal",children:[G.jsxs("div",{className:"modal-header",children:[G.jsx("h2",{children:"Store New Credential"}),G.jsx("button",{onClick:()=>s(!1),children:"×"})]}),G.jsxs("form",{onSubmit:async e=>{e.preventDefault(),a(!0),l(null);try{const e=await qf(p);await v(),s(!1),m({name:"",username:"",password:"",host:"",port:5432,database:""}),alert(`Credential stored successfully!\nCredential ID: ${e.credential_id}`)}catch(t){l(t.message||"Failed to store credential")}finally{a(!1)}},className:"credential-form",children:[G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"name",children:"Credential Name *"}),G.jsx("input",{type:"text",id:"name",value:p.name,onChange:e=>m({...p,name:e.target.value}),required:!0,placeholder:"Enter a descriptive name"})]}),G.jsxs("div",{className:"form-row",children:[G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"host",children:"Host *"}),G.jsx("input",{type:"text",id:"host",value:p.host,onChange:e=>m({...p,host:e.target.value}),required:!0,placeholder:"localhost"})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"port",children:"Port *"}),G.jsx("input",{type:"number",id:"port",value:p.port,onChange:e=>m({...p,port:parseInt(e.target.value)}),required:!0,min:"1",max:"65535"})]})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"database",children:"Database *"}),G.jsx("input",{type:"text",id:"database",value:p.database,onChange:e=>m({...p,database:e.target.value}),required:!0,placeholder:"Database name"})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"username",children:"Username *"}),G.jsx("input",{type:"text",id:"username",value:p.username,onChange:e=>m({...p,username:e.target.value}),required:!0,placeholder:"Database username"})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"password",children:"Password *"}),G.jsx("input",{type:"password",id:"password",value:p.password,onChange:e=>m({...p,password:e.target.value}),required:!0,placeholder:"Database password"}),G.jsx("small",{className:"form-help",children:"Password will be encrypted with AES-256"})]}),G.jsxs("div",{className:"form-actions",children:[G.jsx("button",{type:"button",onClick:()=>s(!1),children:"Cancel"}),G.jsx("button",{type:"submit",className:"btn btn-primary",disabled:n,children:n?"Storing...":"Store Credential"})]})]})]})}),G.jsx("div",{className:"credentials-grid",children:0===e.length?G.jsxs("div",{className:"no-credentials",children:[G.jsx("p",{children:"No credentials stored yet."}),G.jsx("button",{className:"btn btn-primary",onClick:()=>s(!0),children:"Store Your First Credential"})]}):e.map(e=>G.jsxs("div",{className:"credential-card",children:[G.jsxs("div",{className:"credential-header",children:[G.jsx("h3",{children:e.name}),G.jsxs("div",{className:"credential-info",children:[G.jsx("span",{className:"credential-host",children:e.host}),G.jsx("span",{className:"credential-database",children:e.database})]})]}),G.jsxs("div",{className:"credential-actions",children:[G.jsx("button",{className:"btn btn-info",onClick:()=>(async e=>{a(!0),l(null);try{const t=await Bf(e.id);h(t),c(e),d(!0)}catch(t){l(t.message||"Failed to retrieve credential details")}finally{a(!1)}})(e),disabled:n,children:"View Details"}),G.jsx("button",{className:"btn btn-success",onClick:()=>(async e=>{a(!0),l(null);try{const t=await Wf(e.id);alert(t.success?`Connection test successful: ${t.message}`:`Connection test failed: ${t.message}`)}catch(t){l(t.message||"Failed to test connection")}finally{a(!1)}})(e),disabled:n,children:"Test Connection"}),G.jsx("button",{className:"btn btn-danger",onClick:()=>(async e=>{if(confirm(`Are you sure you want to delete the credential "${e.name}"?`)){a(!0),l(null);try{await Qf(e.id),await v(),alert("Credential deleted successfully")}catch(t){l(t.message||"Failed to delete credential")}finally{a(!1)}}})(e),disabled:n,children:"Delete"})]})]},e.id))}),u&&f&&G.jsx("div",{className:"modal-overlay",children:G.jsxs("div",{className:"modal modal-large",children:[G.jsxs("div",{className:"modal-header",children:[G.jsxs("h2",{children:["Credential Details: ",null==o?void 0:o.name]}),G.jsx("button",{onClick:()=>d(!1),children:"×"})]}),G.jsxs("div",{className:"credential-details",children:[G.jsxs("div",{className:"details-section",children:[G.jsx("h3",{children:"Connection Information"}),G.jsxs("div",{className:"details-grid",children:[G.jsxs("div",{children:[G.jsx("strong",{children:"Name:"})," ",f.name]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Host:"})," ",f.host]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Port:"})," ",f.port]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Database:"})," ",f.database]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Username:"})," ",f.username]})]})]}),G.jsxs("div",{className:"details-section",children:[G.jsx("h3",{children:"Security Information"}),G.jsxs("div",{className:"security-info",children:[G.jsx("p",{children:"🔒 Password is encrypted with AES-256 encryption"}),G.jsxs("p",{children:["🔑 Credential ID: ",G.jsx("code",{children:f.credential_id})]})]})]}),G.jsxs("div",{className:"details-section",children:[G.jsx("h3",{children:"Connection String Preview"}),G.jsx("div",{className:"connection-string",children:G.jsxs("code",{children:["postgresql://",f.username,":***@",f.host,":",f.port,"/",f.database]})})]})]})]})})]})},Sh=()=>{const[e,t]=U.useState(null),[n,a]=U.useState(null),[r,l]=U.useState(null),[i,s]=U.useState(null),[o,c]=U.useState(!0),[u,d]=U.useState(null),[f,h]=U.useState(null),p=async()=>{try{d(null);const[e,n,r,i]=await Promise.all([Hf(),Kf(),Yf(),Ff()]);t(e),a(n),l(r),s(i)}catch(e){d(e.message||"Failed to fetch metrics"),console.error("Error fetching metrics:",e)}finally{c(!1)}};U.useEffect(()=>{p();const e=setInterval(p,3e4);return h(e),()=>{e&&clearInterval(e)}},[]);return o&&!e?G.jsx("div",{className:"metrics-loading",children:G.jsx("p",{children:"Loading comprehensive metrics data..."})}):G.jsxs("div",{className:"metrics-container",children:[G.jsxs("div",{className:"metrics-header",children:[G.jsx("h1",{children:"Database Service Metrics"}),G.jsxs("div",{className:"metrics-controls",children:[G.jsx("button",{className:"refresh-button",onClick:()=>{c(!0),p()},disabled:o,children:o?"Refreshing...":"🔄 Refresh"}),G.jsxs("div",{className:"auto-refresh-indicator",children:[G.jsx("span",{className:"refresh-dot"}),"Auto-refresh: 30s"]})]})]}),u&&G.jsxs("div",{className:"error-message",children:[G.jsxs("p",{children:["Error loading metrics: ",u]}),G.jsx("button",{onClick:()=>d(null),children:"×"})]}),i&&G.jsx("div",{className:"health-status-section",children:G.jsxs("div",{className:`health-indicator ${i.status.toLowerCase()}`,children:[G.jsx("span",{className:"health-icon",children:"healthy"===i.status?"✅":"⚠️"}),G.jsxs("span",{className:"health-text",children:["System Status: ",i.status," (v",i.version,")"]}),G.jsxs("span",{className:"health-uptime",children:["Uptime: ",Math.floor(i.uptime/3600),"h ",Math.floor(i.uptime%3600/60),"m"]})]})}),e&&G.jsxs("div",{className:"metrics-summary",children:[G.jsx("h2",{children:"Performance Overview"}),G.jsxs("div",{className:"metrics-grid",children:[G.jsx(bh,{title:"Active Connections",value:`${e.connection_pool.active_connections}/${e.connection_pool.max_connections}`,change:e.connection_pool.active_connections/e.connection_pool.max_connections*100,icon:"plug",color:"blue"}),G.jsx(bh,{title:"Total Queries",value:e.query_performance.total_queries.toLocaleString(),change:e.query_performance.failed_queries>0?-e.query_performance.failed_queries/e.query_performance.total_queries*100:0,icon:"database",color:"green"}),G.jsx(bh,{title:"Avg. Response Time",value:`${e.query_performance.average_response_time.toFixed(2)} ms`,change:e.query_performance.slowest_query_time>e.query_performance.average_response_time?-(e.query_performance.slowest_query_time-e.query_performance.average_response_time)/e.query_performance.average_response_time*100:0,icon:"clock",color:"purple"}),G.jsx(bh,{title:"Cache Hit Rate",value:`${(100*e.cache_performance.cache_hit_rate).toFixed(1)}%`,change:100*e.cache_performance.cache_hit_rate,icon:"check-circle",color:"orange"}),G.jsx(bh,{title:"Success Rate",value:`${(e.query_performance.successful_queries/e.query_performance.total_queries*100).toFixed(1)}%`,change:e.query_performance.successful_queries/e.query_performance.total_queries*100,icon:"shield-check",color:"green"}),G.jsx(bh,{title:"Memory Usage",value:`${e.system_metrics.memory_usage_mb.toFixed(0)} MB`,change:e.system_metrics.memory_usage_mb>1e3?-10:5,icon:"memory",color:"red"}),G.jsx(bh,{title:"CPU Usage",value:`${e.system_metrics.cpu_usage_percent.toFixed(1)}%`,change:e.system_metrics.cpu_usage_percent>80?-15:2,icon:"cpu",color:"yellow"}),G.jsx(bh,{title:"Failed Queries",value:e.query_performance.failed_queries.toLocaleString(),change:e.query_performance.failed_queries>0?-20:0,icon:"alert-triangle",color:"red"})]})]}),G.jsx("div",{className:"metrics-details",children:e&&G.jsxs(G.Fragment,{children:[G.jsxs("div",{className:"metrics-section",children:[G.jsx("h2",{children:"Connection Pool Statistics"}),G.jsx("table",{className:"metrics-table",children:G.jsxs("tbody",{children:[G.jsxs("tr",{children:[G.jsx("td",{children:"Active Connections"}),G.jsx("td",{children:e.connection_pool.active_connections})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Idle Connections"}),G.jsx("td",{children:e.connection_pool.idle_connections})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Total Connections"}),G.jsx("td",{children:e.connection_pool.total_connections})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Max Connections"}),G.jsx("td",{children:e.connection_pool.max_connections})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Pool Utilization"}),G.jsxs("td",{children:[(e.connection_pool.active_connections/e.connection_pool.max_connections*100).toFixed(1),"%"]})]})]})})]}),G.jsxs("div",{className:"metrics-section",children:[G.jsx("h2",{children:"Query Performance"}),G.jsx("table",{className:"metrics-table",children:G.jsxs("tbody",{children:[G.jsxs("tr",{children:[G.jsx("td",{children:"Total Queries"}),G.jsx("td",{children:e.query_performance.total_queries.toLocaleString()})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Successful Queries"}),G.jsx("td",{children:e.query_performance.successful_queries.toLocaleString()})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Failed Queries"}),G.jsx("td",{children:e.query_performance.failed_queries.toLocaleString()})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Success Rate"}),G.jsxs("td",{children:[(e.query_performance.successful_queries/e.query_performance.total_queries*100).toFixed(2),"%"]})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Average Response Time"}),G.jsxs("td",{children:[e.query_performance.average_response_time.toFixed(2)," ms"]})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Slowest Query Time"}),G.jsxs("td",{children:[e.query_performance.slowest_query_time.toFixed(2)," ms"]})]}),(null==r?void 0:r.queries_per_second)&&G.jsxs("tr",{children:[G.jsx("td",{children:"Queries per Second"}),G.jsx("td",{children:r.queries_per_second.toFixed(2)})]})]})})]}),G.jsxs("div",{className:"metrics-section",children:[G.jsx("h2",{children:"Cache Performance"}),G.jsx("table",{className:"metrics-table",children:G.jsxs("tbody",{children:[G.jsxs("tr",{children:[G.jsx("td",{children:"Cache Hits"}),G.jsx("td",{children:e.cache_performance.cache_hits.toLocaleString()})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Cache Misses"}),G.jsx("td",{children:e.cache_performance.cache_misses.toLocaleString()})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Cache Hit Rate"}),G.jsxs("td",{children:[(100*e.cache_performance.cache_hit_rate).toFixed(2),"%"]})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Total Cache Requests"}),G.jsx("td",{children:(e.cache_performance.cache_hits+e.cache_performance.cache_misses).toLocaleString()})]})]})})]}),G.jsxs("div",{className:"metrics-section",children:[G.jsx("h2",{children:"System Resources"}),G.jsx("table",{className:"metrics-table",children:G.jsxs("tbody",{children:[G.jsxs("tr",{children:[G.jsx("td",{children:"Uptime"}),G.jsxs("td",{children:[Math.floor(e.system_metrics.uptime_seconds/3600),"h ",Math.floor(e.system_metrics.uptime_seconds%3600/60),"m"]})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Memory Usage"}),G.jsxs("td",{children:[e.system_metrics.memory_usage_mb.toFixed(2)," MB"]})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"CPU Usage"}),G.jsxs("td",{children:[e.system_metrics.cpu_usage_percent.toFixed(2),"%"]})]}),i&&G.jsxs(G.Fragment,{children:[G.jsxs("tr",{children:[G.jsx("td",{children:"Service Version"}),G.jsx("td",{children:i.version})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Database Connected"}),G.jsx("td",{children:i.database.connected?"✅ Yes":"❌ No"})]})]})]})})]})]})})]})},kh=()=>{const[e,t]=U.useState(null),[n,a]=U.useState(!1),[r,l]=U.useState(""),[i,s]=U.useState(null);U.useEffect(()=>{(async()=>{a(!0),s(null);try{const e=await Gf();t(e)}catch(e){e instanceof Error?s(e.message):s("Failed to load settings.")}finally{a(!1)}})()},[]);const o=e=>{const{name:n,value:a,type:r}=e.target;t(t=>{if(!t)return t;let l;return l="checkbox"===r?e.target.checked:"number"===r?""===a?"":Number(a):a,{...t,[n]:l}})};return n&&!e?G.jsx("div",{className:"settings-loading",children:G.jsx("p",{children:"Loading settings..."})}):i?G.jsx("div",{className:"settings-error",children:G.jsx("p",{children:i})}):e?G.jsxs("div",{className:"settings-container",children:[G.jsx("h1",{className:"settings-title",children:"Database Service Settings"}),G.jsxs("form",{className:"settings-form",onSubmit:async t=>{t.preventDefault(),a(!0),l(""),s(null);try{if(!e)throw new Error("Settings not loaded");await Jf(e),l("Settings saved successfully!"),setTimeout(()=>{l("")},3e3)}catch(n){s("Error saving settings. Please try again.")}finally{a(!1)}},children:[G.jsxs("div",{className:"settings-section",children:[G.jsx("h2",{children:"Connection Settings"}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"connectionTimeout",children:"Connection Timeout (seconds)"}),G.jsx("input",{type:"number",id:"connectionTimeout",name:"connectionTimeout",value:e.connectionTimeout,onChange:o,min:"5",max:"300"})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"maxConnections",children:"Maximum Connections"}),G.jsx("input",{type:"number",id:"maxConnections",name:"maxConnections",value:e.maxConnections,onChange:o,min:"10",max:"1000"})]})]}),G.jsxs("div",{className:"settings-section",children:[G.jsx("h2",{children:"Logging Settings"}),G.jsxs("div",{className:"form-group checkbox-group",children:[G.jsx("input",{type:"checkbox",id:"enableLogging",name:"enableLogging",checked:e.enableLogging,onChange:o}),G.jsx("label",{htmlFor:"enableLogging",children:"Enable Logging"})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"logLevel",children:"Log Level"}),G.jsxs("select",{id:"logLevel",name:"logLevel",value:e.logLevel,onChange:o,disabled:!e.enableLogging,children:[G.jsx("option",{value:"debug",children:"Debug"}),G.jsx("option",{value:"info",children:"Info"}),G.jsx("option",{value:"warning",children:"Warning"}),G.jsx("option",{value:"error",children:"Error"})]})]})]}),G.jsxs("div",{className:"settings-section",children:[G.jsx("h2",{children:"Backup Settings"}),G.jsxs("div",{className:"form-group checkbox-group",children:[G.jsx("input",{type:"checkbox",id:"backupEnabled",name:"backupEnabled",checked:e.backupEnabled,onChange:o}),G.jsx("label",{htmlFor:"backupEnabled",children:"Enable Automatic Backups"})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"backupFrequency",children:"Backup Frequency"}),G.jsxs("select",{id:"backupFrequency",name:"backupFrequency",value:e.backupFrequency,onChange:o,disabled:!e.backupEnabled,children:[G.jsx("option",{value:"hourly",children:"Hourly"}),G.jsx("option",{value:"daily",children:"Daily"}),G.jsx("option",{value:"weekly",children:"Weekly"}),G.jsx("option",{value:"monthly",children:"Monthly"})]})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"backupLocation",children:"Backup Location"}),G.jsx("input",{type:"text",id:"backupLocation",name:"backupLocation",value:e.backupLocation,onChange:o,disabled:!e.backupEnabled})]})]}),G.jsxs("div",{className:"settings-section",children:[G.jsx("h2",{children:"Notification Settings"}),G.jsxs("div",{className:"form-group checkbox-group",children:[G.jsx("input",{type:"checkbox",id:"notificationsEnabled",name:"notificationsEnabled",checked:e.notificationsEnabled,onChange:o}),G.jsx("label",{htmlFor:"notificationsEnabled",children:"Enable Notifications"})]}),G.jsxs("div",{className:"form-group checkbox-group",children:[G.jsx("input",{type:"checkbox",id:"emailNotifications",name:"emailNotifications",checked:e.emailNotifications,onChange:o,disabled:!e.notificationsEnabled}),G.jsx("label",{htmlFor:"emailNotifications",children:"Email Notifications"})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"emailAddress",children:"Email Address"}),G.jsx("input",{type:"email",id:"emailAddress",name:"emailAddress",value:e.emailAddress,onChange:o,disabled:!e.notificationsEnabled||!e.emailNotifications,placeholder:"<EMAIL>"})]})]}),G.jsxs("div",{className:"settings-actions",children:[G.jsx("button",{type:"submit",className:"save-button",disabled:n,children:n?"Saving...":"Save Settings"}),r&&G.jsx("div",{className:"save-message "+(r.includes("Error")?"error":"success"),children:r})]})]})]}):null},Nh=()=>{const[e,t]=U.useState([]),[n,a]=U.useState(!0),[r,l]=U.useState(null),[i,s]=U.useState(!1),[o,c]=U.useState(null),[u,d]=U.useState({}),[f,h]=U.useState({name:"",description:"",metadata:{}});U.useEffect(()=>{p()},[]);const p=async()=>{a(!0),l(null);try{const e=await Xf();t(e)}catch(e){l(e.message||"Failed to fetch applications")}finally{a(!1)}};return n?G.jsx("div",{className:"applications-loading",children:G.jsx("p",{children:"Loading applications..."})}):G.jsxs("div",{className:"applications-container",children:[G.jsxs("div",{className:"applications-header",children:[G.jsx("h1",{children:"Application Management"}),G.jsx("p",{children:"Manage client applications and their API keys"}),G.jsx("button",{className:"btn btn-primary",onClick:()=>s(!0),children:"Register New Application"})]}),r&&G.jsxs("div",{className:"error-message",children:[G.jsx("p",{children:r}),G.jsx("button",{onClick:()=>l(null),children:"×"})]}),i&&G.jsx("div",{className:"modal-overlay",children:G.jsxs("div",{className:"modal",children:[G.jsxs("div",{className:"modal-header",children:[G.jsx("h2",{children:"Register New Application"}),G.jsx("button",{onClick:()=>s(!1),children:"×"})]}),G.jsxs("form",{onSubmit:async e=>{e.preventDefault(),l(null);try{const e=await Zf(f);await p(),s(!1),h({name:"",description:"",metadata:{}}),alert(`Application created successfully!\nAPI Key: ${e.api_key}\n\nPlease save this API key securely - it won't be shown again.`)}catch(t){l(t.message||"Failed to create application")}},className:"application-form",children:[G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"name",children:"Application Name *"}),G.jsx("input",{type:"text",id:"name",value:f.name,onChange:e=>h({...f,name:e.target.value}),required:!0,placeholder:"Enter application name"})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"description",children:"Description *"}),G.jsx("textarea",{id:"description",value:f.description,onChange:e=>h({...f,description:e.target.value}),required:!0,placeholder:"Enter application description",rows:3})]}),G.jsxs("div",{className:"form-actions",children:[G.jsx("button",{type:"button",onClick:()=>s(!1),children:"Cancel"}),G.jsx("button",{type:"submit",className:"btn btn-primary",children:"Register Application"})]})]})]})}),G.jsx("div",{className:"applications-grid",children:0===e.length?G.jsxs("div",{className:"no-applications",children:[G.jsx("p",{children:"No applications registered yet."}),G.jsx("button",{className:"btn btn-primary",onClick:()=>s(!0),children:"Register Your First Application"})]}):e.map(e=>{return G.jsxs("div",{className:"application-card "+(e.active?"":"inactive"),children:[G.jsxs("div",{className:"application-header",children:[G.jsx("h3",{children:e.name}),G.jsx("div",{className:"application-status",children:G.jsx("span",{className:"status-badge "+(e.active?"active":"inactive"),children:e.active?"Active":"Inactive"})})]}),G.jsxs("div",{className:"application-details",children:[G.jsx("p",{className:"description",children:e.description}),G.jsxs("div",{className:"application-info",children:[G.jsxs("div",{className:"info-item",children:[G.jsx("label",{children:"Application ID:"}),G.jsx("span",{children:e.id})]}),G.jsxs("div",{className:"info-item",children:[G.jsx("label",{children:"API Key:"}),G.jsxs("div",{className:"api-key-container",children:[G.jsx("span",{className:"api-key",children:u[e.id]?e.apiKey:(t=e.apiKey,t.length<=8?t:t.substring(0,4)+"..."+t.substring(t.length-4))}),G.jsx("button",{className:"btn-icon",onClick:()=>{return t=e.id,void d(e=>({...e,[t]:!e[t]}));var t},title:u[e.id]?"Hide API Key":"Show API Key",children:u[e.id]?"👁️":"👁️‍🗨️"}),G.jsx("button",{className:"btn-icon",onClick:()=>navigator.clipboard.writeText(e.apiKey),title:"Copy API Key",children:"📋"})]})]}),G.jsxs("div",{className:"info-item",children:[G.jsx("label",{children:"Created:"}),G.jsx("span",{children:new Date(e.createdAt).toLocaleDateString()})]}),G.jsxs("div",{className:"info-item",children:[G.jsx("label",{children:"Last Updated:"}),G.jsx("span",{children:new Date(e.updatedAt).toLocaleDateString()})]})]})]}),G.jsxs("div",{className:"application-actions",children:[G.jsx("button",{className:"btn "+(e.active?"btn-warning":"btn-success"),onClick:()=>(async e=>{try{e.active?await th(e.id):await eh(e.id),await p()}catch(t){l(t.message||"Failed to update application status")}})(e),children:e.active?"Deactivate":"Activate"}),G.jsx("button",{className:"btn btn-secondary",onClick:()=>(async e=>{if(confirm("Are you sure you want to generate a new API key? The old key will be invalidated."))try{const t=await nh(e.id);alert(`New API Key generated:\n${t.api_key}\n\nPlease save this API key securely - it won't be shown again.`),await p()}catch(t){l(t.message||"Failed to generate new API key")}})(e),children:"Generate New API Key"}),G.jsx("button",{className:"btn btn-info",onClick:()=>c(e),children:"View Details"})]})]},e.id);var t})}),o&&G.jsx("div",{className:"modal-overlay",children:G.jsxs("div",{className:"modal modal-large",children:[G.jsxs("div",{className:"modal-header",children:[G.jsxs("h2",{children:["Application Details: ",o.name]}),G.jsx("button",{onClick:()=>c(null),children:"×"})]}),G.jsxs("div",{className:"application-details-modal",children:[G.jsxs("div",{className:"details-section",children:[G.jsx("h3",{children:"Basic Information"}),G.jsxs("div",{className:"details-grid",children:[G.jsxs("div",{children:[G.jsx("strong",{children:"ID:"})," ",o.id]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Name:"})," ",o.name]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Status:"})," ",o.active?"Active":"Inactive"]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Created:"})," ",new Date(o.createdAt).toLocaleString()]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Updated:"})," ",new Date(o.updatedAt).toLocaleString()]})]})]}),G.jsxs("div",{className:"details-section",children:[G.jsx("h3",{children:"Description"}),G.jsx("p",{children:o.description})]}),G.jsxs("div",{className:"details-section",children:[G.jsx("h3",{children:"API Key"}),G.jsxs("div",{className:"api-key-display",children:[G.jsx("code",{children:o.apiKey}),G.jsx("button",{className:"btn btn-small",onClick:()=>navigator.clipboard.writeText(o.apiKey),children:"Copy"})]})]}),o.metadata&&Object.keys(o.metadata).length>0&&G.jsxs("div",{className:"details-section",children:[G.jsx("h3",{children:"Metadata"}),G.jsx("pre",{className:"metadata-display",children:JSON.stringify(o.metadata,null,2)})]})]})]})})]})},_h=()=>{const[e,t]=U.useState([]),[n,a]=U.useState(!0),[r,l]=U.useState(null),[i,s]=U.useState(0),[o,c]=U.useState(1),[u]=U.useState(50),[d,f]=U.useState(null),[h,p]=U.useState({limit:u,offset:0}),[m,v]=U.useState({fromDate:"",toDate:""});U.useEffect(()=>{g()},[h]);const g=async()=>{a(!0),l(null);try{const e=await ah(h);t(e.audit_entries),s(e.total_count)}catch(e){l(e.message||"Failed to fetch audit logs")}finally{a(!1)}},y=(e,t)=>{p(n=>({...n,[e]:t,offset:0})),c(1)},x=(e,t)=>{v(n=>({...n,[e]:t})),y("fromDate"===e?"fromTime":"toTime",t?new Date(t).toISOString():void 0)},b=e=>{c(e),p(t=>({...t,offset:(e-1)*u}))},j=e=>{switch(e.toLowerCase()){case"user_login":case"application_registered":return"#28a745";case"user_logout":default:return"#6c757d";case"database_query":return"#007bff";case"database_execute":return"#17a2b8";case"security_violation":return"#dc3545";case"rate_limit_exceeded":return"#ffc107";case"api_key_generated":return"#fd7e14"}},w=e=>new Date(e).toLocaleString(),S=Math.ceil(i/u);return n&&0===e.length?G.jsx("div",{className:"audit-loading",children:G.jsx("p",{children:"Loading audit logs..."})}):G.jsxs("div",{className:"audit-container",children:[G.jsxs("div",{className:"audit-header",children:[G.jsx("h1",{children:"Audit Logs"}),G.jsx("p",{children:"Security and compliance audit trail"}),G.jsx("div",{className:"header-actions",children:G.jsx("button",{className:"btn btn-secondary",onClick:async()=>{try{const e=await rh(h),t=window.URL.createObjectURL(e),n=document.createElement("a");n.href=t,n.download=`audit-logs-${(new Date).toISOString().split("T")[0]}.csv`,document.body.appendChild(n),n.click(),window.URL.revokeObjectURL(t),document.body.removeChild(n)}catch(e){l(e.message||"Failed to export audit logs")}},children:"Export Logs"})})]}),r&&G.jsxs("div",{className:"error-message",children:[G.jsx("p",{children:r}),G.jsx("button",{onClick:()=>l(null),children:"×"})]}),G.jsx("div",{className:"audit-filters",children:G.jsxs("div",{className:"filters-row",children:[G.jsxs("div",{className:"filter-group",children:[G.jsx("label",{children:"From Date:"}),G.jsx("input",{type:"datetime-local",value:m.fromDate,onChange:e=>x("fromDate",e.target.value)})]}),G.jsxs("div",{className:"filter-group",children:[G.jsx("label",{children:"To Date:"}),G.jsx("input",{type:"datetime-local",value:m.toDate,onChange:e=>x("toDate",e.target.value)})]}),G.jsxs("div",{className:"filter-group",children:[G.jsx("label",{children:"Event Type:"}),G.jsxs("select",{value:h.eventType||"",onChange:e=>y("eventType",e.target.value||void 0),children:[G.jsx("option",{value:"",children:"All Events"}),G.jsx("option",{value:"USER_LOGIN",children:"User Login"}),G.jsx("option",{value:"USER_LOGOUT",children:"User Logout"}),G.jsx("option",{value:"DATABASE_QUERY",children:"Database Query"}),G.jsx("option",{value:"DATABASE_EXECUTE",children:"Database Execute"}),G.jsx("option",{value:"SECURITY_VIOLATION",children:"Security Violation"}),G.jsx("option",{value:"RATE_LIMIT_EXCEEDED",children:"Rate Limit Exceeded"}),G.jsx("option",{value:"APPLICATION_REGISTERED",children:"Application Registered"}),G.jsx("option",{value:"API_KEY_GENERATED",children:"API Key Generated"})]})]}),G.jsxs("div",{className:"filter-group",children:[G.jsx("label",{children:"Success:"}),G.jsxs("select",{value:void 0===h.success?"":h.success.toString(),onChange:e=>y("success",""===e.target.value?void 0:"true"===e.target.value),children:[G.jsx("option",{value:"",children:"All"}),G.jsx("option",{value:"true",children:"Success"}),G.jsx("option",{value:"false",children:"Failed"})]})]}),G.jsxs("div",{className:"filter-actions",children:[G.jsx("button",{className:"btn btn-primary",onClick:g,children:"Apply Filters"}),G.jsx("button",{className:"btn btn-secondary",onClick:()=>{p({limit:u,offset:0}),v({fromDate:"",toDate:""}),c(1)},children:"Clear"})]})]})}),G.jsxs("div",{className:"audit-summary",children:[G.jsxs("p",{children:["Showing ",e.length," of ",i," entries"]}),n&&G.jsx("span",{className:"loading-indicator",children:"Loading..."})]}),G.jsx("div",{className:"audit-table-container",children:G.jsxs("table",{className:"audit-table",children:[G.jsx("thead",{children:G.jsxs("tr",{children:[G.jsx("th",{children:"Timestamp"}),G.jsx("th",{children:"Event Type"}),G.jsx("th",{children:"User/App"}),G.jsx("th",{children:"Action"}),G.jsx("th",{children:"Resource"}),G.jsx("th",{children:"Status"}),G.jsx("th",{children:"IP Address"}),G.jsx("th",{children:"Actions"})]})}),G.jsx("tbody",{children:e.map(e=>G.jsxs("tr",{className:e.success?"":"failed-entry",children:[G.jsx("td",{className:"timestamp",children:w(e.timestamp)}),G.jsx("td",{children:G.jsx("span",{className:"event-type-badge",style:{backgroundColor:j(e.eventType)},children:e.eventType})}),G.jsxs("td",{children:[e.userId>0?`User ${e.userId}`:"",e.applicationId>0?`App ${e.applicationId}`:"",0===e.userId&&0===e.applicationId?"System":""]}),G.jsx("td",{className:"action",children:e.action}),G.jsx("td",{className:"resource",children:e.resource}),G.jsx("td",{children:G.jsx("span",{className:"status-badge "+(e.success?"success":"failed"),children:e.success?"Success":"Failed"})}),G.jsx("td",{className:"ip-address",children:e.ipAddress||"-"}),G.jsx("td",{children:G.jsx("button",{className:"btn btn-small btn-info",onClick:()=>f(e),children:"Details"})})]},e.id))})]})}),S>1&&G.jsxs("div",{className:"pagination",children:[G.jsx("button",{className:"btn btn-secondary",disabled:1===o,onClick:()=>b(o-1),children:"Previous"}),G.jsxs("span",{className:"page-info",children:["Page ",o," of ",S]}),G.jsx("button",{className:"btn btn-secondary",disabled:o===S,onClick:()=>b(o+1),children:"Next"})]}),d&&G.jsx("div",{className:"modal-overlay",children:G.jsxs("div",{className:"modal modal-large",children:[G.jsxs("div",{className:"modal-header",children:[G.jsx("h2",{children:"Audit Entry Details"}),G.jsx("button",{onClick:()=>f(null),children:"×"})]}),G.jsxs("div",{className:"audit-details",children:[G.jsxs("div",{className:"details-section",children:[G.jsx("h3",{children:"Basic Information"}),G.jsxs("div",{className:"details-grid",children:[G.jsxs("div",{children:[G.jsx("strong",{children:"ID:"})," ",d.id]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Timestamp:"})," ",w(d.timestamp)]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Event Type:"})," ",d.eventType]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Action:"})," ",d.action]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Resource:"})," ",d.resource]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Status:"})," ",d.success?"Success":"Failed"]})]})]}),G.jsxs("div",{className:"details-section",children:[G.jsx("h3",{children:"User/Application Information"}),G.jsxs("div",{className:"details-grid",children:[G.jsxs("div",{children:[G.jsx("strong",{children:"User ID:"})," ",d.userId||"N/A"]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Application ID:"})," ",d.applicationId||"N/A"]}),G.jsxs("div",{children:[G.jsx("strong",{children:"IP Address:"})," ",d.ipAddress||"N/A"]}),G.jsxs("div",{children:[G.jsx("strong",{children:"User Agent:"})," ",d.userAgent||"N/A"]})]})]}),d.errorMessage&&G.jsxs("div",{className:"details-section",children:[G.jsx("h3",{children:"Error Information"}),G.jsx("div",{className:"error-details",children:d.errorMessage})]}),d.details&&Object.keys(d.details).length>0&&G.jsxs("div",{className:"details-section",children:[G.jsx("h3",{children:"Additional Details"}),G.jsx("pre",{className:"details-json",children:JSON.stringify(d.details,null,2)})]})]})]})})]})},Ch=()=>{const[e,t]=U.useState(null),[n,a]=U.useState(!0),[r,l]=U.useState(null),[i,s]=U.useState(""),[o,c]=U.useState(null),[u,d]=U.useState(!1),[f,h]=U.useState(!1),[p,m]=U.useState(""),[v,g]=U.useState(""),[y,x]=U.useState(""),[b,j]=U.useState("");U.useEffect(()=>{w();const e=setInterval(w,3e4);return()=>clearInterval(e)},[]);const w=async()=>{a(!0),l(null);try{const e=await lh();t(e)}catch(e){l(e.message||"Failed to fetch rate limiting stats")}finally{a(!1)}},S=async()=>{if(i.trim()){l(null);try{const e=await ih(i.trim());c(e)}catch(e){l(e.message||"Failed to fetch client stats"),c(null)}}else l("Please enter a client ID")};return n&&!e?G.jsx("div",{className:"rate-limit-loading",children:G.jsx("p",{children:"Loading rate limiting data..."})}):G.jsxs("div",{className:"rate-limit-container",children:[G.jsxs("div",{className:"rate-limit-header",children:[G.jsx("h1",{children:"Rate Limiting Management"}),G.jsx("p",{children:"Monitor and manage API rate limiting and client access control"}),G.jsx("div",{className:"header-actions",children:G.jsx("button",{className:"btn btn-primary",onClick:w,children:"Refresh Stats"})})]}),r&&G.jsxs("div",{className:"error-message",children:[G.jsx("p",{children:r}),G.jsx("button",{onClick:()=>l(null),children:"×"})]}),e&&G.jsxs("div",{className:"stats-grid",children:[G.jsxs("div",{className:"stat-card",children:[G.jsx("h3",{children:"Total Requests"}),G.jsx("div",{className:"stat-value",children:e.totalRequests.toLocaleString()}),G.jsx("div",{className:"stat-label",children:"All time"})]}),G.jsxs("div",{className:"stat-card",children:[G.jsx("h3",{children:"Allowed Requests"}),G.jsx("div",{className:"stat-value",children:e.allowedRequests.toLocaleString()}),G.jsx("div",{className:"stat-label",children:"Passed rate limits"})]}),G.jsxs("div",{className:"stat-card",children:[G.jsx("h3",{children:"Blocked Requests"}),G.jsx("div",{className:"stat-value",children:e.blockedRequests.toLocaleString()}),G.jsx("div",{className:"stat-label",children:"Rate limited"})]}),G.jsxs("div",{className:"stat-card",children:[G.jsx("h3",{children:"Success Rate"}),G.jsxs("div",{className:"stat-value",children:[e.successRate.toFixed(1),"%"]}),G.jsx("div",{className:"stat-label",children:"Allowed vs total"})]}),G.jsxs("div",{className:"stat-card",children:[G.jsx("h3",{children:"Active Clients"}),G.jsx("div",{className:"stat-value",children:e.activeClients}),G.jsx("div",{className:"stat-label",children:"Currently active"})]}),G.jsxs("div",{className:"stat-card",children:[G.jsx("h3",{children:"Service Uptime"}),G.jsx("div",{className:"stat-value",children:(e=>{const t=Math.floor(e/86400),n=Math.floor(e%86400/3600),a=Math.floor(e%3600/60);return t>0?`${t}d ${n}h ${a}m`:n>0?`${n}h ${a}m`:`${a}m`})(e.uptimeSeconds)}),G.jsx("div",{className:"stat-label",children:"Since last restart"})]})]}),G.jsxs("div",{className:"management-sections",children:[G.jsxs("div",{className:"section",children:[G.jsx("h2",{children:"Client Statistics"}),G.jsxs("div",{className:"client-lookup",children:[G.jsxs("div",{className:"lookup-form",children:[G.jsx("input",{type:"text",placeholder:"Enter client ID (IP address or API key)",value:i,onChange:e=>s(e.target.value),onKeyPress:e=>"Enter"===e.key&&S()}),G.jsx("button",{className:"btn btn-primary",onClick:S,children:"Lookup Client"}),G.jsx("button",{className:"btn btn-warning",onClick:async()=>{if(i.trim()){if(confirm(`Are you sure you want to reset rate limits for ${i}?`))try{await ch(i.trim()),await S(),alert("Client rate limits reset successfully")}catch(e){l(e.message||"Failed to reset client limits")}}else l("Please enter a client ID")},children:"Reset Limits"})]}),o&&G.jsxs("div",{className:"client-stats",children:[G.jsxs("h3",{children:["Client: ",i]}),G.jsxs("div",{className:"client-stats-grid",children:[G.jsxs("div",{children:[G.jsx("strong",{children:"Total Requests:"})," ",o.total_requests||0]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Allowed:"})," ",o.allowed_requests||0]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Blocked:"})," ",o.blocked_requests||0]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Last Request:"})," ",o.last_request_time||"Never"]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Current Window:"})," ",o.current_window_requests||0]}),G.jsxs("div",{children:[G.jsx("strong",{children:"Window Resets:"})," ",o.window_reset_time||"N/A"]})]})]})]})]}),G.jsxs("div",{className:"section",children:[G.jsx("h2",{children:"Whitelist Management"}),G.jsxs("div",{className:"list-management",children:[G.jsxs("div",{className:"list-header",children:[G.jsx("p",{children:"Whitelisted clients bypass all rate limiting"}),G.jsx("button",{className:"btn btn-success",onClick:()=>d(!0),children:"Add to Whitelist"})]}),e&&G.jsxs("div",{className:"list-stats",children:[G.jsxs("p",{children:["Current whitelist size: ",e.whitelistSize]}),G.jsxs("p",{children:["Whitelist hits: ",e.whitelistHits.toLocaleString()]})]})]})]}),G.jsxs("div",{className:"section",children:[G.jsx("h2",{children:"Blacklist Management"}),G.jsxs("div",{className:"list-management",children:[G.jsxs("div",{className:"list-header",children:[G.jsx("p",{children:"Blacklisted clients are completely blocked"}),G.jsx("button",{className:"btn btn-danger",onClick:()=>h(!0),children:"Add to Blacklist"})]}),e&&G.jsxs("div",{className:"list-stats",children:[G.jsxs("p",{children:["Current blacklist size: ",e.blacklistSize]}),G.jsxs("p",{children:["Blacklist hits: ",e.blacklistHits.toLocaleString()]})]})]})]})]}),u&&G.jsx("div",{className:"modal-overlay",children:G.jsxs("div",{className:"modal",children:[G.jsxs("div",{className:"modal-header",children:[G.jsx("h2",{children:"Add Client to Whitelist"}),G.jsx("button",{onClick:()=>d(!1),children:"×"})]}),G.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),p.trim()){l(null);try{await sh(p.trim()),d(!1),m(""),await w(),alert("Client added to whitelist successfully")}catch(t){l(t.message||"Failed to add client to whitelist")}}else l("Please enter a client ID")},className:"modal-form",children:[G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"whitelist-client-id",children:"Client ID (IP address or API key) *"}),G.jsx("input",{type:"text",id:"whitelist-client-id",value:p,onChange:e=>m(e.target.value),required:!0,placeholder:"e.g., ************* or api_key_123"})]}),G.jsxs("div",{className:"form-actions",children:[G.jsx("button",{type:"button",onClick:()=>d(!1),children:"Cancel"}),G.jsx("button",{type:"submit",className:"btn btn-success",children:"Add to Whitelist"})]})]})]})}),f&&G.jsx("div",{className:"modal-overlay",children:G.jsxs("div",{className:"modal",children:[G.jsxs("div",{className:"modal-header",children:[G.jsx("h2",{children:"Add Client to Blacklist"}),G.jsx("button",{onClick:()=>h(!1),children:"×"})]}),G.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),v.trim()&&y.trim()){l(null);try{const e=b?parseInt(b):void 0;await oh(v.trim(),y.trim(),e),h(!1),g(""),x(""),j(""),await w(),alert("Client added to blacklist successfully")}catch(t){l(t.message||"Failed to add client to blacklist")}}else l("Please enter both client ID and reason")},className:"modal-form",children:[G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"blacklist-client-id",children:"Client ID (IP address or API key) *"}),G.jsx("input",{type:"text",id:"blacklist-client-id",value:v,onChange:e=>g(e.target.value),required:!0,placeholder:"e.g., ************* or api_key_123"})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"blacklist-reason",children:"Reason *"}),G.jsx("input",{type:"text",id:"blacklist-reason",value:y,onChange:e=>x(e.target.value),required:!0,placeholder:"e.g., Abuse, Security violation, etc."})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"blacklist-duration",children:"Duration (seconds, optional)"}),G.jsx("input",{type:"number",id:"blacklist-duration",value:b,onChange:e=>j(e.target.value),placeholder:"Leave empty for permanent",min:"1"})]}),G.jsxs("div",{className:"form-actions",children:[G.jsx("button",{type:"button",onClick:()=>h(!1),children:"Cancel"}),G.jsx("button",{type:"submit",className:"btn btn-danger",children:"Add to Blacklist"})]})]})]})})]})},Eh=()=>{const[e,t]=U.useState("query"),[n,a]=U.useState(""),[r,l]=U.useState("database-service-ui"),[i,s]=U.useState(""),[o,c]=U.useState("public"),[u,d]=U.useState("{}"),[f,h]=U.useState(null),[p,m]=U.useState(null),[v,g]=U.useState(!1),[y,x]=U.useState([]),[b,j]=U.useState([""]);U.useEffect(()=>{(async()=>{try{const e=await Mf();x(e.databases),e.databases.length>0&&!i&&s(e.databases[0].name)}catch(e){console.error("Error loading databases:",e)}})()},[i]);return G.jsxs("div",{className:"query-execution-container",children:[G.jsx("h1",{children:"Query Execution"}),G.jsxs("div",{className:"query-form",children:[G.jsxs("div",{className:"form-row",children:[G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"queryType",children:"Operation Type:"}),G.jsxs("select",{id:"queryType",value:e,onChange:e=>t(e.target.value),children:[G.jsx("option",{value:"query",children:"SELECT Query"}),G.jsx("option",{value:"execute",children:"DML Statement (INSERT/UPDATE/DELETE)"}),G.jsx("option",{value:"transaction",children:"Transaction (Multiple Statements)"})]})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"database",children:"Database:"}),G.jsxs("select",{id:"database",value:i,onChange:e=>s(e.target.value),children:[G.jsx("option",{value:"",children:"Select Database"}),y.map(e=>G.jsx("option",{value:e.name,children:e.name},e.name))]})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"schema",children:"Schema:"}),G.jsx("input",{type:"text",id:"schema",value:o,onChange:e=>c(e.target.value),placeholder:"public"})]})]}),G.jsx("div",{className:"form-row",children:G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"application",children:"Application:"}),G.jsx("input",{type:"text",id:"application",value:r,onChange:e=>l(e.target.value),placeholder:"database-service-ui"})]})}),"transaction"!==e?G.jsxs("div",{className:"form-group",children:[G.jsxs("label",{htmlFor:"sqlText",children:["SQL ","query"===e?"Query":"Statement",":"]}),G.jsx("textarea",{id:"sqlText",value:n,onChange:e=>a(e.target.value),placeholder:"query"===e?"SELECT * FROM users WHERE id = $1":"INSERT INTO users (name, email) VALUES ($1, $2)",rows:8})]}):G.jsxs("div",{className:"form-group",children:[G.jsx("label",{children:"Transaction Statements:"}),b.map((e,t)=>G.jsxs("div",{className:"transaction-statement",children:[G.jsx("textarea",{value:e,onChange:e=>((e,t)=>{const n=[...b];n[e]=t,j(n)})(t,e.target.value),placeholder:`Statement ${t+1}`,rows:3}),G.jsx("div",{className:"statement-actions",children:G.jsx("button",{type:"button",onClick:()=>(e=>{if(b.length>1){const t=b.filter((t,n)=>n!==e);j(t)}})(t),disabled:1===b.length,className:"remove-statement",children:"Remove"})})]},t)),G.jsx("button",{type:"button",onClick:()=>{j([...b,""])},className:"add-statement",children:"Add Statement"})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"parameters",children:"Parameters (JSON):"}),G.jsx("textarea",{id:"parameters",value:u,onChange:e=>d(e.target.value),placeholder:'{"$1": "value1", "$2": "value2"}',rows:3})]}),G.jsx("div",{className:"form-actions",children:G.jsx("button",{onClick:async()=>{if(n.trim()){g(!0),m(null),h(null);try{let t={};if(u.trim()&&(t=JSON.parse(u)),"query"===e){const e={query:n,params:t,application:r,database:i,schema:o},a=await zf(e);h(a)}else if("execute"===e){const e={statement:n,params:t,application:r,database:i,schema:o},a=await Af(e);h(a)}else if("transaction"===e){const e={statements:b.filter(e=>e.trim()).map(e=>({statement:e,params:t})),application:r,database:i,schema:o},n=await Of(e);h(n)}}catch(t){m(t.message||"An error occurred while executing the query")}finally{g(!1)}}else m("Please enter a SQL query")},disabled:v||!i,className:"execute-button",children:v?"Executing...":`Execute ${e.charAt(0).toUpperCase()+e.slice(1)}`})})]}),p&&G.jsxs("div",{className:"error-message",children:[G.jsx("h3",{children:"Error"}),G.jsx("pre",{children:p})]}),(()=>{if(!f)return null;if("columns"in f){const e=f;return G.jsxs("div",{className:"query-results",children:[G.jsxs("div",{className:"results-header",children:[G.jsx("h3",{children:"Query Results"}),G.jsxs("div",{className:"results-meta",children:[G.jsxs("span",{children:["Rows: ",e.rowCount]}),G.jsxs("span",{children:["Execution Time: ",e.executionTime,"ms"]})]})]}),e.rows.length>0?G.jsx("div",{className:"results-table-container",children:G.jsxs("table",{className:"results-table",children:[G.jsx("thead",{children:G.jsx("tr",{children:e.columns.map((e,t)=>G.jsx("th",{children:e},t))})}),G.jsx("tbody",{children:e.rows.map((t,n)=>G.jsx("tr",{children:e.columns.map((e,n)=>G.jsx("td",{children:null!==t[e]?String(t[e]):"NULL"},n))},n))})]})}):G.jsx("div",{className:"no-results",children:"No rows returned"})]})}if("rowsAffected"in f){const e=f;return G.jsxs("div",{className:"execute-results",children:[G.jsxs("div",{className:"results-header",children:[G.jsx("h3",{children:"Execution Results"}),G.jsxs("div",{className:"results-meta",children:[G.jsxs("span",{children:["Rows Affected: ",e.rowsAffected]}),G.jsxs("span",{children:["Execution Time: ",e.executionTime,"ms"]})]})]}),G.jsx("div",{className:"execute-message",children:e.message})]})}{const e=f;return G.jsxs("div",{className:"transaction-results",children:[G.jsxs("div",{className:"results-header",children:[G.jsx("h3",{children:"Transaction Results"}),G.jsxs("div",{className:"results-meta",children:[G.jsxs("span",{children:["Status: ",e.success?"Success":"Failed"]}),G.jsxs("span",{children:["Total Time: ",e.totalExecutionTime,"ms"]})]})]}),G.jsx("div",{className:"transaction-message",children:e.message}),e.results&&G.jsx("div",{className:"statement-results",children:e.results.map((e,t)=>G.jsxs("div",{className:"statement-result",children:[G.jsxs("strong",{children:["Statement ",t+1,":"]})," ",e.rowsAffected," rows affected (",e.executionTime,"ms)"]},t))})]})}})()]})},Ph=()=>G.jsx("div",{className:"not-found-container",children:G.jsxs("div",{className:"not-found-content",children:[G.jsx("h1",{children:"404"}),G.jsx("h2",{children:"Page Not Found"}),G.jsx("p",{children:"The page you are looking for doesn't exist or has been moved."}),G.jsx(jf,{to:"/",className:"back-home-button",children:"Back to Home"})]})}),Th=({children:e})=>{const{isAuthenticated:t,isLoading:n}=fh();return n?G.jsx("div",{className:"loading-container",children:"Loading..."}):t?e:G.jsx(uf,{to:"/",replace:!0})},Lh=()=>{const{isAuthenticated:e}=fh();return G.jsxs("div",{className:"app-container",children:[G.jsx(mh,{}),G.jsx(hh,{}),G.jsx("main",{className:"main-content",children:G.jsxs(hf,{children:[G.jsx(df,{path:"/",element:e?G.jsx(uf,{to:"/dashboard",replace:!0}):G.jsx(xh,{})}),G.jsx(df,{path:"/dashboard",element:G.jsx(Th,{children:G.jsx(jh,{})})}),G.jsx(df,{path:"/credentials",element:G.jsx(Th,{children:G.jsx(wh,{})})}),G.jsx(df,{path:"/metrics",element:G.jsx(Th,{children:G.jsx(Sh,{})})}),G.jsx(df,{path:"/settings",element:G.jsx(Th,{children:G.jsx(kh,{})})}),G.jsx(df,{path:"/applications",element:G.jsx(Th,{children:G.jsx(Nh,{})})}),G.jsx(df,{path:"/audit-logs",element:G.jsx(Th,{children:G.jsx(_h,{})})}),G.jsx(df,{path:"/rate-limiting",element:G.jsx(Th,{children:G.jsx(Ch,{})})}),G.jsx(df,{path:"/query-execution",element:G.jsx(Th,{children:G.jsx(Eh,{})})}),G.jsx(df,{path:"/databases",element:G.jsx(Th,{children:G.jsx(yh,{})})}),G.jsx(df,{path:"/privacy",element:G.jsx(vh,{})}),G.jsx(df,{path:"/terms",element:G.jsx(gh,{})}),G.jsx(df,{path:"*",element:G.jsx(Ph,{})})]})}),G.jsx(ph,{})]})};function Rh(){return G.jsx(yf,{children:G.jsx(dh,{children:G.jsx(Lh,{})})})}J.createRoot(document.getElementById("root")).render(G.jsx($.StrictMode,{children:G.jsx(Rh,{})}));
